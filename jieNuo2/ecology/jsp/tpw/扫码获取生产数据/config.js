let config = [
    {
        workflowid: "802",
        items: [
            {
                fromType: "itemBarcode",
                toType: "pdh",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            },
            {
                fromType: "itemName",
                toType: "cpbmcdmjb",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            },
            {
                fromType: "partnerName",
                toType: "kh",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            },
            {
                fromType: "productType",
                toType: "qxblx",
                fieldType: "1", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            },
            {
                fromType: "instrumentQty",
                toType: "qxsb",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            }
        ]
    },
    {
        workflowid: "884",
        items: [
            {
                fromType: "itemBarcode",
                toType: "pdh",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            },
            {
                fromType: "itemName",
                toType: "cpbmcdmjb",
                fieldType: "0", //0:文本，1:"选择框"，2："浏览框"
                formTable: "", //浏览表单
                xsField: "", //浏览框显示字段
                linkField: "" //浏览表单关联字段名
            }
        ]
    }
];

ecodeSDK.setCom('${appId}', 'config', config);