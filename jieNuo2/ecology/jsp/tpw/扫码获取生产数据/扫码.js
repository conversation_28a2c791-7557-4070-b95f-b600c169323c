let runKeys = [] //控制一个key（页面url参数）执行次数
/**
 * 执行主方法
 * @param newProps 组件参数，可对其更改
 * @param params 主控的配置信息
 * @param 地址栏的_key参数
 */
const configs = ecodeSDK.getCom('${appId}', 'config')

let ssgc = ''
const execute = (newProps, params, urlKey) => {
    //刷新一次页面，执行一次
    if (runKeys.indexOf(urlKey) < 0 && WfForm.isMobile()) {
        runKeys.push(urlKey)
        console.log('扫码获取生产数据params',params)

        //绑定所属工厂值变化触发事件
        var ssgcFieldId = WfForm.convertFieldNameToId(params.ssgcField);
        var smFiledId = WfForm.convertFieldNameToId(params.smFiled,"detail_1");
        WfForm.bindFieldChangeEvent(ssgcFieldId, function (obj, id, value) {
            console.log("所属工厂触发")
            if (WfForm.getDetailAllRowIndexStr("detail_1").length>0 && ssgc != value) {
                WfForm.showConfirm("切换所属工厂会清空所有明细,确认要切换么？", function () {
                    ssgc = value
                    WfForm.delDetailRow("detail_1", "all");
                }, function () {
                    WfForm.changeFieldValue(ssgcFieldId, {value: ssgc});
                }, {
                    title: "信息确认",       //弹确认框的title，仅PC端有效
                    okText: "确认",          //自定义确认按钮名称
                    cancelText: "取消"     //自定义取消按钮名称
                });
            }
        });
        WfForm.registerAction(WfForm.ACTION_EDITDETAILROW + "1", function (index) {
            console.log("进入明细编辑触发", index)
            let count = 0;
            let interval = setInterval(() => {
                let smId =  smFiledId+"_"+ index
                console.log('smId',smId)
                let sm = $(`input[id="${smId}"][name="${smId}"][type="text"]`);
                console.log("扫码",sm)
                if (sm && sm.length > 0) {
                    console.log("要绑定扫码的元素")
                    //增加按钮
                    // 检查是否已经添加过按钮，避免重复添加
                    if ($('#custbutton').length === 0) {
                        // 创建扫码按钮
                        let custButton = $('<button id="custbutton" style="color: #fff; background-color: #2db7f5; border-color: #2db7f5; border-radius: 2px; ">扫码</button>');
                        // 在 #sbName 后面插入按钮
                        sm.after(custButton);
                        // 绑定按钮点击事件（示例：点击后打印日志）
                        custButton.on('click', function () {
                            //获取主表所属工厂字段值
                            console.log('扫码按钮被点击');
                            var ssgc = WfForm.getFieldValue(ssgcFieldId);
                            if (ssgc && ssgc.length>0) {
                                window.em.scanQRCode({
                                    scanType: ["qrCode", "barCode"],
                                    needResult: 1,
                                    success: function (res) {
                                        console.log('res', res);
                                        if(res.resultStr && res.resultStr.startsWith('TA')){
                                            WfForm.changeFieldValue(smFiledId+"_"+index, {value:res.resultStr});
                                            //调用登录接口,获取token
                                            let params = {};
                                            params.username = '<EMAIL>';
                                            params.password = '123456';
                                            params.factoryId = ssgc;
                                            params.serial =  res.resultStr;
                                            params.loginUrl = 'https://gkl.steriguardmed.net/login';
                                            params.prodFactoryUrl = 'https://gkl.steriguardmed.net/api/weaver/product/query';
                                            let loginResult = window.sdUtil.callOaApi('/api/sd/wf/getProdFactory', 'post', params)
                                            console.log('loginResult', loginResult)
                                            // let loginResult = {
                                            //             "data": "{\"partnerName\":\"上海中医药大学附属曙光医院（西院）\",\"factoryId\":5,\"itemBarcode\":\"PD100101500827001\",\"factoryName\":\"上海洁诺申梁消毒供应中心有限公司（浦东）\",\"clientCreateDate\":\"2025-04-08 00:32:40\",\"workflowName\":\"高温全流程\",\"deliveryPointName\":\"曙光西院手术室（住院部5楼）\",\"serviceReqName\":\"标准服务\",\"instrumentQty\":18,\"itemName\":\"A-V精细（18件）\",\"processName\":\"发货\",\"serial\":\"TA1000040008168562\",\"productType\":\"instrument\"}"
                                            //           }
                                            if (loginResult && loginResult.data && loginResult.data.length > 0) {
                                                //如果接口调用成功，回写数据到流程明细当中
                                                let product = JSON.parse(loginResult.data);

                                                let workflowid = wfform.getBaseInfo().workflowid
                                                if(configs && configs.length>0){
                                                    for (let i = 0; i < configs.length; i++) {
                                                        let config = configs[i]
                                                        if(config.workflowid == workflowid){
                                                            let items = config.items
                                                            for (let i = 0; i < items.length; i++) {
                                                                let item = items[i]
                                                                if(item.fieldType == 0){
                                                                    //文本
                                                                    WfForm.changeFieldValue(WfForm.convertFieldNameToId(item.toType,"detail_1")+"_"+index, {value:product[item.fromType]});
                                                                }else if(item.fieldType == 1){
                                                                    //选择框
                                                                    if(product[item.fromType] == 'instrument'){
                                                                        WfForm.changeFieldValue(WfForm.convertFieldNameToId(item.toType,"detail_1")+"_"+index, {value:0});
                                                                    }else if(product[item.fromType] == 'external'){
                                                                        WfForm.changeFieldValue(WfForm.convertFieldNameToId(item.toType,"detail_1")+"_"+index, {value:1});
                                                                    }else{
                                                                        let result = window.sdUtil.queryData("SELECT selectvalue  FROM workflow_selectitem WHERE fieldid = "+WfForm.convertFieldNameToId(item.toType,"detail_1",false)+" AND  selectname = '"+product[item.fromType]+"'")
                                                                        if(result && result.length>0){
                                                                            WfForm.changeFieldValue(WfForm.convertFieldNameToId(item.toType,"detail_1")+"_"+index, {value:result[0].selectvalue});
                                                                        }
                                                                    }
                                                                }else if(item.fieldType == 2){
                                                                    //浏览框
                                                                    let result = window.sdUtil.queryData("select * from "+item.formTable+" where "+item.linkField+" = "+product[item.fromType])
                                                                    if(result && result.length>0){
                                                                        WfForm.changeFieldValue(WfForm.convertFieldNameToId(item.toType,"detail_1")+"_"+index, {
                                                                            value: result[0].id,
                                                                            specialobj:[
                                                                                {id:result[0].id,name:result[0].xsField}
                                                                            ]
                                                                        });
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            } else {
                                                //如果接口调用失败，提示失败信息
                                                WfForm.showMessage("根据??ID与TA服务序号获取?产记录失败",2);
                                            }
                                        }else{
                                            WfForm.showMessage("服务序号格式错误",2);
                                        }

                                    }
                                });
                            } else {
                                //提示必须先填写所属工厂
                                WfForm.showMessage("请先填写所属工厂",2);
                            }
                        });
                    }
                    //绑定按钮事件
                    clearInterval(interval);
                } else {
                    if (count > 30) {
                        clearInterval(interval);
                    }
                    count++;
                }
            }, 100);

        });
    }
}

const getQueryParam = (param, url) => {
    const urlParams = new URLSearchParams(url.split('?')[1]);
    return urlParams.get(param);
}


//导出到window全局的sdWorkflowFunc对象中,固定语句
if (window.sdWorkflowFunc) {
    window.sdWorkflowFunc['${appId}'] = execute
} else {
    window.sdWorkflowFunc = {
        '${appId}': execute
    }
}
