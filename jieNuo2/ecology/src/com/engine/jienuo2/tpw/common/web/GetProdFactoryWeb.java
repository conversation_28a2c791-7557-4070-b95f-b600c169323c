package com.engine.jienuo2.tpw.common.web;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.jienuo2.tpw.common.service.GetProdFactoryService;
import com.engine.jienuo2.tpw.common.service.impl.GetProdFactoryServiceImpl;
import weaver.general.BaseBean;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class GetProdFactoryWeb extends BaseBean {
    private GetProdFactoryService getService(User user) {
        return ServiceUtil.getService(GetProdFactoryServiceImpl.class, user);
    }
    @POST
    @Path("/getProdFactory")
    @Produces({MediaType.TEXT_PLAIN})
    public String getProdFactory(@Context HttpServletRequest request, @Context HttpServletResponse response){
        Map<String, Object> result = new HashMap<>();
        //获取当前用户
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ParamUtil.request2Map(request);
        if (user != null) {
            result = getService(user).getProdFactory(params, user);
        } else {
            result.put("status", false);
            result.put("errorMsg", "user info error");
        }
        return JSONObject.toJSONString(result);
    }
}
