package com.engine.jienuo2.tpw.common.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

public class GetProdFactoryCmd extends AbstractCommonCommand<Map<String, Object>> {
    private final BaseBean bb;

    public GetProdFactoryCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        bb.writeLog("params:" + params);
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {
            bb.writeLog("GetProdFactoryCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            if (params != null && !params.isEmpty()) {
                String data = getProdFactory(params);
                if(StringUtils.isNotBlank(data)){
                    result.put("data", data);
                }
            } else {
                errorMsg = "请求参数为空";
                result.put("errorMsg", errorMsg);
            }
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("GetProdFactoryCmd 异常：" + errorMsg);
        }
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }

    private String getProdFactory(Map<String, Object> params) {
        String data = "";
        try {
            String username = Util.null2String(params.get("username"));
            String password = Util.null2String(params.get("password"));
            String factoryId = Util.null2String(params.get("factoryId"));
            String serial = Util.null2String(params.get("serial"));
            String loginUrl = Util.null2String(params.get("loginUrl"));
            String prodFactoryUrl = Util.null2String(params.get("prodFactoryUrl"));

            String token = "";
            //1.首先调用登录接口，获取token
            String tokenResult = getToken(loginUrl, username, password);
            if(StringUtils.isNotBlank(tokenResult)){
                JSONObject tokenObj = JSONObject.parseObject(tokenResult);
                String status = Util.null2String(tokenObj.get("status"));
                String tokenData = Util.null2String(tokenObj.get("data"));
                if("200".equals(status) && StringUtils.isNotBlank(tokenData)){
                    token = tokenData;
                }
            }
            if (StringUtils.isBlank(token)) {
                bb.writeLog("getProdFactory token is null");
            } else {
                HashMap<String, String> headers = new HashMap<>();
                headers = new HashMap<>();
                headers.put("Authorization", token);
                JSONObject factoryObj = new JSONObject();
                factoryObj.put("factoryId", factoryId);
                factoryObj.put("serial", serial);
                RestResult restResult = HttpUtil.postDataWithHeader(prodFactoryUrl, headers, JSONObject.toJSONString(factoryObj));
                bb.writeLog("restResult:"+JSONObject.toJSONString(restResult));
                if (restResult.isSuccess()) {
                    RestResult.ResponseInfo responseInfo = restResult.getResponseInfo();
                    String body = responseInfo.getBody();
                    if (StringUtils.isNotBlank(body)) {
                        JSONObject bodyObject = JSONObject.parseObject(body);
                        JSONObject dataObj = (JSONObject) bodyObject.get("data");
                        body = JSONObject.toJSONString(dataObj);
                    }
                    return body;
                }
            }
        } catch (Exception e) {
            bb.writeLog("SyncFactoryMasterDataJob  getFactoryMasterData exception: " + SDUtil.getExceptionDetail(e));
        }
        return data;
    }
    public String getToken(String url ,String username,String password) throws IOException {

        // 构建表单数据
        String formData = "username=" + username + "&password=" + password;
        byte[] postData = formData.getBytes(StandardCharsets.UTF_8);

        // 创建连接
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setDoOutput(true);

        // 发送请求
        try (OutputStream os = conn.getOutputStream()) {
            os.write(postData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 读取响应
        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            }
        } else {

        }
        return "";
    }
}
