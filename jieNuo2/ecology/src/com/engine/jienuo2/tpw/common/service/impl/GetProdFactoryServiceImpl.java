package com.engine.jienuo2.tpw.common.service.impl;

import com.engine.core.impl.Service;
import com.engine.jienuo2.tpw.common.cmd.GetProdFactoryCmd;
import com.engine.jienuo2.tpw.common.service.GetProdFactoryService;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class GetProdFactoryServiceImpl extends Service implements GetProdFactoryService {
    @Override
    public Map<String, Object> getProdFactory(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetProdFactoryCmd(params, user));
    }
}

