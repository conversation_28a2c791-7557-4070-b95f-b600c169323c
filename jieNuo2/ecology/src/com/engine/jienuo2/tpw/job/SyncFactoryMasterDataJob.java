package com.engine.jienuo2.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.jienuo2.tpw.job.bean.FactoryBean;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import okhttp3.MediaType;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.ProtocolException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @FileName aa SyncFactoryMasterDataJob 工厂主数据定时同步
 * @Description
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/5/6
 */
@Getter
@Setter
public class SyncFactoryMasterDataJob extends BaseCronJob {
    private BaseBean bb = new BaseBean();
    private String loginUrl;
    private String factoryUrl;
    private String username;
    private String password;
    //感控链工厂信息 这里是一个map对象，key对应工厂id,value对应数据id
    private Map<String, String> oaFactoryMap;
    private JSONArray factories;

    @Override
    public void execute() {
        bb.writeLog("SyncFactoryMasterDataJob Start ");
        String errMsg = "";
        try {
            if (StringUtils.isBlank(loginUrl) || StringUtils.isBlank(username) || StringUtils.isBlank(factoryUrl) || StringUtils.isBlank(password)) {
                bb.writeLog("SyncFactoryMasterDataJob loginUrl or factoryUrl or username or password is null");
                return;
            }
            //初始化参数
            _init();
            //重置参数
            resetParam();
            //获取工厂主数据
            getFactoryMasterData();
            //同步
            syncFactory();
        } catch (Exception e) {
            bb.writeLog("SyncFactoryMasterDataJob exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        bb.writeLog("SyncFactoryMasterDataJob End ");
    }


    private void _init() {
        oaFactoryMap = new HashMap<String, String>();
        factories = new JSONArray();
    }

    private void resetParam() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            //1.获取洁诺医疗-感控链工厂信息（uf_jngklgc）所有信息
            recordSet.executeQuery("select * from uf_jngklgc");
            while (recordSet.next()) {
                //数据id
                String id = recordSet.getString("id");
                //工厂id
                String factoryId = recordSet.getString("gcid");
                oaFactoryMap.put(factoryId, id);
            }
        } catch (Exception e) {
            bb.writeLog("SyncFactoryMasterDataJob resetParam exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }

    }

    private void getFactoryMasterData() {
        try {
            //1.首先调用登录接口，获取token
            String token = "";
            String tokenResult = getToken();
            bb.writeLog("SyncFactoryMasterDataJob getFactoryMasterData token " + tokenResult);
            if(StringUtils.isNotBlank(tokenResult)){
                JSONObject tokenObj = JSONObject.parseObject(tokenResult);
                String status = Util.null2String(tokenObj.get("status"));
                String data = Util.null2String(tokenObj.get("data"));
                if("200".equals(status) && StringUtils.isNotBlank(data)){
                    token = data;
                }
            }
            if(StringUtils.isNotBlank(token)){
                HashMap<String, String> headers = new HashMap<>();
                headers = new HashMap<>();
                headers.put("Authorization", token);
                JSONObject factoryObj = new JSONObject();
                RestResult restResult = HttpUtil.postDataWithHeader(factoryUrl, headers, JSONObject.toJSONString(factoryObj));
                bb.writeLog("SyncFactoryMasterDataJob getFactoryMasterData restResult " + JSONObject.toJSONString(restResult));
                if (restResult.isSuccess()) {
                    RestResult.ResponseInfo responseInfo = restResult.getResponseInfo();
                    String body = responseInfo.getBody();
                    bb.writeLog("SyncFactoryMasterDataJob getFactoryMasterData body " + body);
                    if (StringUtils.isNotBlank(body)) {
                        JSONObject bodyObject = JSONObject.parseObject(body);
                        factories = (JSONArray) bodyObject.get("data");
                    }
                }
                bb.writeLog("SyncFactoryMasterDataJob getFactoryMasterData factories "+JSONObject.toJSONString(factories));
            }else {
                bb.writeLog("SyncFactoryMasterDataJob getFactoryMasterData token is null");
            }
        } catch (Exception e) {
            bb.writeLog("SyncFactoryMasterDataJob  getFactoryMasterData exception: " + SDUtil.getExceptionDetail(e));
        }
    }


    private void syncFactory() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            if (!factories.isEmpty()) {
                ArrayList<FactoryBean> factoryBeans = new ArrayList<FactoryBean>();
                for (int i = 0; i < factories.size(); i++) {
                    JSONObject factory = (JSONObject) factories.get(i);
                    String factoryId = Util.null2String(factory.get("factoryId"));
                    String factoryIdName = Util.null2String(factory.get("factoryIdName"));
                    if (oaFactoryMap.containsKey(factoryId)) {
                        //更新操作
                        recordSet.executeUpdate("update uf_jngklgc set gcmc = '" + factoryIdName + "' where gcid = " + factoryId);
                    } else {
                        //插入操作
                        FactoryBean factoryBean = new FactoryBean();
                        factoryBean.setGcid(factoryId);
                        factoryBean.setGcmc(factoryIdName);
                        factoryBeans.add(factoryBean);
                    }
                }
                if (!factoryBeans.isEmpty()) {
                    // 获取rows的长度
                    int totalRows = factoryBeans.size();
                    // 定义每次截取的大小
                    int batchSize = 1000;
                    // 开始截取数据
                    for (int i = 0; i < totalRows; i += batchSize) {
                        int endIndex = Math.min(i + batchSize, totalRows); // 确定本次截取的结束索引
                        List<FactoryBean> beans = factoryBeans.subList(i, endIndex);
                        ModuleResult mr = ModuleDataUtil.insertObjList(beans, "uf_jngklgc", ModuleDataUtil.getModuleIdByName("uf_jngklgc"), 1);
                        bb.writeLog("SyncFactoryMasterDataJob syncFactoryMasterData job start " + i + " end " + endIndex);
                        bb.writeLog("SyncFactoryMasterDataJob beans " + JSONObject.toJSONString(beans));
                        bb.writeLog("SyncFactoryMasterDataJob mr " + JSONObject.toJSONString(mr));
                    }
                }
            } else {
                bb.writeLog("SyncFactoryMasterDataJob syncFactory factories is null");
            }
        } catch (Exception e) {
            bb.writeLog("SyncFactoryMasterDataJob syncFactory exception: " + SDUtil.getExceptionDetail(e));
        }
    }


    public String getToken() throws IOException {
        // 请求URL和参数
        String url = loginUrl;
        // 构建表单数据
        String formData = "username=" + username + "&password=" + password;
        byte[] postData = formData.getBytes(StandardCharsets.UTF_8);

        // 创建连接
        HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setDoOutput(true);

        // 发送请求
        try (OutputStream os = conn.getOutputStream()) {
            os.write(postData);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        // 读取响应
        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = br.readLine()) != null) {
                    response.append(line);
                }
                return response.toString();
            }
        } else {

        }
        return "";
    }


}
