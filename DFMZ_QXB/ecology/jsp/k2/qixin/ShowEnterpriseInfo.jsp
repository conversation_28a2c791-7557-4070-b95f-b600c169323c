<%@ page import="weaver.general.Util" %>
<%@ page import="weaver.hrm.User" %>
<%@ page import="weaver.hrm.HrmUserVarify" %>
<%@ page import="weaver.hrm.OnLineMonitor" %>
<%@ page import="java.util.Map" %>
<%@ page import="java.util.HashMap" %>
<jsp:useBean id="rs" class="weaver.conn.RecordSet" scope="page"/>
<jsp:useBean id="log" class="weaver.general.BaseBean" scope="page" />

<%
    String etpFullName = Util.null2String(request.getParameter("EtpFullName"));
    // TODO: 记录访问日志
    String inquirer = Util.null2String(request.getParameter("Inquirer"));
    String loginid  = log.getPropValue("qixin", "loginid");

    User user = HrmUserVarify.getUser(request, response);
    if (user == null) {
        rs.execute("select * from HrmResource where id = " + loginid);
        if(rs.next()){
            user = new User();
            user.setUid(rs.getInt("id"));
            user.setLoginid(rs.getString("loginid"));
            user.setFirstname(rs.getString("firstname"));
            user.setLastname(rs.getString("lastname"));
            user.setAliasname(rs.getString("aliasname"));
            user.setTitle(rs.getString("title"));
            user.setTitlelocation(rs.getString("titlelocation"));
            user.setSex(rs.getString("sex"));
            user.setPwd(rs.getString("password"));
            String languageidweaver = rs.getString("systemlanguage");
            user.setLanguage(Util.getIntValue(languageidweaver, 0));

            user.setTelephone(rs.getString("telephone"));
            user.setMobile(rs.getString("mobile"));
            user.setMobilecall(rs.getString("mobilecall"));
            user.setEmail(rs.getString("email"));
            user.setCountryid(rs.getString("countryid"));
            user.setLocationid(rs.getString("locationid"));
            user.setResourcetype(rs.getString("resourcetype"));
            user.setStartdate(rs.getString("startdate"));
            user.setEnddate(rs.getString("enddate"));
            user.setContractdate(rs.getString("contractdate"));
            user.setJobtitle(rs.getString("jobtitle"));
            user.setJobgroup(rs.getString("jobgroup"));
            user.setJobactivity(rs.getString("jobactivity"));
            user.setJoblevel(rs.getString("joblevel"));
            user.setSeclevel(rs.getString("seclevel"));
            user.setUserDepartment(Util.getIntValue(rs.getString("departmentid"), 0));
            user.setUserSubCompany1(Util.getIntValue(rs.getString("subcompanyid1"), 0));
            user.setUserSubCompany2(Util.getIntValue(rs.getString("subcompanyid2"), 0));
            user.setUserSubCompany3(Util.getIntValue(rs.getString("subcompanyid3"), 0));
            user.setUserSubCompany4(Util.getIntValue(rs.getString("subcompanyid4"), 0));
            user.setManagerid(rs.getString("managerid"));
            user.setAssistantid(rs.getString("assistantid"));
            user.setPurchaselimit(rs.getString("purchaselimit"));
            user.setCurrencyid(rs.getString("currencyid"));
            user.setLastlogindate(rs.getString("currentdate"));
            user.setLogintype("1");
            user.setAccount(rs.getString("account"));

            user.setLoginip(request.getRemoteAddr());
            request.getSession(true).setMaxInactiveInterval(60 * 60 * 24);
            request.getSession(true).setAttribute("weaver_user@bean", user);
            request.getSession(true).setAttribute("moniter", new OnLineMonitor("" + user.getUID(), user.getLoginip()));
            Util.setCookie(response, "loginfileweaver", "/main.jsp", 172800);
            Util.setCookie(response, "loginidweaver", "" + user.getUID(), 172800);
            Util.setCookie(response, "languageidweaver", languageidweaver, 172800);

            Map logmessages = (Map) application.getAttribute("logmessages");
            if (logmessages == null) {
                logmessages = new HashMap();
                logmessages.put("" + user.getUID(), "");
                application.setAttribute("logmessages", logmessages);
            }
        }
    }
    String link = "/spa/crm/static/index.html#/main/crm/api/APIShowGroup/crmGroup?EtpFullName=" + etpFullName + "&Inquirer=" + inquirer;
%>

<html style="overflow: hidden">
<head>
    <title><%=etpFullName %></title>
</head>
<body>
<iframe id="nav" src="<%=link%>" frameborder="0" width="100%" height="100%"></iframe>
</body>
</html>