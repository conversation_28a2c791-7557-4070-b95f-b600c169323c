package k2.qixin;

import org.junit.Test;
import weaver.toolbox.http.ec.RestfulHelper;

import java.util.HashMap;
import java.util.Map;

/**
 * 功能说明
 *
 * <AUTHOR>
 * @create 2022-12-23 02:26
 */
public class AccessUrlApiTest {
    @Test
    public void test(){
        RestfulHelper helper = new RestfulHelper("ecapi123456789","http://ec9.weaverfather.com");
        Map<String, Object> params = new HashMap<>();
        params.put("etpFullName", "北京启信宝科技有限公司");
        params.put("inquirer", "金城武");
        params.put("accessToken", "e82946b8-4adc-4554-84f2-fc5663e0b51b");
        String res = helper.doPost("/api/ec9/qixin/access/url", params);
        System.out.println("111");
        System.out.println(res);
        System.out.println("222");
    }
}
