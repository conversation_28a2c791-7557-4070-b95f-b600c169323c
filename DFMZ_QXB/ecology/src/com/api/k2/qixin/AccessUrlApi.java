package com.api.k2.qixin;

import com.api.ec9.base.AbstractController;
import com.api.ec9.base.entity.ApiResult;
import com.api.ec9.base.enums.ApiCodeMsgEnum;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.core.date.DateUtil;
import weaver.toolbox.core.util.StringUtils;
import weaver.toolbox.db.Entity;
import weaver.toolbox.db.recordset.ExecuteUtil;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.http.HttpRequest;
import weaver.toolbox.json.JSONObject;
import weaver.toolbox.log.LogUtil;
import weaver.toolbox.modeform.ModeFormUtil;
import weaver.toolbox.setting.PropUtils;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.*;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;

/**
 * 功能说明 根据token获取ec访问启信宝企业信息的链接，并且链接在访问一次后失效
 *
 * <AUTHOR>
 * @create 2022-12-14 00:08
 */
@Path("/ec9/qixin")
public class AccessUrlApi extends AbstractController {
    private static final LogUtil log = new LogUtil(AccessUrlApi.class);

    /**
     * 获取企业信息访问链接
     *
     * @param etpFullName 企业全称
     * @param inquirer    查询人
     * @param request     servlet请求上下文
     * @return 访问链接
     */
    @Path("/access/url")
    @POST
    @Produces({MediaType.APPLICATION_JSON})
    public String getAccessUrl(@FormParam(value = "etpFullName") String etpFullName,
                               @FormParam(value = "inquirer") String inquirer,
                               @FormParam(value = "accessToken") String accessToken,
                               @Context HttpServletRequest request) {
        return service(() -> {
            log.writeLog("getAccessUrl---START");
            log.writeLog("etpFullName:" + etpFullName);
            log.writeLog("inquirer:" + inquirer);
            log.writeLog("accessToken:" + accessToken);
            if (StringUtils.isBlank(etpFullName) || StringUtils.isBlank(inquirer) || StringUtils.isBlank(accessToken)) {
                return ApiResult.fail(ApiCodeMsgEnum.PARAMETER_INVALID, "请求参数错误");
            }
            // 用accessToken获取ssoToken
            String loginId = PropUtils.getPropValue("qixin", "loginid");
            String oaAddress = PropUtils.getPropValue("qixin", "oaAddress");
            String ssoTokenUrl = String.format("%s/ssologin/getToken?appid=%s&loginid=%s", oaAddress, accessToken, loginId);
            log.writeLog("ssoTokenUrl:" + ssoTokenUrl);
            String ssoToken = HttpRequest.get(ssoTokenUrl).execute().body();

            log.writeLog("Qixin AccessUrlApi getAccessUrl ssoToken:", ssoToken);

            // 建模表记录下ssoToken，只能访问一次
            Entity data = new Entity();
            data.setTableName("uf_k2qxb_token");
            data.set("token", ssoToken);
            data.set("etpfullname", etpFullName);
            data.set("inquirer", inquirer);
            data.set("createtime", DateUtil.now());
            data.set("status", "0");
            String formModeId = QueryUtil.getFormmodeidByTablename("uf_k2qxb_token");
            int dataId = ModeFormUtil.insertModeData(data, Convert.toInt(formModeId), 1);
            log.writeLog("Qixin AccessUrlApi getAccessUrl dataId:", dataId);

            String accessUrl = String.format("%s/spa/crm/static/index.html?ssoToken=%s#/main/crm/api/APIShowGroup/crmGroup?EtpFullName=%s&Inquirer=%s", oaAddress, ssoToken, etpFullName, inquirer);
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("accessUrl", accessUrl);
            return ApiResult.success(jsonObject);
        }).handleException(e -> {
            log.writeLog("Qixin AccessUrlApi getAccessUrl exception:", e);
            return ApiResult.fail("操作异常: " + e.getMessage());
        });
    }

    /**
     * 获取异构系统查询人，并且使令牌失效
     *
     * @param ssoToken 令牌
     * @return 查询人
     */
    @Path("/inquirer")
    @POST
    @Produces({MediaType.APPLICATION_JSON})
    public String getInquirer(@QueryParam("ssoToken") String ssoToken,
                              @Context HttpServletRequest request) {
        return service(() -> {
            if (StringUtils.isBlank(ssoToken)) {
                return ApiResult.fail(ApiCodeMsgEnum.PARAMETER_INVALID, "请求参数错误");
            }

            // 根据ssoToken获取查询人
            String qry = "select inquirer from uf_k2qxb_token where token = ? and status = 0";
            String inquirer = QueryUtil.doQueryFieldValueWithArgs(qry, "inquirer", ssoToken);
            log.writeLog("Qixin AccessUrlApi getInquirer inquirer:", inquirer);

            if (StringUtils.isBlank(inquirer)) {
                // 重新生成
                String loginId = PropUtils.getPropValue("qixin", "loginid");
                String oaAddress = PropUtils.getPropValue("qixin", "oaAddress");
                String accessToken = PropUtils.getPropValue("qixin", "accessToken");
                String ssoTokenUrl = String.format("%s/ssologin/getToken?appid=%s&loginid=%s", oaAddress, accessToken, loginId);
                HttpRequest.get(ssoTokenUrl).execute().body();
            }

            // 使令牌失效
            String sql = "update uf_k2qxb_token set status = 1, updatetime = ? where token = ?";
            ExecuteUtil.executeUpdateSql(sql, DateUtil.now(), ssoToken);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("inquirer", inquirer);
            return ApiResult.success(jsonObject);
        }).handleException(e -> {
            log.writeLog("Qixin AccessUrlApi getInquirer exception:", e);
            return ApiResult.fail("操作异常: " + e.getMessage());
        });
    }
}
