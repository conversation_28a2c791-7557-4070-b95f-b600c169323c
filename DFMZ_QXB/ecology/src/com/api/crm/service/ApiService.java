package com.api.crm.service;

import com.api.browser.bean.SearchConditionOption;
import com.api.crm.util.CrmConstant;
import com.api.crm.util.CrmFormItemUtil;
import com.api.crm.util.PageUidFactory;
import com.cloudstore.dev.api.util.Util_TableMap;
import net.sf.json.JSONArray;
import net.sf.json.JSONException;
import net.sf.json.JSONObject;
import weaver.conn.ConnStatement;
import weaver.conn.RecordSet;
import weaver.general.PageIdConst;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;
import weaver.secondary.util.TransUtil;
import weaver.systeminfo.SystemEnv;
import weaver.toolbox.core.convert.Convert;
import weaver.toolbox.db.Entity;
import weaver.toolbox.db.recordset.QueryUtil;
import weaver.toolbox.modeform.ModeFormUtil;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.io.Writer;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;

public class ApiService extends BaseService {
    Map<String, Object> sublist = new HashMap<String, Object>();
    //    Map<String, Object> jsonlist = new HashMap<String, Object>();
    String isList = "0";
    public static final String front_path = weaver.general.GCONST.getContextPath() + "/spa/crm/static/index.html#/main/crm/api/APISingleShow";
    public static final String front_path1 = weaver.general.GCONST.getContextPath() + "/spa/crm/static/index.html#/main/crm/api/APIShow";
    public static final String front_path2 = weaver.general.GCONST.getContextPath() + "/spa/crm/static/index.html#/main/crm/api/APIShowGroup";

    /**
     * 请求接口数据，并返回接口处理的结果
     *
     * @param user
     * @param params
     * @param request
     * @return
     * @throws Exception
     */
    public Map<String, Object> getCondition(User user, Map<String, Object> params, HttpServletRequest request) throws Exception {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        String apiId = Util.null2String(params.get("apiId"));
        isList = Util.null2String(params.get("isList"));//是否为纯列表接口
        String skip = Util.null2String(params.get("skip"));
        Base64.Decoder decoder = Base64.getDecoder();
        String tempQuery = URLDecoder.decode(Util.null2String(params.get("query")), "UTF-8");
        loggerBean.writeLog("tempQuery:" + tempQuery);

        String tempQuery2 = tempQuery.replace("＂", "\"");
        JSONObject jsonObject = JSONObject.fromObject(tempQuery2);
        String inquirer = Util.null2String(jsonObject.get("Inquirer"));


        Map<String, Object> query = unitQuerys(apiId, tempQuery.replace("＂", "\""), skip);
        String urlQueryBeforeEncode = (String) query.get("urlQueryBeforeEncode");
        if (!urlQueryBeforeEncode.endsWith("&")) {
            urlQueryBeforeEncode += "&";
        }
        urlQueryBeforeEncode += "Inquirer=" + inquirer + "&";
        loggerBean.writeLog("urlQueryBeforeEncode:" + urlQueryBeforeEncode);
        apiResult.putAll(getCondition(apiId, (String) query.get("urlQuery"), urlQueryBeforeEncode, user, request));

        String callType = Util.null2String(apiResult.get("callType"));
        String callResult = Util.null2String(apiResult.get(CrmConstant.CRM_RESULT_MESSAGECODE));
        String ipAddress = request.getRemoteAddr();
//        tempQuery = tempQuery.replace("＂", "\"");
//        JSONObject jsonObject = JSONObject.fromObject(tempQuery);
//        String inquirer = Util.null2String(jsonObject.get("Inquirer"));
        loggerBean.writeLog("apiId:" + apiId + ",callType:" + callType + ",callResult:" + callResult + ",ipAddress:" + ipAddress + ",inquirer:" + inquirer);
        k2WriteLog(callType, apiId, urlQueryBeforeEncode, callResult, ipAddress, inquirer);

        return apiResult;
    }

    public Map<String, Object> getCondition(String apiId, String query, String urlQueryBeforeEncode, User user, HttpServletRequest request) {
        //定义日志变量
        String operator = Util.null2String(user.getUID());//操作者
        String callType = "";//调用类型，1 缓存；0 新请求
        String callResult = "";//请求结果
        String callTime = TimeUtil.getOnlyCurrentTimeString();//时间
        String callDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());//日期
        Map<String, Object> apiResult = new HashMap<String, Object>();
        if (query.endsWith("&")) {
            query = query.substring(0, query.length() - 1);
        }
        if (urlQueryBeforeEncode.endsWith("&")) {
            urlQueryBeforeEncode = urlQueryBeforeEncode.substring(0, urlQueryBeforeEncode.length() - 1);
        }
        RecordSet rs = new RecordSet();
        //获取appKey
        String sql = "select * from crm_busniessinfosettings";
        rs.executeQuery(sql);
        rs.first();
        String ApiAppkey = rs.getString("appkey");
        if (ApiAppkey.equals("")) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, "appkey" + SystemEnv.getHtmlLabelName(504380, user.getLanguage()));
            loggerBean.writeLog("appkey" + SystemEnv.getHtmlLabelName(504380, user.getLanguage()));
            return apiResult;
        }
        //拿到其他参数
        sql = "select * from mobilemode_api where id=?";
        rs.executeQuery(sql, apiId);
        rs.first();
        String apiServerAddr = rs.getString("server_addr");//接口地址
        String apiServerPath = rs.getString("server_path");//接口路径
        //获取请求路径
        String url = apiServerAddr + apiServerPath + "?appkey=" + ApiAppkey + query;//拼接Url
        String isopen = rs.getString("is_enabled");//查询该接口是否启用
        String catchDay = Util.null2s(rs.getString("server_cachetime"), "0");//查询缓存及缓存时间

        if (isopen.equals("1")) {//如果接口启用
            String result = null;
            String modifydate = null;
            String modifytime = null;
//            String dataAfterDeal = null;
            boolean needRequest = false;
            String catchSql = null;
            RecordSet rsCatch = new RecordSet();
            catchSql = "select * from crm_busniessinfoeache where path=? and query=?";
            rsCatch.executeQuery(catchSql, apiServerAddr + apiServerPath, query);
            if (rsCatch.first()) {
                modifydate = rsCatch.getString("modifydate");
                modifytime = rsCatch.getString("modifytime");
                TimeUtil timeUtil = new TimeUtil();
                double time = (double) timeUtil.timeInterval(modifydate + " " + modifytime, callDate + " " + callTime) / 86400;
                //如果缓存天数已经超过时间，则重新请求
                if (time >= Double.parseDouble(catchDay)) {
                    needRequest = true;
                }
            } else {
                needRequest = true;
            }
            if (needRequest) {
                try {
                    //请求数据
                    TransUtil transUtil = new TransUtil();
                    result = transUtil.sendPost(url, "")
                            .replaceAll("\\\\r\\\\n", "")
                            .replaceAll("\\\\n", "")
                            .replaceAll("\\\\r", "");
                    callType = "0";
                    apiResult.put("updata_date", callDate);
                    apiResult.put("updata_time", callTime);
                } catch (Exception e) {
                    apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(506119, user.getLanguage()));
                    loggerBean.writeLog(SystemEnv.getHtmlLabelName(506119, user.getLanguage()));
                    e.printStackTrace();
                    return apiResult;
                }
            } else {
                result = rsCatch.getString("data");
                callType = "1";
                apiResult.put("updata_date", rsCatch.getString("modifydate"));
                apiResult.put("updata_time", rsCatch.getString("modifytime"));
            }
            apiResult.put("callType", callType);
            apiResult.putAll(this.analysisApi(result, apiId, user));//获取处理后的数据
            apiResult.put("json", result);
            apiResult.put("name", "name");
            //如果数据为有效数据，则保存数据缓存
            if (apiResult.get(CrmConstant.CRM_RESULT_STATUS).equals("success")) {
                if (needRequest) {
                    //写入缓存
                    writeCache(query, user, result, apiServerAddr, apiServerPath);
                }
            }
            callResult = Util.null2String(apiResult.get(CrmConstant.CRM_RESULT_MESSAGECODE));
            //写入日志
            String ipAddress = request.getRemoteAddr();
            writeLog(callType, apiId, urlQueryBeforeEncode, operator, callResult, ipAddress);
        } else {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(506122, user.getLanguage()));
            loggerBean.writeLog(SystemEnv.getHtmlLabelName(506122, user.getLanguage()));
        }
        return apiResult;
    }

    /**
     * 将拿到的json数据解析成系统组件
     *
     * @param result
     * @param apiId
     * @return
     */
    public Map<String, Object> analysisApi(String result, String apiId, User user) {
        List tableList = new ArrayList();
        Map<String, Object> data = new HashMap<String, Object>();
        Map<String, Object> datas = new HashMap<String, Object>();
        RecordSet rs = new RecordSet();
        try {
            String code = ""; //拿到该接口的错误码标志
            String messageCode = "";//拿到该接口的错误信息标志
            String successCode = "";//拿到该接口的成功码
            String dataCode = ""; //拿到该接口的数据标志
            String sql = "select * from mobilemode_api_result_define where api_id='" + apiId + "' and alias_name in ( 'status','data','message')";
            rs.executeQuery(sql);
            while (rs.next()) {
                String rsData = rs.getString("alias_name").trim();
                if (rsData.equals("data")) {
                    dataCode = rs.getString("name").trim();
                }
                if (rsData.equals("message")) {
                    messageCode = rs.getString("name").trim();
                }
                if (rsData.equals("status")) {
                    code = rs.getString("name").trim();
                    String value_mapping = rs.getString("value_mapping");
                    JSONArray array = JSONArray.fromObject(value_mapping);
                    JSONObject tempCode = JSONObject.fromObject(array.get(0));
                    successCode = tempCode.getString("init");
                }
            }
            //这里开始解析数据
            //判断是否是成功数据
            JSONObject tempJSON = JSONObject.fromObject(result);
            String tempStatus = tempJSON.getString(code);
            if (tempStatus.equals(successCode)) {
                RecordSet ts = new RecordSet();
                String resuleDefineSql;
                Object tempData = tempJSON.get(dataCode);
                if (tempData instanceof List) {
                    resuleDefineSql = "select * from mobilemode_api_result_define where api_id=? and name='" + dataCode + "'";
                    ts.executeQuery(resuleDefineSql, apiId);
                    ts.first();
                    Map<String, Object> datas1 = getTableCondition(((JSONArray) tempData).toString(), ts.getString("name"), apiId, ts.getString("remark"));
                    tableList.add(datas1);
                    data.put("tableList", tableList);
                } else if (tempData instanceof JSONObject) {
                    data = getJSONCondition(dataCode, tempData, apiId);
                }
                data.put("sublist", sublist);//子列表
//                data.put("jsonList",jsonlist);//子json串
                datas.put(CrmConstant.CRM_RESULT_STATUS, "success");
                datas.put(CrmConstant.CRM_RESULT_MESSAGECODE, tempJSON.getString(messageCode));
                loggerBean.writeLog(tempJSON.getString(messageCode));
                datas.put("datas", data);
            } else {
                datas.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                datas.put(CrmConstant.CRM_RESULT_MESSAGECODE, tempJSON.getString(messageCode));
                datas.put("code", tempJSON.getString("status"));
                loggerBean.writeLog(tempJSON.getString(messageCode));
            }
        } catch (Exception e) {
            datas.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            datas.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(384850, user.getLanguage()));
            loggerBean.writeLog(SystemEnv.getHtmlLabelName(384850, user.getLanguage()));
            e.printStackTrace();
        }
        return datas;
    }

    /**
     * 多接口页面-菜单
     *
     * @param user
     * @param params
     * @return
     * @throws Exception
     */
    public Map<String, Object> getMenu(User user, Map<String, Object> params) throws Exception {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        String module = Util.null2String(params.get("module"));
        RecordSet rs = new RecordSet();
        rs.executeQuery("select * from crm_enterprise_api where flag=?", module);
        if (rs.getCounts() <= 0) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, "" + weaver.systeminfo.SystemEnv.getHtmlLabelName(10004417, weaver.general.ThreadVarLanguage.getLang()) + "");
            return apiResult;
        }
        try {
            String sql = "select * from mobilemode_api_group";
            rs.executeQuery(sql);
            RecordSet rsItem = new RecordSet();
            //分组
            List groupList = new ArrayList();
            while (rs.next()) {
                Map<String, Object> group = new HashMap<String, Object>();
                List itemList = new ArrayList();
                String sqlItem = "select a.id id,a.name,a.front_path front_path,b.isdefault isdefault from mobilemode_api a,crm_enterprise_api b where a.id=b.api_id and b.flag='" + module + "' and a.groupid=? ORDER BY b.id";
                rsItem.executeQuery(sqlItem, rs.getString("id"));
                group.put("groupName", rs.getString("name"));
                group.put("groupId", rs.getString("id"));
                if (rsItem.getCounts() == 0) {
                    continue;
                }
                //每个分组下面的项目
                while (rsItem.next()) {
                    List paramList = null;
                    Map<String, Object> item = new HashMap<String, Object>();
                    item.put("itemId", rsItem.getString("id"));
                    item.put("itemName", rsItem.getString("name"));
                    item.put("front_path", rsItem.getString("front_path"));
                    if (rsItem.getString("isdefault").equals("1")) {
                        apiResult.put("default_path", rsItem.getString("front_path"));
                        apiResult.put("default_group", rs.getString("id"));
                    }
                    paramList = this.selectParamById(rsItem.getString("id"));
                    item.put("paramList", paramList);
                    itemList.add(item);
                }
                group.put("item", itemList);
                groupList.add(group);
            }
            apiResult.put("data", groupList);
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
        } catch (Exception e) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
            e.printStackTrace();
        }
        return apiResult;
    }

    /**
     * 根据id将该接口的参数查询出来
     *
     * @param id
     * @return
     */
    public List selectParamById(String id) {
        String sql = "SELECT a.name ,b.name name1 ,remark FROM mobilemode_api_front_param a,mobilemode_api_server_param b where b.front_parameter_id=a.id and b.api_id=? ORDER BY a.show_order ASC";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql, id);
        List<Map<String, Object>> paramList = new ArrayList();
        while (rs.next()) {
            Map<String, Object> param = new HashMap<String, Object>();
            param.put("front_name", rs.getString("name"));
            param.put("server_name", rs.getString("name1"));
            param.put("remark", rs.getString("remark"));
            paramList.add(param);
        }
        return paramList;
    }

    /**
     *
     */
    public Map<String, Object> unitQuerys(String id, String querys, String skip) {
        Map<String, Object> value = new HashMap<String, Object>();
        List<Map<String, Object>> paramList = this.selectParamById(id);
        StringBuffer urlQuery = new StringBuffer("&");
        StringBuffer urlQueryBeforeEncode = new StringBuffer("&");
        if (!"".equals(querys)) {
            JSONObject jsonObject = JSONObject.fromObject(querys);
            for (int i = 0; i < paramList.size(); i++) {
                String queryEn = "";
                try {
                    queryEn = jsonObject.getString((String) paramList.get(i).get("front_name"));
                } catch (JSONException e) {
                    queryEn = "";
                }
                String serverName = (String) paramList.get(i).get("server_name");
                if (!"".equals(queryEn)) {
                    urlQueryBeforeEncode.append(serverName + "=" + queryEn + "&");
                    boolean isMatch = Pattern.matches(".*[\u4e00-\u9fa5]+.*$", queryEn);
//                    Pattern r = Pattern.compile("/.*[\u4e00-\u9fa5]+.*$/");
//                    Matcher m = r.matcher(queryEn);
//
                    if (isMatch) {
                        try {
                            urlQuery.append(serverName + "=" + URLEncoder.encode(queryEn, "UTF-8") + "&");
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }

                    } else {
                        urlQuery.append(serverName + "=" + queryEn + "&");
                    }
                }
                if (this.isList.equals("1") && !skip.equals("")) {
                    urlQuery.append("skip=" + skip);
                    urlQueryBeforeEncode.append("skip=" + skip);
                }
            }
            loggerBean.writeLog("urlQuery:" + urlQuery.toString());
        }
        value.put("urlQuery", urlQuery.toString().replace(" ", "%20"));
        value.put("urlQueryBeforeEncode", urlQueryBeforeEncode.toString());
        return value;
    }

    /**
     * 根据前端路径获取接口的id、参数、及其他基本信息
     *
     * @param user
     * @param params
     * @return
     * @throws Exception
     */
    public Map<String, Object> getIdByFrontPath(User user, Map<String, Object> params) throws Exception {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        String frontPath = Util.null2String(params.get("frontPath"));
        try {
            String sql = "select a.id id,a.name ,a.front_path front_path ,a.remark remark,b.islist islist ,b.showTemplet showTemplet from mobilemode_api a,crm_enterprise_api b where front_path=? and a.id=b.api_id";
            RecordSet rs = new RecordSet();
            rs.executeQuery(sql, frontPath);
            if (rs.first()) {
                String id = rs.getString("id");
                if (id.equals("")) {
                    apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                    apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(506123, user.getLanguage()));
                } else {
                    apiResult.put("apiId", id);
                    apiResult.put("apiName", rs.getString("name"));
                    apiResult.put("apiRemark", rs.getString("remark"));
                    apiResult.put("front_path", rs.getString("front_path"));
                    List paramList = this.selectParamById(id);
                    apiResult.put("params", paramList);
                    apiResult.put("islist", rs.getString("islist"));
                    apiResult.put("showTemplet", rs.getString("showTemplet"));
                    apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
                    apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
                }
            } else {
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(506123, user.getLanguage()));
            }
        } catch (Exception e) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(384850, user.getLanguage()));
            e.printStackTrace();
        }
        return apiResult;
    }

    /**
     * 集成中心-企业信息集成-调用日志-日志列表
     *
     * @param user
     * @param params
     * @return
     * @throws Exception
     */
    public Map<String, Object> getRequestLog(User user, Map<String, Object> params) throws Exception {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        if (!HrmUserVarify.checkUserRight("crm:businessinfo", user)) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(2012, user.getLanguage()));
        } else {
            try {
                String ipAddress = Util.null2String(params.get("clientip"));
                String requestUid = Util.null2String(params.get("requestuid"));
                String requestType = Util.null2String(params.get("requesttype"));
                String requestResult = Util.null2String(params.get("request_result"));
                String requestParamter = Util.null2String(params.get("request_paramter"));
                String name = Util.null2String(params.get("name"));
                String department = Util.null2String(params.get("department"));
                String subDepartment = Util.null2String(params.get("subDepartment"));
                String datetype = Util.null2String(params.get("datetype_selectType"));
                String startDate = Util.null2String(params.get("datetype_fromDate"));
                String endDate = Util.null2String(params.get("datetype_toDate"));
                String childWhere = "";
                //接口名称
                if (!name.equals("")) {
                    childWhere += " and b.id ='" + name + "'";
                }
                //请求参数
                if (!requestParamter.equals("")) {
                    childWhere += " and a.request_paramter like '%" + requestParamter + "%'";
                }
                //客户端ip
                if (!ipAddress.equals("")) {
                    childWhere += " and a.ipaddress like '%" + ipAddress + "%'";
                }
                //操作结果
                if (!requestResult.equals("")) {
                    childWhere += " and a.request_result like '%" + requestResult + "%'";
                }
                //类型
                if (!requestType.equals("")) {
                    childWhere += " and a.requesttype ='" + requestType + "'";
                }
                //操作者分部
                if (!subDepartment.equals("")) {
                    childWhere += " and a.requestuid in ( select id from HrmResource where subcompanyid1 = '" + subDepartment + "')";
                }
                //部门
                if (!department.equals("")) {
                    childWhere += " and a.requestuid in ( select id from HrmResource where departmentid = '" + department + "')";
                }
                //操作者
                if (!requestUid.equals("")) {
                    childWhere += " and a.requestuid = '" + requestUid + "'";
                }
                //日期范围
                if (!"".equals(datetype) && !"0".equals(datetype) && !"6".equals(datetype)) {
                    childWhere += " and a.requestdate >= '" + TimeUtil.getDateByOption(datetype + "", "0") + "'";
                    childWhere += " and a.requestdate <= '" + TimeUtil.getDateByOption(datetype + "", "") + "'";
                }

                if ("6".equals(datetype) && !"".equals(startDate)) {
                    childWhere += " and a.requestdate >= '" + startDate + "'";
                }

                if ("6".equals(datetype) && !"".equals(endDate)) {
                    childWhere += " and a.requestdate <= '" + endDate + "'";
                }
                String backfields = "a.id ,a.requestdate requestdate,a.requesttime requesttime,a.requesttype requesttype,a.requestuid requestuid,a.request_paramter request_paramter,a.request_result request_result,a.ipaddress ipaddress,b.name ,b.remark remark";
                String sqlFrom = "crm_busniessinfolog a,mobilemode_api b";
                String sqlWhere = " b.id=a.apiid" + childWhere;
                String orderBy = "a.id";
                String tableString = "<table pageId=\"" + PageIdConst.CRM_API_RequestLog + "\" pageUid=\"" + PageUidFactory.getCrmPageUid("103") + "\" pagesize=\"" + PageIdConst.getPageSize(PageIdConst.CRM_API_RequestLog, user.getUID(), PageIdConst.CRM) + "\" >";
                tableString += "<sql backfields=\"" + backfields + "\" sqlform=\"" + Util.toHtmlForSplitPage(sqlFrom) + "\" sqlorderby=\"" + orderBy + "\" sqlsortway=\"Desc\" sqlprimarykey=\"t1.id\" sqlwhere=\"" + Util.toHtmlForSplitPage(sqlWhere) + "\" sqlisdistinct=\"true\"/>";
                tableString += "<head>" +
                        "<col name='remark' width='35%' text='" + SystemEnv.getHtmlLabelName(386719, user.getLanguage()) + "' column='remark' orderkey='remark' target='_blank'/>" +
                        "<col name='requestdate' width='5%' text='" + SystemEnv.getHtmlLabelName(97, user.getLanguage()) + "' column='requestdate'/>" +
                        "<col name='requesttime' width='5%' text='" + SystemEnv.getHtmlLabelName(277, user.getLanguage()) + "' column='requesttime'/>" +
                        "<col name='requestuid' width='5%' text='" + SystemEnv.getHtmlLabelName(17482, user.getLanguage()) + "' column='requestuid' transmethod='com.api.crm.util.CrmSPATransMethod.getHrmNameLink' orderkey='requestuid'/>" +
                        "<col name='requesttype' width='5%' text='" + SystemEnv.getHtmlLabelName(63, user.getLanguage()) + "' column='requesttype'  transmethod='com.api.crm.util.CrmSPATransMethod.getApiRequestType' otherpara='" + user.getLanguage() + "' orderkey='requesttype'/>" +
                        "<col name='request_result' width='10%' text='" + SystemEnv.getHtmlLabelName(356, user.getLanguage()) + "' column='request_result'/>" +
                        "<col name='ipaddress' width='5%' text='" + SystemEnv.getHtmlLabelName(32531, user.getLanguage()) + "' column='ipaddress'/>" +
                        "<col name='request_paramter' width='30%' text='" + SystemEnv.getHtmlLabelName(561, user.getLanguage()) + "' column='request_paramter'/>" +
                        "</head>";
                tableString += "</table>";
                String sessionkey = PageUidFactory.getCrmPageUid("103") + "_" + Util.getEncrypt(Util.getRandom());
                Util_TableMap.setVal(sessionkey, tableString);
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
                apiResult.put(CrmConstant.CRM_RESULT_SESSIONKEY, sessionkey);
            } catch (Exception e) {
                loggerBean.writeLog(e.getMessage());
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
                e.printStackTrace();
            }
        }
        return apiResult;
    }

    /**
     * 集成中心-企业信息集成-调用日志-高级搜索表单
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> getRequestLogConditions(User user, Map<String, Object> params) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        try {
            //高级搜索--开始
            List<Map<String, Object>> formItemGroupList = new ArrayList<Map<String, Object>>();//分组队列
            Map<String, Object> formItemGroup = new HashMap<String, Object>();//分组信息
            List<Map<String, Object>> formItems = new ArrayList<Map<String, Object>>();//元素队列
            Map<String, Object> formItem = new HashMap<String, Object>();//元素信息
            //接口名称
            List<String> domKeyList = new ArrayList<String>();
            domKeyList.add("name");
            List<SearchConditionOption> optionList = new ArrayList<SearchConditionOption>();
            String sql = "select a.id id,a.name ,a.remark remark from mobilemode_api a,crm_enterprise_api b where a.id=b.api_id and b.flag='crm'";
            RecordSet rs = new RecordSet();
            rs.executeQuery(sql);
            optionList.add(new SearchConditionOption("", "", true));
            while (rs.next()) {
                optionList.add(new SearchConditionOption(rs.getString("id"), rs.getString("remark"), false));
            }
            formItem = CrmFormItemUtil.simpleSelectItem(domKeyList, optionList, SystemEnv.getHtmlLabelName(386719, user.getLanguage()), null, 2);
            formItems.add(formItem);
            //参数
            formItem = CrmFormItemUtil.getFormItemForInput("request_paramter", SystemEnv.getHtmlLabelName(561, user.getLanguage()), null, 100, 2);
            formItems.add(formItem);
            //结果
            formItem = CrmFormItemUtil.getFormItemForInput("request_result", SystemEnv.getHtmlLabelName(356, user.getLanguage()), null, 100, 2);
            formItems.add(formItem);
            //操作人
            formItem = CrmFormItemUtil.getFormItemForBrowser("requestuid", SystemEnv.getHtmlLabelName(99, user.getLanguage()), "1", "", 2, "", "", null);
            formItems.add(formItem);
            //操作部门
            formItem = CrmFormItemUtil.getFormItemForBrowser("department", SystemEnv.getHtmlLabelName(33826, user.getLanguage()), "4", "", 2, "", "", null);
            formItems.add(formItem);
            //操作分部
            formItem = CrmFormItemUtil.getFormItemForBrowser("subDepartment", SystemEnv.getHtmlLabelName(33827, user.getLanguage()), "164", "", 2, "", "", null);
            formItems.add(formItem);
            //类型
            domKeyList = new ArrayList<String>();
            domKeyList.add("requesttype");
            optionList = new ArrayList<SearchConditionOption>();
            optionList.add(new SearchConditionOption("", "", true));
            optionList.add(new SearchConditionOption("0", SystemEnv.getHtmlLabelName(32363, user.getLanguage()), false));
            optionList.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(131288, user.getLanguage()), false));
            formItem = CrmFormItemUtil.simpleSelectItem(domKeyList, optionList, SystemEnv.getHtmlLabelName(15503, user.getLanguage()), null, 2);
            formItems.add(formItem);
            //IP地址
            formItem = CrmFormItemUtil.getFormItemForInput("clientip", SystemEnv.getHtmlLabelName(32531, user.getLanguage()), null, 100, 2);
            formItems.add(formItem);
            //操作时间
            List<Map<String, Object>> options = CrmFormItemUtil.getDateTypeOptions("0", user.getLanguage());
            formItem = CrmFormItemUtil.getFormItemForBrowserDate("datetype", SystemEnv.getHtmlLabelName(97, user.getLanguage()), "", 2, options, null);
            formItems.add(formItem);
            //高级搜索--结束
            formItemGroup.put("title", SystemEnv.getHtmlLabelName(21995, user.getLanguage()));
            formItemGroup.put("items", formItems);
            formItemGroup.put("defaultshow", true);
            formItemGroupList.add(formItemGroup);

            apiResult.put(CrmConstant.CRM_RESULT_DATA, formItemGroupList);
            apiResult.put(CrmConstant.CRM_RESULT_TYPE, 0);
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
        } catch (Exception e) {
            loggerBean.writeLog(e.getMessage());
            e.printStackTrace();
            return getExceptionMsg();
        }
        return apiResult;
    }

    /**
     * 集成中心-企业信息集成-应用设置表单
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> getEnterpriseSettingForm(User user, Map<String, Object> params) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        if (!HrmUserVarify.checkUserRight("crm:businessinfo", user)) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(2012, user.getLanguage()));
            apiResult.put("havRight", false);
        } else {
            try {
                RecordSet recordSet = new RecordSet();
                recordSet.executeQuery("select * from crm_busniessinfosettings where id=?", 1);
                String appkey = "";
                if (recordSet.first()) {
                    appkey = Util.null2String(recordSet.getString("appkey"));
                }
                List<SearchConditionOption> sourceoptions = new ArrayList<SearchConditionOption>();
                sourceoptions.add(new SearchConditionOption("1", SystemEnv.getHtmlLabelName(501677, user.getLanguage()), true));
                apiResult.put("source", "1");
                apiResult.put("sourceoptions", sourceoptions);
                apiResult.put("appkey", appkey);
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
                apiResult.put("havRight", true);
            } catch (Exception e) {
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
                e.printStackTrace();
            }
        }
        return apiResult;
    }

    /**
     * 集成中心-企业信息集成-应用设置保存
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> saveEnterpriseSettingForm(User user, Map<String, Object> params) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        if (!HrmUserVarify.checkUserRight("crm:businessinfo", user)) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(2012, user.getLanguage()));
        } else {
            try {
                String appkey = Util.null2String((String) params.get("appkey"));
                String source = Util.null2String((String) params.get("source"));
                String sql = "update crm_busniessinfosettings set appkey='" + appkey + "' where id='" + source + "'";
                RecordSet rs = new RecordSet();
                rs.execute(sql);
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
            } catch (Exception e) {
                apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
                apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
                e.printStackTrace();
            }
        }
        return apiResult;
    }

    /**
     * 集成中心-企业信息集成-接口调用-列表
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> getApiListTable(User user, Map<String, Object> params) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        List datas = new ArrayList();
        RecordSet rs = new RecordSet();
        String name = Util.null2String(params.get("name"));
        String sqlFrom = "";
        if (!name.equals("")) {
            sqlFrom = "and a.remark like '%" + name + "%'";
        }
        try {
            String sql = "select a.id id,a.name ,a.remark remark,a.front_path front_path,b.isdefault isdefault ,c.name groupname from mobilemode_api a,crm_enterprise_api b ,mobilemode_api_group c where a.id=b.api_id and a.groupid=c.id and b.flag='crm'" + sqlFrom;
            rs.executeQuery(sql);
            int index = 3;
            datas.add(this.getMutilyApiFront(user, "crm", front_path1, 1, "" + weaver.systeminfo.SystemEnv.getHtmlLabelName(10004418, weaver.general.ThreadVarLanguage.getLang()) + ""));
            datas.add(this.getMutilyApiFront(user, "crmGroup", front_path2, 2, "" + weaver.systeminfo.SystemEnv.getHtmlLabelName(10004419, weaver.general.ThreadVarLanguage.getLang()) + ""));
            while (rs.next()) {
                Map<String, Object> data = new HashMap<String, Object>();
                data.put("key", index);
                data.put("name", rs.getString("remark"));//接口名称
                data.put("groupName", rs.getString("groupname"));//分组名称
                List paramList = this.selectParamById(rs.getString("id"));
                StringBuffer path = new StringBuffer(this.front_path + rs.getString("front_path") + "?");
                for (Object param : paramList) {
                    JSONObject tempParam = JSONObject.fromObject(param);
                    if (!tempParam.getString("front_name").equals("appkey")) {
                        path.append(tempParam.getString("front_name") + "=" + Util.formatMultiLang(tempParam.getString("remark"), user.getLanguage() + "") + "&");
                    }
                }
                if (path.toString().endsWith("&")) {
                    path = new StringBuffer(path.substring(0, path.length() - 1));
                }
                data.put("TrackBack", path.toString());//引用路径
                data.put("remark", paramList);//备注
                datas.add(data);
                index++;
            }

            apiResult.put("data", datas);
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
        } catch (Exception e) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
            e.printStackTrace();
        }
        return apiResult;
    }

    /**
     * 根据多接口页面标识获取该接口的调用链接
     *
     * @param user
     * @param flag
     * @return
     */
    public Map<String, Object> getMutilyApiFront(User user, String flag, String front_path, int index, String desc) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        StringBuffer path = new StringBuffer(front_path + "/" + flag + "?");
        List params = new ArrayList();
        String sql = "select DISTINCT b.remark remark, b.name  from  crm_enterprise_api a,mobilemode_api_front_param b where a.api_id=b.api_id and a.flag='" + flag + "'";
        RecordSet rs = new RecordSet();
        rs.executeQuery(sql);
        while (rs.next()) {
            Map<String, Object> param = new HashMap<String, Object>();
            if (!rs.getString("front_name").equals("appkey")) {
                path.append(rs.getString("name") + "=" + Util.formatMultiLang(rs.getString("remark"), user.getLanguage() + "") + "&");
                param.put("front_name", rs.getString("name"));
                param.put("remark", rs.getString("remark"));
                params.add(param);
            }
        }
        if (path.toString().endsWith("&")) {
            path = new StringBuffer(path.substring(0, path.length() - 1));
        }
        apiResult.put("key", index);
        apiResult.put("name", desc);//接口名称
        apiResult.put("groupName", flag);//分组名称
        apiResult.put("TrackBack", path.toString());
        apiResult.put("remark", params);
        return apiResult;
    }

    /**
     * 获取Table，解析Array
     *
     * @param subTable 数据串
     * @param name     上级结果名称
     * @param apiId    数据库中的id
     * @param remark   字段名称备注
     * @return
     */
    public Map<String, Object> getTableCondition(String subTable, String name, String apiId, String remark) {
        Map<String, Object> datas = new HashMap<String, Object>();
        Map<String, Object> column = null;
        Map<String, Object> data = new HashMap<String, Object>();

        List list = new ArrayList();
        List columns = new ArrayList();
        List<String> keys = new ArrayList();
        boolean iString = false;
        //字符串数组化
        JSONArray dataTemp = JSONArray.fromObject(subTable);
        int parentNameLength = name.split("[.]").length;
        //拿出一条数据，判断一下值的类型是一个对象还是一个字符串
        //获取key，把数组拿出来
        RecordSet rss = new RecordSet();
        String sqlTitle = "select * from mobilemode_api_result_define where api_id=? and name=?";
        RecordSet rs = new RecordSet();
        String sqlParam = "select * from mobilemode_api_result_define where api_id=? and name LIKE '%" + name + "%' ORDER BY show_order ASC";
        rs.executeQuery(sqlParam, apiId);
        if (rs.getCounts() == 1) {
            rs.first();
            String nameArray[] = rs.getString("name").split("[.]");
            String key = nameArray[nameArray.length - 1];
            column = new HashMap<String, Object>();
            column.put("title", rs.getString("remark"));
            column.put("dataIndex", key);
            columns.add(column);
            keys.add(key);
            iString = true;
        } else {
            while (rs.next()) {
                column = new HashMap<String, Object>();
                String type = rs.getString("type");
                String nameArray[] = rs.getString("name").split("[.]");
                if (nameArray.length != parentNameLength + 1) {
                    continue;
                } else {
                    String key = nameArray[nameArray.length - 1];
                    keys.add(key);
                    String sortDirections[] = {"descend", "ascend"};
                    if (type.equals("ARRAY")) {
                        column.put("dataIndex", "");
                        column.put("key", "x");
                        column.put("render", key);
                        column.put("columnName", key);
                    } else if (type.equals("JSON")) {
                        column.put("dataIndex", "");
                        column.put("key", "x");
                        column.put("render", key);
                        column.put("columnName", key);
                    } else {
                        column.put("sortDirections", sortDirections);
                        column.put("isHtml", true);
                        column.put("key", key);
                        column.put("dataIndex", key);
                    }
                    column.put("title", rs.getString("remark"));
                    columns.add(column);
                }
            }
        }
        //如果这个列表下面没有数据，则只显示表头和一条空数据
        int index = 1;
        if (dataTemp.size() == 0) {
            data.put("key", index);
            for (String key : keys) {
                data.put(key, "-");
            }
            list.add(data);
        } else {
            if (iString) {
                String[] nameArray = name.split("[.]");
                String keySingle = nameArray[nameArray.length - 1];
                rss.executeQuery(sqlTitle, apiId, name);
                rss.first();
                index = 1;
                for (int j = 0; j < dataTemp.size(); j++) {
                    data = new HashMap<String, Object>();
                    data.put("key", index);
                    data.put(keySingle, getValueMapping(rss.getString("value_mapping"), Util.null2String(dataTemp.get(j))));
                    index++;
                    list.add(data);
                }
            } else {
                index = 1;
                //获取Table值
                for (int i = 0; i < dataTemp.size(); i++) {
                    data = new HashMap<String, Object>();
                    data.put("key", index);
                    String dataTempI = Util.null2String(dataTemp.get(i));
                    for (String key : keys) {
                        String value = "";
                        Object dataTempIObject = JSONObject.fromObject(dataTempI).get(key);
                        String tempName = name + "." + key;
                        rss.executeQuery(sqlTitle, apiId, tempName);
                        rss.first();
                        if (dataTempIObject instanceof List) {
                            Map<String, Object> tempTable = getTableCondition(Util.null2String(dataTempIObject), tempName, apiId, rss.getString("reamrk"));
                            sublist.put(key + index, tempTable);
                        } else if (dataTempIObject instanceof JSONObject) {//将子列表中的json处理成列表的形式
                            Map<String, Object> tempTable = getTableCondition("[" + Util.null2String(dataTempIObject) + "]", tempName, apiId, rss.getString("reamrk"));
                            sublist.put(key + index, tempTable);
//                            jsonlist.put(key + index,getJSONCondition(tempName,dataTempIObject,apiId));
                        } else {
                            value = getValueMapping(rss.getString("value_mapping"), Util.null2String(dataTempIObject));
                            data.put(key, value);
                        }
                    }
                    index++;
                    list.add(data);
                }
            }
        }
        datas.put("listSize", index - 1);//分组名
        datas.put("title", remark);//分组名
        datas.put("list", list);//数据
        datas.put("columns", columns);//表头
        return datas;
    }

    /**
     * 获取Table，解析JSON
     *
     * @param nameP
     * @param tempData
     * @param apiId
     * @return
     */
    public Map<String, Object> getJSONCondition(String nameP, Object tempData, String apiId) {
        List baseList = new ArrayList();
        RecordSet rs = new RecordSet();
        List tableList = new ArrayList();
        Map<String, Object> data = new HashMap<String, Object>();
        int parentNameLength = nameP.split("[.]").length;
        JSONObject dataTemp = JSONObject.fromObject(tempData);
        String sql = "select * from mobilemode_api_result_define where api_id=? and name LIKE '%" + nameP + "%' ORDER BY show_order ASC";
        rs.executeQuery(sql, apiId);
        while (rs.next()) {
            String name = rs.getString("name");
            String[] nameArray = name.split("[.]");
            if (nameArray.length != parentNameLength + 1) {
                continue;
            } else {
                String remark = rs.getString("remark");
                String key = nameArray[nameArray.length - 1];
                Object dataTempIObject = JSONObject.fromObject(dataTemp).get(key);
                //数组类型的用Table显示
                if (dataTempIObject instanceof List) {
                    String subTable = dataTemp.getString(key);
                    Map<String, Object> datas1 = getTableCondition(subTable, name, apiId, remark);
                    tableList.add(datas1);
                    data.put("tableList", tableList);
                } else if (dataTempIObject instanceof JSONObject) {
                    String subTable = dataTemp.getString(key);
                    Map<String, Object> datas1 = getTableCondition("[" + subTable + "]", name, apiId, remark);
                    tableList.add(datas1);
                    data.put("tableList", tableList);
                } else {//第一层的String类型就用普通的表单显示
                    Map<String, String> baseInfo = new HashMap<String, String>();
//                    String temp = dataTemp.getString(key);
                    String temp = getValueMapping(rs.getString("value_mapping"), dataTemp.getString(key));
                    if (isList.equals("1") && key.equals("total")) {
                        data.put("total", temp);
                    }
                    baseInfo.put("key", key);//标记
                    baseInfo.put("showName", remark);//显示名
                    baseInfo.put("value", temp);//显示值
                    baseList.add(baseInfo);
                }
                data.put("baseList", baseList);
            }
        }
        return data;
    }

    /**
     * @param valueMapping
     * @param temp
     * @return
     */
    public String getValueMapping(String valueMapping, String temp) {
        String result = "";
        if (valueMapping.equals("")) {
            result = temp;
        } else {
            JSONArray dataTemp = JSONArray.fromObject(valueMapping);
            Iterator<String> it = dataTemp.iterator();
//            Map<String, Object>
            while (it.hasNext()) {
                JSONObject js = JSONObject.fromObject(it.next());
                String init = js.getString("init");
                String end = js.getString("end");
                if (init.equals(temp) && !end.equals("")) {
                    result = end;
                }
            }
        }
        return result.equals("") ? temp : result;
    }

    /**
     * 写入日志
     *
     * @param callType
     * @param apiId
     * @param urlQueryBeforeEncode
     * @param operator
     * @param callResult
     * @param ipAddress
     * @return
     */
    public String writeLog(String callType, String apiId, String urlQueryBeforeEncode, String operator, String callResult, String ipAddress) {
        String callTime = TimeUtil.getOnlyCurrentTimeString();//时间
        String callDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());//日期
        String sqlLog = "insert into crm_busniessinfolog(requesttype,requestdate,requesttime,apiid,request_paramter,requestuid,request_result,ipaddress)" +
                "values('" + callType + "','" + callDate + "','" + callTime + "','" + apiId + "','" + urlQueryBeforeEncode + "','" + operator + "','" + callResult + "','" + ipAddress + "')";
        RecordSet rs = new RecordSet();
        rs.execute(sqlLog);
        return "";
    }

    /**
     * k2请求写入日志
     *
     * @param callType
     * @param apiId
     * @param urlQueryBeforeEncode
     * @param callResult
     * @param ipAddress
     * @param inquirer
     * @return
     */
    public void k2WriteLog(String callType, String apiId, String urlQueryBeforeEncode, String callResult, String ipAddress, String inquirer) {
        String callTime = TimeUtil.getOnlyCurrentTimeString();
        String callDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

        // K2的查询数据记录到建模表
        // 根据apiId获取接口名称
        String sql = "select name from mobilemode_api where id = ?";
        String apiName = QueryUtil.doQueryFieldValueWithArgs(sql, "name", apiId);
        Entity data = new Entity();
        data.setTableName("uf_k2qxb_log");
        // 接口名称
        data.set("jkmc", apiName);
        // 参数名称
        data.set("csmc", urlQueryBeforeEncode);
        // 调用时间
        String dysj = callDate + " " + callTime.substring(0, 5);
        data.set("dysj", dysj);
        // 调用人
        data.set("dyr", inquirer);
        // 调用类型
        data.set("lx", callType);
        // 调用结果
        data.set("jg", callResult);
        // ip
        data.set("ip", ipAddress);

        loggerBean.writeLog("k2WriteLog data: " + data.toString());

        String formModeId = QueryUtil.getFormmodeidByTablename("uf_k2qxb_log");

        loggerBean.writeLog("k2WriteLog formModeId: " + formModeId);

        int dataId = ModeFormUtil.insertModeData(data, Convert.toInt(formModeId), 1);

        loggerBean.writeLog("k2WriteLog dataId: " + dataId);
    }

    /**
     * 写入缓存
     *
     * @param query
     * @param user
     * @param result
     * @return
     */
    public String writeCache(String query, User user, String result, String apiServerAddr, String apiServerPath) {
        String callTime = TimeUtil.getOnlyCurrentTimeString();//时间
        String callDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());//日期
        RecordSet rs = new RecordSet();
        //如果是重新请求的数据，先删除原来的数据，再插入新数据
        String sqlDelete = "delete from crm_busniessinfoeache where path='" + apiServerAddr + apiServerPath + "' and query='" + query + "'";
        rs.execute(sqlDelete);
        //dm国产数据库的dbtype为oracle,其真实类型在orgindbtype属性中
        if (rs.getDBType().equals("oracle") && Util.null2String(rs.getOrgindbtype()).equals("oracle")) {//oracle特殊处理clob
            String insertSql = "insert into crm_busniessinfoeache (userid,data,modifydate,modifytime,path,query) values('" + user.getUID() + "',empty_clob(),'" + callDate + "','" + callTime + "','" + apiServerAddr + apiServerPath + "','" + query + "')";
            String updateSql = "select data from crm_busniessinfoeache where path='" + apiServerAddr + apiServerPath + "' and query='" + query + "' for update";
            handleOracleClob(insertSql, updateSql, result);
        } else if ("mysql".equalsIgnoreCase(rs.getDBType())) {

            result = result.replace("\\", "\\\\");

            String sqlInsertCatch = "insert into crm_busniessinfoeache(userid,data,modifydate,modifytime,path,query) values('" + user.getUID() + "','" + result + "','" + callDate + "','" + callTime + "','" + apiServerAddr + apiServerPath + "','" + query + "')";
            rs.execute(sqlInsertCatch);

        } else {
            String sqlInsertCatch = "insert into crm_busniessinfoeache(userid,data,modifydate,modifytime,path,query) values('" + user.getUID() + "','" + result + "','" + callDate + "','" + callTime + "','" + apiServerAddr + apiServerPath + "','" + query + "')";
            rs.execute(sqlInsertCatch);
        }
        return "";
    }

    /**
     * 未使用，暂定
     *
     * @param data_after_deal
     * @param path
     * @param query
     * @param result
     */

    public void updateCache(String data_after_deal, String path, String query, String result) {
        RecordSet rs = new RecordSet();
        if (rs.getDBType().equals("oracle") && rs.getOrgindbtype().equalsIgnoreCase("oracle")) {
            String updateSql = "select data_after_deal from crm_busniessinfoeache where path='" + path + "' and query='" + query + "' for update";
            handleOracleClob("", updateSql, result);
        } else {
            String sql = "update crm_busniessinfoeache set data_after_deal='" + data_after_deal + "'where path='" + path + "' and query='" + query + "' for update";
            rs.execute(sql);
        }
    }

    /**
     * Oracle特殊处理clob
     *
     * @param insertSql
     * @param updateSql
     * @param result
     */
    public void handleOracleClob(String insertSql, String updateSql, String result) {
        ConnStatement conn = null;
        try {
            conn = new ConnStatement();
            if (!insertSql.equals("")) {
                conn.setStatementSql(insertSql);
                conn.executeUpdate();
            }
            conn.setStatementSql(updateSql);
            conn.executeQuery();
            oracle.sql.CLOB clob = null;
            if (conn.next()) {
                char[] c = result.toCharArray();
                clob = (oracle.sql.CLOB) conn.getClob(1);
                Writer outStream = clob.getCharacterOutputStream();
                outStream.write(c, 0, c.length);
                outStream.flush();
                outStream.close();
            }
        } catch (Exception e) {
            loggerBean.writeLog(e.getMessage());
        } finally {
            if (conn != null) {
                conn.close();
            }
        }
    }

    /**
     * 多接口页面按分组加载数据
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> getConditionByGroup(User user, Map<String, Object> params, HttpServletRequest request) throws Exception {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        String groupId = Util.null2String(params.get("groupId"));
        String module = Util.null2String(params.get("module"));
        String firstId = Util.null2String(params.get("firstId"));
        if (groupId.equals("") || module.equals("")) {
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(390950, user.getLanguage()));
            return apiResult;
        }
        try {
            RecordSet rs = new RecordSet();
            String sql = "select b.id id ,b.front_path front_path ,b.name  ,a.islist islist from crm_enterprise_api a,mobilemode_api b where a.api_id=b.id and a.flag=? and b.groupid=? ORDER BY a.id";
            rs.executeQuery(sql, module, groupId);
            List paramsList = new ArrayList();
            while (rs.next()) {
                Map<String, Object> info = new HashMap<String, Object>();
                String id = rs.getString("id");
                String front_path = rs.getString("front_path");
                String name = rs.getString("name");
                info.put("id", id);
                info.put("front_path", front_path);
                info.put("name", name);
                paramsList.add(info);
            }
            //如果制定了第一个元素，那么将第一个元素与指定的互换
            if (!firstId.equals("")) {
                for (int i = 0; i < paramsList.size(); i++) {
                    String tempId = JSONObject.fromObject(paramsList.get(i)).getString("id");
                    if (tempId.equals(firstId)) {
                        Collections.swap(paramsList, 0, i);
                    }
                }
            }
            apiResult.put("data", paramsList);
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
        } catch (Exception e) {
            loggerBean.writeLog(e.getMessage());
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
            e.printStackTrace();
        }
        return apiResult;
    }

    /**
     * 根据前端路径获取分组id
     *
     * @param user
     * @param params
     * @return
     */
    public Map<String, Object> getGroupIdByApifront(User user, Map<String, Object> params) {
        Map<String, Object> apiResult = new HashMap<String, Object>();
        try {
            String front_path = Util.null2String(params.get("front_path"));
            String id = Util.null2String(params.get("id"));
            String sql = "select groupid from mobilemode_api where id=?";
            RecordSet rs = new RecordSet();
            rs.executeQuery(sql, id);
            rs.first();
            apiResult.put("groupId", rs.getString(1));
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "success");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(30700, user.getLanguage()));
        } catch (Exception e) {
            loggerBean.writeLog(e.getMessage());
            apiResult.put(CrmConstant.CRM_RESULT_STATUS, "fail");
            apiResult.put(CrmConstant.CRM_RESULT_MESSAGECODE, SystemEnv.getHtmlLabelName(83912, user.getLanguage()));
            e.printStackTrace();
        }
        return apiResult;
    }
}
