package com.engine.grtool.doc.util;

import com.aspose.words.*;
import com.aspose.words.Shape;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * @FileName WordsUtil.java
 * @Description words工具类，处理word文档、pdf文档这些
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/26
 */
public class WordsUtil {
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(WordsUtil.class);


    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user) {
        return doc2Pdf(orginDocId, user, "");
    }

    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user, String waterStr) {
        DocFileInfo fileInfo = DocUtil.getFirstFileWithInputStream(orginDocId);
        String fileName = Util.null2String(fileInfo.getFileName());
        String fileNameWithOutExt = fileName;
        if (fileName.contains(".")) {
            fileNameWithOutExt = fileName.substring(0, fileName.lastIndexOf("."));
        }
        String newFileName = fileNameWithOutExt + ".pdf";
        InputStream orginDocInputStream = fileInfo.getFileInputStream();
        if (orginDocInputStream != null) {
            return doc2Pdf(orginDocInputStream, newFileName, user, fileInfo.getSeccategory(), waterStr);
        }
        return -1;
    }

    /**
     *
     * @param orginDocInputStream
     * @param pdfFileName
     * @param user
     * @param newDocCategoryId
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(InputStream orginDocInputStream, String pdfFileName, User user, int newDocCategoryId, String waterStr) {
        int newPdfDocId = -1;
        InputStream newFileIs;
        String tempFilePath = "";
        Document docss;
        FileOutputStream osll = null;
        try {
            //创建临时文件目录
            String tempPdfFileFolder = GCONST.getRootPath() + File.separator + "grsd" + File.separator + "temp";
            createTempFileFolder(tempPdfFileFolder);
            //创建历史文件
            String uuid = UUID.randomUUID().toString();
            tempFilePath = tempPdfFileFolder + File.separator + uuid + ".pdf";
            //创建文件输出流
            osll = new FileOutputStream(tempFilePath);
            //load授权
            loadLicense();
            try {
                docss = new Document(orginDocInputStream);
                if (!Util.null2String(waterStr).isEmpty()) {
                    //插入水印
                    insertWatermarkText(docss, waterStr);
                }
                // 全面支持DOC, DOCX, OOXML, RTF，HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
                docss.save(osll, SaveFormat.PDF);
                //获取临时文件的输入流
                newFileIs = Files.newInputStream(Paths.get(tempFilePath));
                //根据临时文件输出流，创建OA文档
                newPdfDocId = DocUtil.createDocWithFile(newFileIs, user, pdfFileName, newDocCategoryId);
            } catch (Exception e) {
                log.error("doc2Pdf error", e);
            }
        } catch (Exception e1) {
            log.error("doc2Pdf异常,", e1);
        } finally {
            //关闭文件原始文件输入流
            try {
                orginDocInputStream.close();
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //关闭目标文件输出流
            try {
                if (osll != null) {
                    osll.close();
                }
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //删除临时文件
            if (osll != null) {
                File file = new File(tempFilePath);
                if (file.exists()) {
                    file.delete();
                }
            }
        }
        return newPdfDocId;
    }

    /**
     * 创建临时目录
     *
     * @param tempPdfFilePath
     */
    private static void createTempFileFolder(String tempPdfFilePath) {
        //判断该目录是否存在
        File tempPdfFileDir = new File(tempPdfFilePath);
        if (!tempPdfFileDir.exists()) {
            tempPdfFileDir.mkdirs();
        }
    }

    /**
     * 加载授权
     */
    private static void loadLicense() {
        // 返回读取指定资源的输入流
        License license = new License();
        InputStream is = null;
        try {
            //String licenseFile = "/Users/<USER>/Documents/WEAVER/otherlib/aspose-words-14.9.0-jdk16-license.xml";
            String licenseFile = GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "lib" + File.separator + "aspose-words-14.9.0-jdk16-license.xml";
            //获取文件流
            is = Files.newInputStream(Paths.get(licenseFile));
            license.setLicense(is);
        } catch (Exception e) {
            log.error("loadLicense异常,", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("loadLicense释放资源异常", e);
                }
            }
        }
    }

    /**
     * PDF生成水印
     *
     * @param doc
     * @param watermarkText
     * @throws Exception
     * @throws
     * @Title: insertWatermarkText
     * @Description:
     * <AUTHOR>
     */
    private static void insertWatermarkText(Document doc, String watermarkText) throws Exception {
        // 获取页面尺寸
        PageSetup ps = doc.getFirstSection().getPageSetup();
        double pageWidth = ps.getPageWidth();
        double pageHeight = ps.getPageHeight();

        for (Section sect : doc.getSections()) {
            //设置首页不同
            sect.getPageSetup().setDifferentFirstPageHeaderFooter(true);

            // 为每个页眉类型创建独立的水印段落，避免共享同一个段落对象
            Paragraph watermarkParaPrimary = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);
            Paragraph watermarkParaFirst = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);
            Paragraph watermarkParaEven = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);

            insertWatermarkIntoHeader(watermarkParaPrimary, sect, HeaderFooterType.HEADER_PRIMARY);
            insertWatermarkIntoHeader(watermarkParaFirst, sect, HeaderFooterType.HEADER_FIRST);
            insertWatermarkIntoHeader(watermarkParaEven, sect, HeaderFooterType.HEADER_EVEN);
        }
        System.out.println("Watermark Set");
    }

    /**
     * 创建水印段落
     * @param doc 文档对象
     * @param watermarkText 水印文本
     * @param pageWidth 页面宽度
     * @param pageHeight 页面高度
     * @return 包含水印的段落对象
     * @throws Exception
     */
    private static Paragraph createWatermarkParagraph(Document doc, String watermarkText, double pageWidth, double pageHeight) throws Exception {
        Paragraph watermarkPara = new Paragraph(doc);

        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
        //水印内容
        watermark.getTextPath().setText(watermarkText);
        //水印字体
        watermark.getTextPath().setFontFamily("宋体");
        //水印宽度
        watermark.setWidth(150);
        //水印高度
        watermark.setHeight(30);
        //旋转水印
        watermark.setRotation(-40);
        //水印颜色
        watermark.getFill().setColor(Color.lightGray);
        watermark.setStrokeColor(Color.lightGray);
        //将水印置于文字下方
        watermark.setBehindText(true);
        watermark.setWrapType(WrapType.NONE);
        watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
        watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);

        // 平铺水印
        for (double y = 0; y < pageHeight; y += 100) {
            for (double x = 0; x < pageWidth; x += 200) {
                Shape watermarkClone = (Shape) watermark.deepClone(true);
                watermarkClone.setLeft(x);
                watermarkClone.setTop(y);
                watermarkPara.appendChild(watermarkClone);
            }
        }

        return watermarkPara;
    }


    private static void insertWatermarkIntoHeader(Paragraph watermarkPara, Section sect, int headerType) throws Exception {
        HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(headerType);
        if (header == null) {
            header = new HeaderFooter(sect.getDocument(), headerType);
            sect.getHeadersFooters().add(header);
        }
        header.appendChild(watermarkPara.deepClone(true));
    }
}