package com.engine.grtool.doc.util;

import com.aspose.words.*;
import com.aspose.words.Shape;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * @FileName WordsUtil.java
 * @Description words工具类，处理word文档、pdf文档这些
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/26
 */
public class WordsUtil {
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(WordsUtil.class);


    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user) {
        return doc2Pdf(orginDocId, user, "");
    }

    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user, String waterStr) {
        DocFileInfo fileInfo = DocUtil.getFirstFileWithInputStream(orginDocId);
        String fileName = Util.null2String(fileInfo.getFileName());
        String fileNameWithOutExt = fileName;
        if (fileName.contains(".")) {
            fileNameWithOutExt = fileName.substring(0, fileName.lastIndexOf("."));
        }
        String newFileName = fileNameWithOutExt + ".pdf";
        InputStream orginDocInputStream = fileInfo.getFileInputStream();
        if (orginDocInputStream != null) {
            return doc2Pdf(orginDocInputStream, newFileName, user, fileInfo.getSeccategory(), waterStr);
        }
        return -1;
    }

    /**
     *
     * @param orginDocInputStream
     * @param pdfFileName
     * @param user
     * @param newDocCategoryId
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(InputStream orginDocInputStream, String pdfFileName, User user, int newDocCategoryId, String waterStr) {
        int newPdfDocId = -1;
        InputStream newFileIs;
        String tempFilePath = "";
        Document docss;
        FileOutputStream osll = null;
        try {
            //创建临时文件目录
            String tempPdfFileFolder = GCONST.getRootPath() + File.separator + "grsd" + File.separator + "temp";
            createTempFileFolder(tempPdfFileFolder);
            //创建历史文件
            String uuid = UUID.randomUUID().toString();
            tempFilePath = tempPdfFileFolder + File.separator + uuid + ".pdf";
            //创建文件输出流
            osll = new FileOutputStream(tempFilePath);
            //load授权
            loadLicense();
            try {
                docss = new Document(orginDocInputStream);
                // 先转换为PDF，不添加水印
                docss.save(osll, SaveFormat.PDF);
                osll.close(); // 关闭输出流

                // 如果需要添加水印，在PDF中添加
                if (!Util.null2String(waterStr).isEmpty()) {
                    String tempWatermarkFilePath = tempFilePath.replace(".pdf", "_watermark.pdf");
                    addPdfWatermark(tempFilePath, tempWatermarkFilePath, waterStr, user);
                    // 删除原始PDF文件
                    //new File(tempFilePath).delete();
                    // 使用带水印的PDF文件
                    tempFilePath = tempWatermarkFilePath;
                }

                //获取临时文件的输入流
                newFileIs = Files.newInputStream(Paths.get(tempFilePath));
                //根据临时文件输出流，创建OA文档
                newPdfDocId = DocUtil.createDocWithFile(newFileIs, user, pdfFileName, newDocCategoryId);
            } catch (Exception e) {
                log.error("doc2Pdf error", e);
            }
        } catch (Exception e1) {
            log.error("doc2Pdf异常,", e1);
        } finally {
            //关闭文件原始文件输入流
            try {
                orginDocInputStream.close();
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //关闭目标文件输出流
            try {
                if (osll != null) {
                    osll.close();
                }
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //删除临时文件
//            if (osll != null) {
//                File file = new File(tempFilePath);
//                if (file.exists()) {
//                    file.delete();
//                }
//            }
        }
        return newPdfDocId;
    }

    /**
     * 创建临时目录
     *
     * @param tempPdfFilePath
     */
    private static void createTempFileFolder(String tempPdfFilePath) {
        //判断该目录是否存在
        File tempPdfFileDir = new File(tempPdfFilePath);
        if (!tempPdfFileDir.exists()) {
            tempPdfFileDir.mkdirs();
        }
    }

    /**
     * 加载授权
     */
    private static void loadLicense() {
        // 返回读取指定资源的输入流
        License license = new License();
        InputStream is = null;
        try {
            //String licenseFile = "/Users/<USER>/Documents/WEAVER/otherlib/aspose-words-14.9.0-jdk16-license.xml";
            String licenseFile = GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "lib" + File.separator + "aspose-words-14.9.0-jdk16-license.xml";
            //获取文件流
            is = Files.newInputStream(Paths.get(licenseFile));
            license.setLicense(is);
        } catch (Exception e) {
            log.error("loadLicense异常,", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("loadLicense释放资源异常", e);
                }
            }
        }
    }

    /**
     * PDF生成水印
     *
     * @param doc
     * @param watermarkText
     * @throws Exception
     * @throws
     * @Title: insertWatermarkText
     * @Description:
     * <AUTHOR>
     */
    private static void insertWatermarkText(Document doc, String watermarkText) throws Exception {
        // 获取页面尺寸
        PageSetup ps = doc.getFirstSection().getPageSetup();
        double pageWidth = ps.getPageWidth();
        double pageHeight = ps.getPageHeight();

        for (Section sect : doc.getSections()) {
            // 重要：先设置页眉页脚配置，再插入水印
            sect.getPageSetup().setDifferentFirstPageHeaderFooter(true);

            // 为每个页眉类型创建独立的水印段落
            Paragraph watermarkParaPrimary = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);
            Paragraph watermarkParaFirst = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);
            Paragraph watermarkParaEven = createWatermarkParagraph(doc, watermarkText, pageWidth, pageHeight);

            // 确保首页页眉存在并插入水印
            HeaderFooter firstHeader = sect.getHeadersFooters().getByHeaderFooterType(HeaderFooterType.HEADER_FIRST);
            if (firstHeader == null) {
                firstHeader = new HeaderFooter(sect.getDocument(), HeaderFooterType.HEADER_FIRST);
                sect.getHeadersFooters().add(firstHeader);
            }
            firstHeader.appendChild(watermarkParaFirst);

            // 插入到主页眉（普通页面）
            insertWatermarkIntoHeader(watermarkParaPrimary, sect, HeaderFooterType.HEADER_PRIMARY);

            // 插入偶数页页眉
            insertWatermarkIntoHeader(watermarkParaEven, sect, HeaderFooterType.HEADER_EVEN);
        }
        System.out.println("Watermark Set");
    }

    /**
     * 创建水印段落
     *
     * @param doc           文档对象
     * @param watermarkText 水印文本
     * @param pageWidth     页面宽度
     * @param pageHeight    页面高度
     * @return 包含水印的段落对象
     * @throws Exception
     */
    private static Paragraph createWatermarkParagraph(Document doc, String watermarkText, double pageWidth, double pageHeight) throws Exception {
        Paragraph watermarkPara = new Paragraph(doc);

        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
        //水印内容
        watermark.getTextPath().setText(watermarkText);
        //水印字体
        watermark.getTextPath().setFontFamily("宋体");
        //水印宽度
        watermark.setWidth(150);
        //水印高度
        watermark.setHeight(30);
        //旋转水印
        watermark.setRotation(-40);
        //水印颜色
        watermark.getFill().setColor(Color.lightGray);
        watermark.setStrokeColor(Color.lightGray);
        //将水印置于文字下方
        watermark.setBehindText(true);
        watermark.setWrapType(WrapType.NONE);
        watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
        watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);

        // 设置水印居中显示，确保在页面中央可见
        watermark.setVerticalAlignment(VerticalAlignment.CENTER);
        watermark.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 平铺水印
        for (double y = 0; y < pageHeight; y += 100) {
            for (double x = 0; x < pageWidth; x += 200) {
                Shape watermarkClone = (Shape) watermark.deepClone(true);
                watermarkClone.setLeft(x);
                watermarkClone.setTop(y);
                watermarkPara.appendChild(watermarkClone);
            }
        }

        return watermarkPara;
    }


    private static void insertWatermarkIntoHeader(Paragraph watermarkPara, Section sect, int headerType) throws Exception {
        HeaderFooter header = sect.getHeadersFooters().getByHeaderFooterType(headerType);
        if (header == null) {
            header = new HeaderFooter(sect.getDocument(), headerType);
            sect.getHeadersFooters().add(header);
        }
        header.appendChild(watermarkPara.deepClone(true));
    }

    /**
     * 在PDF文件中添加水印（使用Aspose.Words）
     *
     * @param sourcePdfPath 源PDF文件路径
     * @param targetPdfPath 目标PDF文件路径
     * @param watermarkText 水印文本
     * @param user          用户对象
     */
    private static void addPdfWatermark(String sourcePdfPath, String targetPdfPath, String watermarkText, User user) {
        try {
            // 使用Aspose.Words加载PDF文档
            Document pdfDoc = new Document(sourcePdfPath);

            // 为PDF文档的每个节添加水印
            for (Section section : pdfDoc.getSections()) {
                // 获取页面尺寸
                PageSetup pageSetup = section.getPageSetup();
                double pageWidth = pageSetup.getPageWidth();
                double pageHeight = pageSetup.getPageHeight();

                // 创建水印段落
                Paragraph watermarkPara = createPdfWatermarkParagraph(pdfDoc, watermarkText, pageWidth, pageHeight);

                // 确保页眉存在
                HeaderFooter header = section.getHeadersFooters().getByHeaderFooterType(HeaderFooterType.HEADER_PRIMARY);
                if (header == null) {
                    header = new HeaderFooter(pdfDoc, HeaderFooterType.HEADER_PRIMARY);
                    section.getHeadersFooters().add(header);
                }

                // 清除现有内容并添加水印
                header.removeAllChildren();
                header.appendChild(watermarkPara);

                // 如果有首页页眉，也添加水印
                HeaderFooter firstHeader = section.getHeadersFooters().getByHeaderFooterType(HeaderFooterType.HEADER_FIRST);
                if (firstHeader != null) {
                    firstHeader.removeAllChildren();
                    firstHeader.appendChild(watermarkPara.deepClone(true));
                }

                // 如果有偶数页页眉，也添加水印
                HeaderFooter evenHeader = section.getHeadersFooters().getByHeaderFooterType(HeaderFooterType.HEADER_EVEN);
                if (evenHeader != null) {
                    evenHeader.removeAllChildren();
                    evenHeader.appendChild(watermarkPara.deepClone(true));
                }
            }

            // 保存带水印的PDF
            pdfDoc.save(targetPdfPath, SaveFormat.PDF);

            log.info("PDF水印添加成功: " + targetPdfPath);
        } catch (Exception e) {
            log.error("PDF水印添加失败", e);
            // 如果添加水印失败，复制原文件
            try {
                Files.copy(Paths.get(sourcePdfPath), Paths.get(targetPdfPath));
            } catch (Exception copyException) {
                log.error("复制原PDF文件失败", copyException);
            }
        }
    }

    /**
     * 为PDF创建水印段落
     *
     * @param doc           文档对象
     * @param watermarkText 水印文本
     * @param pageWidth     页面宽度
     * @param pageHeight    页面高度
     * @return 包含水印的段落对象
     * @throws Exception
     */
    private static Paragraph createPdfWatermarkParagraph(Document doc, String watermarkText, double pageWidth, double pageHeight) throws Exception {
        Paragraph watermarkPara = new Paragraph(doc);

        Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
        //水印内容
        watermark.getTextPath().setText(watermarkText);
        //水印字体
        watermark.getTextPath().setFontFamily("宋体");
        //水印宽度
        watermark.setWidth(200);
        //水印高度
        watermark.setHeight(50);
        //旋转水印
        watermark.setRotation(-45);
        //水印颜色 - 设置为浅灰色，更适合PDF
        watermark.getFill().setColor(new Color(200, 200, 200));
        watermark.setStrokeColor(new Color(200, 200, 200));
        //将水印置于文字下方
        watermark.setBehindText(true);
        watermark.setWrapType(WrapType.NONE);
        watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
        watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);

        // 设置水印居中显示
        watermark.setVerticalAlignment(VerticalAlignment.CENTER);
        watermark.setHorizontalAlignment(HorizontalAlignment.CENTER);

        // 平铺水印 - 调整间距以适应PDF
        for (double y = 0; y < pageHeight; y += 120) {
            for (double x = 0; x < pageWidth; x += 250) {
                Shape watermarkClone = (Shape) watermark.deepClone(true);
                watermarkClone.setLeft(x);
                watermarkClone.setTop(y);
                watermarkPara.appendChild(watermarkClone);
            }
        }

        return watermarkPara;
    }
}