package com.engine.grtool.doc.util;

import com.aspose.words.*;
import com.aspose.words.Shape;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.GCONST;
import weaver.general.Util;
import weaver.hrm.User;

import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * @FileName WordsUtil.java
 * @Description words工具类，处理word文档、pdf文档这些
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/26
 */
public class WordsUtil {
    /**
     * 二开log类
     */
    private static final Logger log = LoggerFactory.getLogger(WordsUtil.class);


    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user) {
        return doc2Pdf(orginDocId, user, "");
    }

    /**
     * OA文档转pdf文档
     * 新文件的文件名和原来一致，后缀改为了pdf
     *
     * @param orginDocId
     * @param user
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(String orginDocId, User user, String waterStr) {
        DocFileInfo fileInfo = DocUtil.getFirstFileWithInputStream(orginDocId);
        String fileName = Util.null2String(fileInfo.getFileName());
        String fileNameWithOutExt = fileName;
        if (fileName.contains(".")) {
            fileNameWithOutExt = fileName.substring(0, fileName.lastIndexOf("."));
        }
        String newFileName = fileNameWithOutExt + ".pdf";
        InputStream orginDocInputStream = fileInfo.getFileInputStream();
        if (orginDocInputStream != null) {
            return doc2Pdf(orginDocInputStream, newFileName, user, fileInfo.getSeccategory(), waterStr);
        }
        return -1;
    }

    /**
     *
     * @param orginDocInputStream
     * @param pdfFileName
     * @param user
     * @param newDocCategoryId
     * @param waterStr
     * @return
     */
    public static int doc2Pdf(InputStream orginDocInputStream, String pdfFileName, User user, int newDocCategoryId, String waterStr) {
        int newPdfDocId = -1;
        InputStream newFileIs;
        String tempFilePath = "";
        Document docss;
        FileOutputStream osll = null;
        try {
            //创建临时文件目录
            String tempPdfFileFolder = GCONST.getRootPath() + File.separator + "grsd" + File.separator + "temp";
            createTempFileFolder(tempPdfFileFolder);
            //创建历史文件
            String uuid = UUID.randomUUID().toString();
            tempFilePath = tempPdfFileFolder + File.separator + uuid + ".pdf";
            //创建文件输出流
            osll = new FileOutputStream(tempFilePath);
            //load授权
            loadLicense();
            try {
                docss = new Document(orginDocInputStream);

                // 如果需要添加水印，在Word文档中添加水印
                if (!Util.null2String(waterStr).isEmpty()) {
                    //插入水印到Word文档
                    insertWatermarkTextImproved(docss, waterStr);

                    // 保存加了水印的Word文档，方便排查
                    String tempWordFilePath = tempFilePath.replace(".pdf", "_watermark.docx");
                    docss.save(tempWordFilePath, SaveFormat.DOCX);
                    log.info("已保存带水印的Word文档用于排查: " + tempWordFilePath);
                }

                // 转换为PDF
                docss.save(osll, SaveFormat.PDF);

                //获取临时文件的输入流
                newFileIs = Files.newInputStream(Paths.get(tempFilePath));
                //根据临时文件输出流，创建OA文档
                newPdfDocId = DocUtil.createDocWithFile(newFileIs, user, pdfFileName, newDocCategoryId);
            } catch (Exception e) {
                log.error("doc2Pdf error", e);
            }
        } catch (Exception e1) {
            log.error("doc2Pdf异常,", e1);
        } finally {
            //关闭文件原始文件输入流
            try {
                orginDocInputStream.close();
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //关闭目标文件输出流
            try {
                if (osll != null) {
                    osll.close();
                }
            } catch (IOException e) {
                log.error("doc2Pdf释放资源异常", e);
            }
            //删除临时文件
//            if (osll != null) {
//                File file = new File(tempFilePath);
//                if (file.exists()) {
//                    file.delete();
//                }
//            }
        }
        return newPdfDocId;
    }

    /**
     * 创建临时目录
     *
     * @param tempPdfFilePath
     */
    private static void createTempFileFolder(String tempPdfFilePath) {
        //判断该目录是否存在
        File tempPdfFileDir = new File(tempPdfFilePath);
        if (!tempPdfFileDir.exists()) {
            tempPdfFileDir.mkdirs();
        }
    }

    /**
     * 加载授权
     */
    private static void loadLicense() {
        // 返回读取指定资源的输入流
        License license = new License();
        InputStream is = null;
        try {
            //String licenseFile = "/Users/<USER>/Documents/WEAVER/otherlib/aspose-words-14.9.0-jdk16-license.xml";
            String licenseFile = GCONST.getRootPath() + File.separator + "WEB-INF" + File.separator + "lib" + File.separator + "aspose-words-14.9.0-jdk16-license.xml";
            //获取文件流
            is = Files.newInputStream(Paths.get(licenseFile));
            license.setLicense(is);
        } catch (Exception e) {
            log.error("loadLicense异常,", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    log.error("loadLicense释放资源异常", e);
                }
            }
        }
    }


    /**
     * 改进的水印插入方法
     *
     * @param doc           Word文档对象
     * @param watermarkText 水印文本
     * @throws Exception
     */
    private static void insertWatermarkTextImproved(Document doc, String watermarkText) throws Exception {
        log.info("开始插入改进的水印: " + watermarkText);

        // 为文档的每个节添加水印
        for (Section section : doc.getSections()) {
            // 获取页面设置
            PageSetup pageSetup = section.getPageSetup();
            double pageWidth = pageSetup.getPageWidth();
            double pageHeight = pageSetup.getPageHeight();

            log.info("处理节，页面尺寸: " + pageWidth + " x " + pageHeight);

            // 方法1：在页眉中添加水印
            section.getPageSetup().setDifferentFirstPageHeaderFooter(true);
            createAndAddWatermarkToHeader(section, HeaderFooterType.HEADER_PRIMARY, doc, watermarkText, pageWidth, pageHeight);
            createAndAddWatermarkToHeader(section, HeaderFooterType.HEADER_FIRST, doc, watermarkText, pageWidth, pageHeight);
            createAndAddWatermarkToHeader(section, HeaderFooterType.HEADER_EVEN, doc, watermarkText, pageWidth, pageHeight);

            // 方法2：在文档主体中添加浮动水印（确保在图片上方）
            addFloatingWatermarkToBody(section, doc, watermarkText, pageWidth, pageHeight);
        }

        log.info("改进的水印插入完成");
    }

    /**
     * 为指定的页眉类型创建并添加水印
     *
     * @param section       文档节
     * @param headerType    页眉类型
     * @param doc           文档对象
     * @param watermarkText 水印文本
     * @param pageWidth     页面宽度
     * @param pageHeight    页面高度
     * @throws Exception
     */
    private static void createAndAddWatermarkToHeader(Section section, int headerType, Document doc,
                                                      String watermarkText, double pageWidth, double pageHeight) throws Exception {
        // 确保页眉存在
        HeaderFooter header = section.getHeadersFooters().getByHeaderFooterType(headerType);
        if (header == null) {
            header = new HeaderFooter(doc, headerType);
            section.getHeadersFooters().add(header);
        }

        // 清除现有内容
        header.removeAllChildren();

        // 创建水印段落
        Paragraph watermarkPara = new Paragraph(doc);

        // 添加平铺的水印
        for (double y = 0; y < pageHeight; y += 120) {
            for (double x = 0; x < pageWidth; x += 250) {
                Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
                watermark.getTextPath().setText(watermarkText);
                watermark.getTextPath().setFontFamily("宋体");
                watermark.setWidth(100);
                watermark.setHeight(20);
                watermark.setRotation(-45);
                watermark.getFill().setColor(new Color(180, 180, 180));
                watermark.setStrokeColor(new Color(180, 180, 180));

                // 关键设置：确保水印显示在图片上方
                watermark.setBehindText(false);  // 不在文本后面
                watermark.setWrapType(WrapType.TOP_BOTTOM);  // 改为TOP_BOTTOM包装类型
                watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
                watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);

                // 设置最高的Z-order，确保在最上层
                watermark.setZOrder(Integer.MAX_VALUE);

                // 设置为浮动形状，确保在所有内容之上
                watermark.setAnchorLocked(false);
                watermark.setAllowOverlap(true);

                watermark.setLeft(x);
                watermark.setTop(y);

                watermarkPara.appendChild(watermark);
            }
        }

        // 添加到页眉
        header.appendChild(watermarkPara);

        String headerTypeName = getHeaderTypeName(headerType);
        log.info("已添加水印到: " + headerTypeName);
    }

    /**
     * 在文档主体中添加浮动水印
     *
     * @param section       文档节
     * @param doc           文档对象
     * @param watermarkText 水印文本
     * @param pageWidth     页面宽度
     * @param pageHeight    页面高度
     * @throws Exception
     */
    private static void addFloatingWatermarkToBody(Section section, Document doc, String watermarkText,
                                                   double pageWidth, double pageHeight) throws Exception {
        // 获取第一个段落，如果没有则创建一个
        Body body = section.getBody();
        if (body.getChildNodes().getCount() == 0) {
            body.appendChild(new Paragraph(doc));
        }

        Paragraph firstPara = (Paragraph) body.getFirstChild();

        // 创建平铺的浮动水印
        for (double y = 50; y < pageHeight - 50; y += 120) {
            for (double x = 50; x < pageWidth - 50; x += 250) {
                Shape watermark = new Shape(doc, ShapeType.TEXT_PLAIN_TEXT);
                watermark.getTextPath().setText(watermarkText);
                watermark.getTextPath().setFontFamily("宋体");
                watermark.setWidth(100);
                watermark.setHeight(30);
                watermark.setRotation(-45);
                watermark.getFill().setColor(new Color(180, 180, 180));
                watermark.setStrokeColor(new Color(180, 180, 180));

                // 关键设置：确保水印浮动在所有内容之上
                watermark.setBehindText(false);
                watermark.setWrapType(WrapType.NONE);
                watermark.setRelativeHorizontalPosition(RelativeHorizontalPosition.PAGE);
                watermark.setRelativeVerticalPosition(RelativeVerticalPosition.PAGE);
                watermark.setZOrder(Integer.MAX_VALUE);
                watermark.setAnchorLocked(false);
                watermark.setAllowOverlap(true);

                // 设置为绝对定位
                watermark.setLeft(x);
                watermark.setTop(y);

                // 将水印添加到第一个段落
                firstPara.appendChild(watermark);
            }
        }

        log.info("已在文档主体添加浮动水印");
    }

    /**
     * 获取页眉类型名称（用于日志）
     *
     * @param headerType 页眉类型
     * @return 页眉类型名称
     */
    private static String getHeaderTypeName(int headerType) {
        switch (headerType) {
            case HeaderFooterType.HEADER_PRIMARY:
                return "主页眉";
            case HeaderFooterType.HEADER_FIRST:
                return "首页页眉";
            case HeaderFooterType.HEADER_EVEN:
                return "偶数页页眉";
            default:
                return "未知页眉类型";
        }
    }
}