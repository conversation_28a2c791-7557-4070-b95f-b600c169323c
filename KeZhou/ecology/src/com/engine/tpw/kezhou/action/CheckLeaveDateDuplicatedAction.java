package com.engine.tpw.kezhou.action;

import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.workflow.BaseSDAction;
import com.engine.sd2.db.util.DBUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * @FileName TestAction CheckLeaveDateDuplicated
 * @Description 根据主表的“填报人“、明细表的”日期“。查询是否有符合条件的请假流程数据。
 * 如果有（请假天数=1），则在流程提交的时候，进行拦截、并提示：工时日期当天，已提交请假流程，不可再提交工时填报。（类似流程提交校验）
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/8/1
 */
@Getter
@Setter
public class CheckLeaveDateDuplicatedAction extends BaseSDAction implements Action {
    //使用二开log类
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    /**
     * action执行入口
     *
     * @param requestInfo
     * @return
     */
    @Override
    public String execute(RequestInfo requestInfo) {
        //SDAction初始化
        initAction(requestInfo, this.getClass(), null, "工时填报流程提交校验");
        if (getActionError().isEmpty()) {
            try {
                if (getActionError().isEmpty()) {
                    //执行业务逻辑
                    execuetMy();
                }
            } catch (Exception e) {
                getThreadLocalBaseParam().actionError = "action执行异常:" + SDUtil.getExceptionDetail(e);
                appendLog(getActionError());
                log.error("action执行异常:", e);
            } finally {
                appendLog(this.getClass().getName() + "---END---requestid:" + requestInfo.getRequestid());
            }
        }
        return actionReturn();
    }
    /**
     * 执行业务逻辑
     */
    private void execuetMy() {
        try {
            appendLog("execuetMy start");
            Map<String, String> wfMainData = getThreadLocalBaseParam().wfMainData;
            Map<Integer, List<Map<String, String>>> detailData = getThreadLocalBaseParam().actionInfo.getDetailData();
            //获取主表中"填报人"字段值
            String sqr = Util.null2String(wfMainData.get("sqr"));
            StringBuilder errMsgBuilder = new StringBuilder();
            if (StringUtils.isBlank(sqr)) {
                getThreadLocalBaseParam().actionError = "流程填报人未填写，不允许提交！";
            } else {
                //获取相关明细中的日期字段
                ArrayList<String> list = new ArrayList<>();
                RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                String sql = "select fromdate from kq_flow_split_leave where duration = 1.0 and  (leavebackrequestid = '' or leavebackrequestid is null) and resourceid = ? ";
                recordSet.executeQuery(sql, sqr);
                while (recordSet.next()) {
                    list.add(recordSet.getString("fromdate"));
                }
                List<Map<String, String>> maps = detailData.get(1);
                if (!maps.isEmpty()) {
                    for (int i = 0; i < maps.size(); i++) {
                        Map<String, String> stringStringMap = maps.get(i);
                        String rq = Util.null2String(stringStringMap.get("rq"));
                        if (list.contains(rq)) {
                            errMsgBuilder.append(rq).append("，");
                        }
                    }
                }
            }
            String errMsg = errMsgBuilder.toString();
            if (StringUtils.isNotBlank(errMsg)) {
                getThreadLocalBaseParam().actionError = "申请中的" + errMsg + "已提交请假流程，不可再提交工时填报。";
            }
        } catch (Exception e) {
            getThreadLocalBaseParam().actionError = "execuetMy异常：" + SDUtil.getExceptionDetail(e);
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        appendLog("execuetMy end");
    }
}
