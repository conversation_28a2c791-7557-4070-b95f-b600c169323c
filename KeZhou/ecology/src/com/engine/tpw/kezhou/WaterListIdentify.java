package com.engine.tpw.kezhou;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.EncryptUtil;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.doc.DocUtil;
import com.engine.parent.doc.dto.DocFileInfo;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.mzlion.easyokhttp.http.InputStreamRequestBody;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.general.Util;
import weaver.hrm.User;
import weaver.soa.workflow.request.RequestInfo;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
    * @FileName ModeExpandTemplate1
    * @Description 水单识别 waterListIdentify
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/7/29
    */
public class WaterListIdentify extends AbstractModeExpandJavaCodeNew {
    /**
     * 执行模块扩展动作
     *
     * @param param param包含(但不限于)以下数据
     *              user 当前用户
     *              importtype 导入方式(仅在批量导入的接口动作会传输) 1 追加，2覆盖,3更新，获取方式(int)param.get("importtype")
     *              导入链接中拼接的特殊参数(仅在批量导入的接口动作会传输)，比如a=1，可通过param.get("a")获取参数值
     *              页面链接拼接的参数，比如b=2,可以通过param.get("b")来获取参数
     * @return
     */
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    public Map<String, String> doModeExpand(Map<String, Object> param) {
        Map<String, String> result = new HashMap<String, String>();
        try {
            User user = (User) param.get("user");
            int billid = -1;//数据id
            int modeid = -1;//模块id
            RequestInfo requestInfo = (RequestInfo) param.get("RequestInfo");
            if (requestInfo != null) {
                billid = Util.getIntValue(requestInfo.getRequestid());
                modeid = Util.getIntValue(requestInfo.getWorkflowid());
                if (billid > 0 && modeid > 0) {
                    //------请在下面编写业务逻辑代码------
                    String tableName = ModuleDataUtil.getModuleNameById(String.valueOf(modeid));
                    log.info("WaterListIdentify -- start");
                    log.info("billid:" + billid + ",modeid:" + modeid + ",tableName:" + tableName);
                    String fj = "";
                    RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                    recordSet.executeQuery("select * from " + tableName + " where id =" + billid);
                    if (recordSet.next()) {
                        fj = recordSet.getString("sdfj");
                    }
                    log.info("sdfj:" + fj);
                    if (StringUtils.isNotBlank(fj)) {
                        List<DocFileInfo> listDocFileInfo = DocUtil.getDocFileInfoByDocIds(fj);
                        if (!listDocFileInfo.isEmpty()) {
                            DocFileInfo docFileInfo = listDocFileInfo.get(0);
                            log.info("docFileInfo:" + JSONObject.toJSONString(docFileInfo));
                            String fileName = docFileInfo.getFileName();
                            String fileid = docFileInfo.getFileid();
                            InputStream fileInputStream = DocUtil.getFileInputStream(Integer.parseInt(fileid));
                            //appId
                            String appId = "5kycr197";
                            //时间戳
                            String timestamp = String.valueOf(System.currentTimeMillis());
                            //appSecret
                            String appSecret = "7eae9a1b979899ce03f8685c596a6cb9";
                            String md5Str = appId + timestamp + appSecret;
                            log.info("md5Str:" + md5Str);
                            String sign = EncryptUtil.md5(md5Str);
                            log.info("sign:" + sign);
                            OkHttpClient client = new OkHttpClient().newBuilder()
                                    .build();
                            //构建请求体
                            RequestBody body = new MultipartBody.Builder()
                                    .setType(MultipartBody.FORM)
                                    .addFormDataPart("file", fileName,
                                            new InputStreamRequestBody(fileInputStream, MediaType.parse("text/plain")))
                                    .addFormDataPart("serial", "XM00001")
                                    .build();

                            Request request = new Request.Builder()
                                    .url("https://cloud.qianliling.com/spec-apps/api/smart/model/v1/call")
                                    .method("POST", body)
                                    .addHeader("appId", appId)
                                    .addHeader("timestamp", timestamp)
                                    .addHeader("sign", sign)
                                    .build();
                            try (Response response = client.newCall(request).execute()) {
                                if (response.code() == 200) { // 完全匹配图片中的成功状态码
                                    String responseBody = response.body().string();
                                    log.info("水单识别 responseBody ："+responseBody);
                                    if(StringUtils.isNotBlank(responseBody)){
                                        JSONObject jsonObject = JSONObject.parseObject(responseBody);
                                        if("0000".equals(Util.null2String(jsonObject.get("code")))){
                                            JSONObject data = (JSONObject) jsonObject.get("data");
                                            log.info("data:" + data.toJSONString());
                                            JSONObject writeData = (JSONObject) data.get("result");
                                            log.info("writeData:" + JSONObject.toJSONString(writeData));
                                            JSONObject singleField = (JSONObject) writeData.get("singleField");
                                            String sjmc = Util.null2String(singleField.get("sjmc"));
                                            String rq = Util.null2String(singleField.get("rq"));
                                            String sj = Util.null2String(singleField.get("sj"));
                                            if(StringUtils.isNotBlank(sj) ){
                                                sj = sj.substring(0,5);
                                            }
                                            String rs = Util.null2String(singleField.get("rs"));
                                            String je = Util.null2String(singleField.get("je"));
                                            recordSet.executeUpdate("UPDATE "+tableName+" set sjmc = ?,sdrq = ?,sdsj = ?,rs = ?,sdje = ? WHERE id = ?", sjmc, rq, sj, rs, je,billid);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            log.info("WaterListIdentify -- end");
        } catch (Exception e) {
            log.info("WaterListIdentify -- exception:" + SDUtil.getExceptionDetail(e));
            result.put("errmsg", SDUtil.getExceptionDetail(e) );
            result.put("flag", "false");
        }finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return result;
    }

}