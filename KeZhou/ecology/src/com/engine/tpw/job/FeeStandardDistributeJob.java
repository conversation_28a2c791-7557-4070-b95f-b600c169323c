package com.engine.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.dto.ModuleUpdateBean;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.sd2.functionlog.bean.SDLog;
import com.engine.sd2.functionlog.util.SDLogUtil;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * @FileName FeeStandardDistributeJob
 * @Description 费用标准分发至人员维度
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/7/14
 */
public class FeeStandardDistributeJob extends BaseCronJob {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private SDLogUtil sdLogUtil;
    private SDLog sdLog; // 二开日志bean
    private String errMsg;
    private JSONArray hrms;
    private ArrayList<String> fields;
    private HashMap<String, JSONObject> levelMap;
    private HashMap<String, String> hrmMap;

    @Override
    public void execute() {
        try {
            _init();
            if (StringUtils.isNotBlank(errMsg)) {
                return;
            }
            appendLog("FeeStandardDistributeJob----Start");
            executeMy();
        } catch (Exception e) {
            appendLog("execute:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
            appendLog("FeeStandardDistributeJob----END");
//            afterExecute();
        }
    }

    private void executeMy() {
        try {
            appendLog("executeMy---start");
            if (hrms == null || hrms.isEmpty()) {
                appendLog("executeMy---hrms is null");
                return;
            }
            for (int i = 0; i < hrms.size(); i++) {
                JSONObject hrm = (JSONObject) hrms.get(i);
                //人员id
                String hrmid = Util.null2String(hrm.get("ID"));
                String status = Util.null2String(hrm.get("STATUS"));
                String jb = Util.null2String(hrm.get("field8"));
                if (levelMap.containsKey(jb)) {
                    JSONObject jsonObject = levelMap.get(jb);
                    //判断费用标准-员工维度是否已经有该员工

                    if ("4".equals(status) || "5".equals(status) || "6".equals(status) || "7".equals(status)) {
                        //删除
                        if (hrmMap.containsKey(hrmid)) {
                            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
                            recordSet.executeUpdate("delete from uf_fybz where ygxm = ?", hrmid);
                        }
                    } else if (hrmMap.containsKey(hrmid)) {
                        //更新
                        List<Object> value = assemblyParameters(hrm, jsonObject);
                        value.add(hrmMap.get(hrmid));
                        List<List<Object>> values = new ArrayList<>();
                        values.add(value);
                        ModuleUpdateBean mb = new ModuleUpdateBean();
                        mb.setTableName("uf_fybz")
                                .setFields(fields)
                                .setValues(values)
                                .setUpdater(1)
                                .setValue(value);
                        ModuleDataUtil.update(mb);
                    } else {
                        //新增
                        //组装参数
                        List<Object> value = assemblyParameters(hrm, jsonObject);
                        ModuleInsertBean mb = new ModuleInsertBean();
                        mb.setTableName("uf_fybz")
                                .setFields(fields)
                                .setValue(value)
                                .setCreatorId(1)
                                .setModuleId(ModuleDataUtil.getModuleIdByName("uf_fybz"));
                        ModuleDataUtil.insertOne(mb);
                    }
                }
            }
        } catch (Exception e) {
            errMsg = "executeMy:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        } finally {
            appendLog("executeMy---end");
        }
    }

    private List<Object> assemblyParameters(JSONObject hrm, JSONObject jsonObject) {
        appendLog("assemblyParameters----start");
        appendLog("assemblyParameters----hrm : "+JSONObject.toJSONString(hrm));
        appendLog("assemblyParameters----jsonObject : "+JSONObject.toJSONString(jsonObject));
        ArrayList<Object> value = new ArrayList<>();
        try {
            value.add(Util.null2String(hrm.get("ID")));
            value.add(Util.null2String(hrm.get("WORKCODE")));
            value.add(Util.null2String(hrm.get("DEPARTMENTID")));
            value.add(Util.null2String(hrm.get("JOBTITLE")));
            value.add(Util.null2String(hrm.get("field8")));
            value.add(Util.null2String(jsonObject.get("zsedal")));
            value.add(Util.null2String(jsonObject.get("zsedbl")));
            value.add(Util.null2String(jsonObject.get("zsedcl")));
            value.add(Util.null2String(jsonObject.get("zdcfjlp")));
            value.add(Util.null2String(jsonObject.get("zdcfymry")));
        } catch (Exception e) {
            appendLog("assemblyParameters:" + SDUtil.getExceptionDetail(e));
        }
        appendLog("assemblyParameters----value : " + value);
        appendLog("assemblyParameters----start");
        return value;
    }

    private void _init() {
        try {
            errMsg = "";
            sdLogUtil = new SDLogUtil();
            hrms = new JSONArray();
            levelMap = new HashMap<>();
            hrmMap = new HashMap<>();
            //初始化日志bean
            sdLog = new SDLog(1,
                    this.getClass().getSimpleName(),
                    this.getClass().getName(),
                    SDLog.TYPE_JOB,
                    "费用标准分发至人员维度");
            sdLog.setRelate_module("定时任务");
            sdLog.setRelate_table("");
            sdLog.setRelate_dataid("");
            fields = new ArrayList<>();
            initFields();
            appendLog("fields " + fields);
            //查询所有的人员信息
            queryHrmResourceInfo();
            //获取所有的费用标准-级别维度（uf_fybzjb）信息
            queryfybzjbInfo();
            //获取所有的费用标准-员工维度（uf_fybz）信息
            queryfybzInfo();
        } catch (Exception e) {
            errMsg = "FeeStandardDistributeJob----_init:" + SDUtil.getExceptionDetail(e);
            sdLog.setError(errMsg);
            appendLog(errMsg);
        }
    }

    private void initFields() {
        fields.add("ygxm");
        fields.add("yggh");
        fields.add("bm");
        fields.add("gw");
        fields.add("ygjb");
        fields.add("zsedal");
        fields.add("zsedbl");
        fields.add("zsedcl");
        fields.add("zdcfjlp");
        fields.add("zdcfymry");
    }

    private void queryfybzInfo() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select id,ygxm from uf_fybz");
            while (recordSet.next()) {
                hrmMap.put(recordSet.getString("ygxm"), recordSet.getString("id"));
            }
            appendLog("queryfybzInfo---hrmMap " + JSONObject.toJSONString(hrmMap));
        } catch (Exception e) {
            appendLog("queryfybzInfo Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    private void queryfybzjbInfo() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select * from uf_fybzjb");
            JSONArray jsonList = QueryUtil.getJSONList(recordSet);
            for (int i = 0; i < jsonList.size(); i++) {
                JSONObject json = (JSONObject) jsonList.get(i);
                String jb = Util.null2String(json.get("jb"));
                if (StringUtils.isNotBlank(jb)) {
                    levelMap.put(jb, json);
                }
            }
            appendLog("queryfybzjbInfo---levelMap " + JSONObject.toJSONString(levelMap));
        } catch (Exception e) {
            appendLog("queryfybzjbInfo Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    private void queryHrmResourceInfo() {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            recordSet.executeQuery("select hr.*,cf.field8 from hrmresource hr left join cus_fielddata cf on hr.id =cf.id where cf.scope='HrmCustomFieldByInfoType' and cf.scopeid= -1 ");
            hrms = QueryUtil.getJSONList(recordSet);
            appendLog("queryHrmResourceInfo---hrms " + JSONObject.toJSONString(hrms));
        } catch (Exception e) {
            appendLog("queryHrmResourceInfo Exception:" + SDUtil.getExceptionDetail(e));
        }
    }

    private void appendLog(String logMsg) {
        logger.info(logMsg);
        sdLogUtil.appendLog(logMsg);
    }

    private void afterExecute() {
        try {
            appendLog("afterExecute----Start");
            //清除RecordSet
            DBUtil.clearThreadLocalRecordSet();
            //插入二开日志
            ModuleResult moduleResult = SDLog.saveLog(sdLog, sdLogUtil.getFullLog());
            appendLog("插入二开日志 moduleResult " + JSONObject.toJSONString(moduleResult));
        } catch (Exception e) {
            appendLog("afterExecute:" + SDUtil.getExceptionDetail(e));
        } finally {
            appendLog("afterExecute----end");
        }
    }

}
