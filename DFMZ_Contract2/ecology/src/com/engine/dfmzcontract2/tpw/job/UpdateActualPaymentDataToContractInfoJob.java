package com.engine.dfmzcontract2.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.dfmzcontract2.tpw.job.bean.HtxxDetailsLog;
import com.engine.dfmzcontract2.tpw.job.bean.HtxxMainLog;
import com.engine.dfmzcontract2.tpw.job.util.HTSFKUtil;
import com.engine.parent.common.util.SDUtil;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
    * @FileName UpdateActualPaymentDataToContractInfoJob
    * @Description 客户需要在合同信息页面查看到合同实付款数据合计，故需要通过定时任务更新汇总数据
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/6/11
    */
@Getter
@Setter
public class UpdateActualPaymentDataToContractInfoJob extends BaseCronJob {

    private BaseBean bb = new BaseBean();
    
    private final RecordSet rs = new RecordSet();
    /**
     * 交换中心更新OA面单日志
     */
    private String logTableName;

    @Override
    public void execute() {
        String errMsg = "";
        bb.writeLog("请求参数logTableName:"+logTableName);
        bb.writeLog("UpdateActualPaymentDataToContractInfoJob----START");
        try {
            //日志主表对象
            HtxxMainLog mainLog = new HtxxMainLog();
            //日志明细表集合
            List<HtxxDetailsLog> detailLogs = new ArrayList<>();
            // uf_htxx表更新之前的旧数据
            JSONArray oldDatas = new JSONArray();
            // uf_htxx表更新之后的新数据
            JSONArray newDatas = new JSONArray();
            //uf_htxx表总记录数
            int sumCounts = 0;
            //uf_htxx表更新的数量
            int updateCounts = 0;
            // 查询更新之前的uf_htxx数据
            String oldSql = "select * from uf_htxx";
            rs.execute(oldSql);
            sumCounts = rs.getCounts();

            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("htglbh", rs.getString("htglbh"));
                jsonObject.put("sfkjehj", rs.getString("sfkjehj"));
                oldDatas.add(jsonObject);
            }
            bb.writeLog("uf_htxx表更新之前的旧数据"+JSONObject.toJSONString(oldDatas));
            // 更新数据
            String updateSql = "UPDATE uf_htxx SET uf_htxx.sfkjehj = (SELECT SUM(sfk.sfje) FROM uf_sfkxx sfk WHERE sfk.htglbh = uf_htxx.htglbh) WHERE EXISTS (SELECT 1 FROM uf_sfkxx sfk WHERE sfk.htglbh = uf_htxx.htglbh)";
            rs.execute(updateSql);
            // 查询更新之后的uf_htxx数据
            String newSql = "select * from uf_htxx";
            rs.execute(newSql);
            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("htglbh", rs.getString("htglbh"));
                jsonObject.put("sfkjehj", rs.getString("sfkjehj"));
                newDatas.add(jsonObject);
            }
            bb.writeLog("uf_htxx表更新之前的新数据"+JSONObject.toJSONString(newDatas));
            // 对比新旧数据，提取要同步到日志表中的数据
            if (!oldDatas.isEmpty() && !newDatas.isEmpty()) {
                for (int i = 0; i < oldDatas.size(); i++) {
                    JSONObject oldData = oldDatas.getJSONObject(i);
                    String htglbh = oldData.getString("htglbh");
                    String oldSfkjehj = oldData.getString("sfkjehj");
                    Iterator<Object> iterator = newDatas.iterator();
                    while (iterator.hasNext()) {
                        JSONObject newData = (JSONObject) iterator.next();
                        String newhtglbh = newData.getString("htglbh");
                        if (StringUtils.isNotBlank(htglbh) && htglbh.equals(newhtglbh)) {
                            String newSfkjehj = newData.getString("sfkjehj");
                            if(oldSfkjehj == null && StringUtils.isNotBlank(newSfkjehj)){
                                // 同步变化的数据到日志表的明细数据
                                HtxxDetailsLog detailLog = new HtxxDetailsLog();
                                detailLog.setHth(htglbh);
                                detailLog.setJz(oldSfkjehj);
                                detailLog.setXz(newSfkjehj);
                                //变化的数据
                                updateCounts++;
                                detailLogs.add(detailLog);
                            }else if(StringUtils.isBlank(oldSfkjehj) && StringUtils.isBlank(newSfkjehj)){

                            }else if (oldSfkjehj!=null && !oldSfkjehj.equals(newSfkjehj)) {
                                // 同步变化的数据到日志表的明细数据
                                HtxxDetailsLog detailLog = new HtxxDetailsLog();
                                detailLog.setHth(htglbh);
                                detailLog.setJz(oldSfkjehj);
                                detailLog.setXz(newSfkjehj);
                                //变化的数据
                                updateCounts++;
                                detailLogs.add(detailLog);
                            }
                            iterator.remove();
                            break;
                        }
                    }
                }
            } else {
                // uf_htxx表中无数据，不会执行更新操作
                bb.writeLog("uf_htxx table is empty, no update performed.");
            }

            //开始插入合同信息实付款金额合计日志
            mainLog.setHtxxsl(sumCounts);
            mainLog.setHtxxbcgxsl(updateCounts);
            bb.writeLog("本次更新的主表数据"+JSONObject.toJSONString(mainLog));
            bb.writeLog("本次更新的明细表数据"+JSONObject.toJSONString(detailLogs));
            HTSFKUtil.insertLog(logTableName,mainLog,detailLogs);
        } catch (Exception e) {
            bb.writeLog("UpdateActualPaymentDataToContractInfoJob----ERROR" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("UpdateActualPaymentDataToContractInfoJob----END");
    }

}
