package com.engine.dfmzcontract2.tpw.job.util;

import com.engine.dfmzcontract2.tpw.job.bean.HtxxDetailsLog;
import com.engine.dfmzcontract2.tpw.job.bean.HtxxMainLog;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import weaver.general.BaseBean;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
    * @FileName HTXXUtil
    * @Description 合同实付款日志工具类
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/6/11
    */
public class HTSFKUtil {

    private static final BaseBean bb = new BaseBean();


    /**
     * 将数据日志插入
     *
     * @param log
     * @return
     */
    public static String insertLog(String logTableName , HtxxMainLog log, List<HtxxDetailsLog> detailList) {
        String errorMsg = "";
        String fieldName;
        Object fieldValue;
        bb.writeLog("HTSFKUtil insertData ---START");

        try {
            Class<?> clazz = HtxxMainLog.class;
            Field[] fields = clazz.getDeclaredFields();
            List<String> insertFields = new ArrayList<>();
            List<Object> value = new ArrayList<>();
            for (Field field : fields) {
                field.setAccessible(true); // 设置可访问私有字段
                //字段名
                fieldName = field.getName();
                //获取字段值
                fieldValue = field.get(log);
                insertFields.add(fieldName);
                value.add(fieldValue);
            }
            int moduleId = ModuleDataUtil.getModuleIdByName(logTableName);
            ModuleInsertBean mb = new ModuleInsertBean();
            mb.setTableName(logTableName)
                    .setFields(insertFields)
                    .setValue(value)
                    .setCreatorId(1)
                    .setModuleId(moduleId);
            ModuleResult mrMain = ModuleDataUtil.insertOne(mb);
            if (mrMain.isSuccess()) {

                errorMsg = insertDetailLog(logTableName,detailList, mrMain.getBillid());
                //插入明细表
            } else {
                errorMsg = mrMain.getErroMsg();
            }
        } catch (IllegalAccessException e) {
            bb.writeLog("HTSFKUtil insertData error :" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("HTSFKUtil insertData ---END");
        return errorMsg;
    }

    /**
     * 记录明细日志
     *
     * @param
     * @param mainLogId
     * @return
     */
    public static String insertDetailLog(String logTableName ,List<HtxxDetailsLog> detailList, int mainLogId) {
        bb.writeLog("HTSFKUtil insertDetailLog ---START");
        String errorMsg;
        String fieldName;
        Object fieldValue;
        try {
            Class<?> clazz = HtxxDetailsLog.class;
            Field[] fields = clazz.getDeclaredFields();
            List<Field> list = Arrays.asList(fields);
            List<String> insertFields = new ArrayList<>();
            List<List<Object>> values = new ArrayList<>();

            for (Field field : list) {
                field.setAccessible(true); // 设置可访问私有字段
                fieldName = field.getName();
                insertFields.add(fieldName);
            }
            insertFields.add("mainid");

            for (int i = 0; i < detailList.size(); i++) {
                List<Object> value = new ArrayList<>();
                HtxxDetailsLog log = detailList.get(i);
                for (Field field : list) {
                    field.setAccessible(true); // 设置可访问私有字段
                    fieldValue = field.get(log);
                    value.add(fieldValue);
                }
                value.add(mainLogId);
                values.add(value);
            }

            ModuleInsertBean mb = new ModuleInsertBean();
            String detailTableName = logTableName+"_dt1";
            mb.setTableName(detailTableName)
                    .setFields(insertFields)
                    .setValues(values);
            ModuleResult mr = ModuleDataUtil.insertDetailAc(mb);
            errorMsg = mr.getErroMsg();
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("HTSFKUtil insertDetailLog error :" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("HTSFKUtil insertDetailLog ---END");
        return errorMsg;
    }
}
