@media print, screen and (min-width: 768px) {
    /*输入框加圆角*/
    .wea-associative-search {
        border-radius: 4px;
    }

    .wf-input {
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 6px;
        height: 30px;
        font-size: 14px !important;
        font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Microsoft YaHei, Source Han Sans SC, Noto Sans CJK SC, WenQuanYi Micro Hei, sans-serif !important;
    }

    .wea-select .ant-select-selection {
        border-radius: 4px;
    }

    .wf-form-textarea .ant-input {
        border-radius: 4px;
    }

    .wea-associative-search .ant-select-selection--multiple .ant-select-selection__choice {
        padding: 6px 0 0 8px;
    }

    .wea-associative-search .ant-select-selection--multiple .ant-select-selection__choice__remove {
        font-size: 16px !important;
    }


    /*表头*/
    table.excelMainTable td {
        background-color: #fff !important;
    }

    table.excelMainTable td span,
    table.excelMainTable td div {

        font-size: 14px !important;
        font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, PingFang SC, Microsoft YaHei, Source Han Sans SC, Noto Sans CJK SC, WenQuanYi Micro Hei, sans-serif;
    }

    table.execlMainTable .specialField span {
        color: #f00 !important;
    }

    table.excelMainTable tr[style*="height: 30px;"] > td.etype_29 {
        display: none;
    }

    table.excelMainTable tr td:nth-child(10) {
        display: none;
    }

    /*表单空白列 隐藏 字段名3,6 字段值4,7*/
    table.excelMainTable > colgroup > col:nth-child(2),
    table.excelMainTable > colgroup > col:nth-child(5),
    table.excelMainTable > colgroup > col:nth-child(8),
    table.excelMainTable > colgroup > col:nth-child(10) {
        width: 0 !important;
        background-color: transparent;
    }

    /* 字段名 */
    table.excelMainTable > tbody td.etype_2 {
        background-color: #f4f5f7 !important;
        border-right: 1px solid #e1e5e9;
        border-bottom: 1px solid #e1e5e9;
        padding: 6px 6px 6px 12px;
    }

    table.excelMainTable > tbody:after {
        content: "";
        height: 12px;
        display: block;
    }


    /*表单空白行 隐藏*/
    /*table.excelMainTable>tbody>tr[style*="height: 11px;"],
    table.excelMainTable>tbody>tr[style*="height: 12px;"],
    table.excelMainTable>tbody>tr[style*="height: 13px;"],
    table.excelMainTable>tbody>tr[style*="height: 14px;"],
    table.excelMainTable>tbody>tr[style*="height: 15px;"],
    table.excelMainTable>tbody>tr[style*="height: 16px;"],
    table.excelMainTable>tbody>tr[style*="height: 18px;"],
    table.excelMainTable>tbody>tr[style*="height: 30px;"] {
        display: none !important;
    }*/
    table.excelMainTable > tbody > tr[style*="height: 11px;"],
    table.excelMainTable > tbody > tr[style*="height: 12px;"],
    table.excelMainTable > tbody > tr[style*="height: 13px;"],
    table.excelMainTable > tbody > tr[style*="height: 14px;"],
    table.excelMainTable > tbody > tr[style*="height: 15px;"],
    table.excelMainTable > tbody > tr[style*="height: 16px;"],
    table.excelMainTable > tbody > tr[style*="height: 18px;"],
    table.excelMainTable > tbody > tr[style*="height: 30px;"] {
        height: 0 !important;
        overflow: hidden !important;
        display: block;
    }

    table.excelMainTable > tbody > tr[style*="height: 24px;"]:has(td:empty) {
        display: none;
    }

    /*条形码位置调整*/
    table.excelMainTable > tbody img.barcodeimg {
        position: absolute;
        width: 305px;
        height: 80px;
        right: 11%;
        margin-top: 10px;
        padding-top: 18px;
    }

    /*表单头部 空白行处理*/
    table.excelMainTable > tbody > tr[style*="height: 80px;"] {
        height: 10px !important;
    }

    table.excelMainTable > tbody > tr:nth-child(1) > td {
        height: 28px !important;
    }

    table.excelMainTable > tbody td[colspan="9"] span {
        display: inline-block;
        font-size: 16pt !important;
        padding-bottom: 6px !important;
    }

    table.excelMainTable > tbody > tr:nth-child(2) td span {
        padding-bottom: 16px;
    }

    table.excelMainTable > tbody > tr:nth-child(1) td,
    table.excelMainTable > tbody > tr:nth-child(2) td,
    table.excelMainTable > tbody > tr:nth-child(3) td,
    table.excelMainTable > tbody > tr:nth-child(4) td,
    table.excelMainTable > tbody > tr:nth-child(5) td {
        border: none;
    }


    /*表单标题*/
    table.excelMainTable > tbody > tr[style*="height: 80px;"] > td:nth-child(2),
    table.excelMainTable > tbody > tr[style*="height: 80px;"] > td:nth-child(5) {
        background-color: transparent;
        border: none;
    }

    /*分组标题*/
    table.excelMainTable > tbody > tr[style*="height: 42px;"] > td {
        border: none;
        line-height: 26px !important;
        background-color: #edf0f5;
    }

    table.excelMainTable > tbody > tr[style*="height: 42px;"] td:nth-child(2) .etype_1_swapDiv {
        padding: 8px;
        border: 1px solid #e1e5e9;
    }

    table.excelMainTable > tbody > tr[style*="height: 42px;"] .etype_1_swapDiv > span {
        padding-left: 10px;
        font-size: 18px;
        color: #333;
    }

    table.excelMainTable > tbody > tr[style*="height: 42px;"] td:nth-child(2) > .etype_1_swapDiv > span:before {
        content: "";
        display: inline-block;
        height: 14px;
        width: 3px;
        background: #D81C25;
        margin-right: 5px;
        position: relative;
        left: 0;
        top: 2px;
    }

    table.excelMainTable > tbody > tr[style*="height: 42px;"] > td:nth-child(2) {
        padding-left: 0;
        background-color: #edf0f5 !important;
    }

    /* 分组间增加空白行 */
    table.excelMainTable > tbody > tr[style*="height: 42px;"] > td:nth-child(2):before {
        content: " ";
        height: 26px;
        display: block;
        background-color: #fff;
    }


    /*表单字段 名称*/
    table.excelMainTable tr[style*="height: 34px;"] > td:nth-child(3),
    table.excelMainTable tr[style*="height: 36px;"] > td:nth-child(3),
    table.excelMainTable tr[style*="height: 37px;"] > td:nth-child(3),
    table.excelMainTable tr[style*="height: 38px;"] > td:nth-child(3),
    table.excelMainTable tr[style*="height: 39px;"] > td:nth-child(3),
    table.excelMainTable tr[style*="height: 47px;"] > td:nth-child(3) {
        background-color: #f4f5f7 !important;
        border-right: 1px solid #e1e5e9;
        border-bottom: 1px solid #e1e5e9;
        border-left: 1px solid #e1e5e9;
        border-top: 1px solid #e1e5e9;

    }

    table.excelMainTable tr[style*="height: 39px;"] > td:nth-child(3) span,
    table.excelMainTable tr[style*="height: 39px;"] > td:nth-child(6) span {
        color: #333;
    }

    /*表单字段 控件*/
    table.excelMainTable > tbody > tr > td:nth-child(4),
    table.excelMainTable > tbody > tr > td:nth-child(7) {
        padding: 6px 8px;
    }

    table.excelMainTable > tbody > tr[style*="height: 39px;"] > td:nth-child(4),
    table.excelMainTable > tbody > tr[style*="height: 39px;"] > td:nth-child(7) {
        border-right: 1px solid #e1e5e9;
        border-bottom: 1px solid #e1e5e9;
        background-color: transparent;
        border-top: 1px solid #e1e5e9;
    }

    table.excelMainTable tr[style*="height: 39px;"] > td:nth-child(9) {
        border: none;
        background-color: transparent;
    }


    /*表单字段 单元格控制*/
    /* 明细表 */
    .excelDetailContent {
        padding: 6px;
    }

    /* 明细行 */
    table.excelDetailTable .detail_data_row td {
        background-color: transparent !important;
    }


    table.excelDetailTable tr:nth-child(1) {
        /*display: none;*/
    }

    table.excelDetailTable tr:nth-child(2) > td {
        border: none !important;
    }

    table.excelDetailTable td {
        border: 1px solid #e1e5e9;
    }

    /*明细行表头*/
    table.excelDetailTable > tbody > tr:nth-child(3) td {
        background-color: #f4f5f7 !important;
    }

    /*签字意见*/
    .wf-req-sign-list .wf-req-sign-list-content .content-left .left-department-span {
        display: none !important;
    }

    .wf-req-loglist .wea-avatar {
        display: none !important;
    }

    .wf-req-loglist {
        padding: 6px;
        margin: auto;
    }

    .wf-req-loglist .content-right {
        padding: 0;
    }

    /*2024-5-14 调整审批意见位置，p标签去掉inline-block; 保留换行效果*/
    .wf-req-loglist .content-right div,
    p {
        /*display: inline-block;*/
        margin-right: 16px;
    }


    .wf-req-loglist .logitem-Recipient {
        float: right;
        margin-top: 0;
        min-width: 80px;
        /*display: none;*/
    }

    /*2024-5-14 调整审批意见位置*/
    .wf-req-loglist .content-right {
        display: flex;
    }

    .wf-req-loglist .content-right:before {
        content: '审批意见：';
        color: #666;
        margin-right: 2px;
    }

    /*.wf-req-loglist .content-right-remark-html:before {*/
    /*    content: '审批意见：';*/
    /*    color: #666;*/
    /*}*/
    .wf-req-loglist .content-right-remark-html {
        width: 40%;
        margin-top: 0;
    }

    .wf-req-loglist .loglist-item-operatedate {

        margin-top: 0 !important;
    }

    .wf-req-loglist .loglist-item-operatedate > span {
        display: inline-block
    }

    /*审批节点*/
    .wf-req-loglist .loglist-item-operatedate > span:nth-child(2) {
        float: left;
        width: 150px;
        margin-right: 4px;
        color: #333;
    }

    /*审批时间*/
    .wf-req-loglist .loglist-item-operatedate > span:nth-child(1) {
        width: 120px;
        margin-right: 4px;
    }


    .wf-req-loglist .loglist-item-operatedate .reqlogbtn {

        float: right !important;
        min-width: 150px;
        margin-right: 4px;
    }


    .wf-req-sign-list-content {
        padding: 4px !important;
    }


    /*打印*/
    .wf-req-print table.excelMainTable {
        table-layout: fixed;
    }


    .wf-req-print table.excelMainTable > colgroup > col:nth-child(1),
    .wf-req-print table.excelMainTable > colgroup > col:nth-child(9) {
        width: 0 !important;
    }


    .wf-req-print table.excelMainTable img.barcodeimg {
        position: absolute;
        width: 350px;
        height: 80px;
        right: 1%;
    }

    .sign-label {
        color: #4696D1 !important;
        font-size: 16px;
        font-weight: bold;
    }

    .icon-coms-Need-feedback {
        color: #4696D1 !important;
    }
}

@media print {
    body {
        -webkit-filter: grayscale(100%);
        -moz-filter: grayscale(100%);
        -ms-filter: grayscale(100%);
        -o-filter: grayscale(100%);

        filter: grayscale(100%);
    }

    .wf-req-loglist .content-right-remark-html {
        width: 200px;
        margin-top: 0;
    }


    .wf-req-print table.excelMainTable img.barcodeimg {
        position: absolute;
        width: 250px;
        height: 55px;
        right: 0;
    }

}

