<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.engine.contract.mapper.DepoSitMapper">

    <select id="getNewContractAmount" resultMap="ContractNew">
        SELECT * FROM ContractNew
    </select>
    <resultMap id="ContractNew" type="com.engine.contract.common.ContracDepositBean">
        <id column="id" property="id" />
        <result column="htlx" property="htlx"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="moveDate" property="moveDate"/>
        <result column="deposit" property="deposit"/>
        <result column="lylc" property="lylc"/>
        <result column="yhtlc" property="yhtlc"/>
        <collection property="details" ofType="com.engine.contract.common.ContactDepositDetail" column="id" javaType="java.util.ArrayList" select="selectdetailByIdNew">
            <id column="id" property="id" />
            <result column="mainid" property="mainid" />
            <result column="startDate" property="startDate"/>
            <result column="endDate" property="endDate"/>
            <result column="deposit" property="deposit"/>
        </collection>
    </resultMap>
    <select id="selectdetailByIdNew" resultType="com.engine.contract.common.ContactDepositDetail">
        select id,mainid,ksrq as startdate,jsrq as endDate,dzbzj as deposit from formtable_main_43_dt1 where mainid = #{id}
    </select>






    <select id="getRenewContractAmount" resultMap="ContractReNew">
        SELECT * FROM ContractReNew
    </select>
    <resultMap id="ContractReNew" type="com.engine.contract.common.ContracDepositBean">
        <id column="id" property="id" />
        <result column="htlx" property="htlx"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="moveDate" property="moveDate"/>
        <result column="deposit" property="deposit"/>
        <result column="lylc" property="lylc"/>
        <result column="yhtlc" property="yhtlc"/>
        <collection property="details" ofType="com.engine.contract.common.ContactDepositDetail" column="id" javaType="java.util.ArrayList" select="selectdetailByIdReNew">
            <id column="id" property="id" />
            <result column="mainid" property="mainid" />
            <result column="startDate" property="startDate"/>
            <result column="endDate" property="endDate"/>
            <result column="deposit" property="deposit"/>
        </collection>
    </resultMap>
    <select id="selectdetailByIdReNew" resultType="com.engine.contract.common.ContactDepositDetail">
        select id,mainid,ksrq as startdate,jsrq as endDate,dzbzj as deposit from formtable_main_78_dt1 where mainid = #{id}
    </select>





    <select id="getalterContractAmount" resultMap="ContractAlter">
         SELECT * FROM ContractAlter
    </select>
    <resultMap id="ContractAlter" type="com.engine.contract.common.ContracDepositBean">
        <id column="id" property="id" />
        <result column="htlx" property="htlx"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="moveDate" property="moveDate"/>
        <result column="deposit" property="deposit"/>
        <result column="lylc" property="lylc"/>
        <result column="yhtlc" property="yhtlc"/>
        <collection property="details" ofType="com.engine.contract.common.ContactDepositDetail" column="id" javaType="java.util.ArrayList" select="selectdetailByIdAlter">
            <id column="id" property="id" />
            <result column="mainid" property="mainid" />
            <result column="startDate" property="startDate"/>
            <result column="endDate" property="endDate"/>
            <result column="deposit" property="deposit"/>
        </collection>
    </resultMap>
    <select id="selectdetailByIdAlter" resultType="com.engine.contract.common.ContactDepositDetail">
        select id,mainid,ksrq as startdate,jsrq as endDate,dzbzj as deposit from formtable_main_100_dt1 where mainid = #{id}
    </select>





    <select id="getEndContractAmount" resultMap="ContractEnd">
        SELECT * FROM ContractEnd
    </select>
    <resultMap id="ContractEnd" type="com.engine.contract.common.ContracDepositBean">
        <id column="id" property="id" />
        <result column="htlx" property="htlx"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="moveDate" property="moveDate"/>
        <result column="deposit" property="deposit"/>
        <result column="lylc" property="lylc"/>
        <result column="yhtlc" property="yhtlc"/>
    </resultMap>
</mapper>