<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.engine.contract.mapper.ContractMapper">
    <select id="selectAllContract" resultMap="Contract">
        SELECT *
        FROM data4reportview
    </select>
    <resultMap id="Contract" type="com.engine.contract.common.ContractBean">
        <id column="id" property="id"/>
        <result column="htbh" property="htbh"/>
        <result column="htlx" property="htlx"/>
        <result column="startDate" property="startDate"/>
        <result column="endDate" property="endDate"/>
        <result column="qzrq" property="qzrq"/>
        <result column="jsrq" property="jsrq"/>
        <result column="moveDate" property="moveDate"/>
        <result column="rent" property="rent"/>
        <result column="area" property="area"/>
        <result column="lylc" property="lylc"/>
        <result column="yhtlc" property="yhtlc"/>
        <collection property="details" ofType="com.engine.contract.common.ContractDetail" column="id"
                    javaType="java.util.ArrayList" select="selectdetailById">
            <id column="id" property="id"/>
            <result column="mainid" property="mainid"/>
            <result column="endDate" property="endDate"/>
            <result column="startDate" property="startDate"/>
            <result column="rent" property="rent"/>
            <result column="area" property="area"/>
        </collection>
    </resultMap>
    <select id="selectdetailById" resultType="com.engine.contract.common.ContractDetail">
        select id, mainid, ksrq as startdate, jsrq as endDate, je as rent, zlmj as area
        from uf_cwzl_jcht_dt1
        where mainid = #{id}
    </select>


    <resultMap id="Receive" type="com.engine.contract.common.AmountContractBean">
        <id column="hth" property="htid"/>
        <result column="nf" property="nf"/>
        <result column="col1" property="seone"/>
        <result column="col2" property="setwo"/>
        <result column="col3" property="sethree"/>
        <result column="col4" property="sefour"/>
    </resultMap>
    <select id="selectAmountReceived" resultMap="Receive">
        select *
        from ssjereport
    </select>

    <!--补缴-->
    <resultMap id="Supplementary" type="com.engine.contract.common.AmountContractBean">
        <id column="hth" property="htid"/>
        <result column="nf" property="nf"/>
        <result column="bj1" property="seone"/>
        <result column="bj2" property="setwo"/>
        <result column="bj3" property="sethree"/>
        <result column="bj4" property="sefour"/>
    </resultMap>
    <select id="selectsupplementary" resultMap="Supplementary">
        SELECT (CAST(hth as varchar) + '_' + CAST(nf as varchar)) hth,
               SUM(yjdbj)                                         bj1,
               SUM(ejdbj)                                         bj2,
               SUM(sjdbj)                                         bj3,
               SUM(sijdbj)                                        bj4,
               nf
        FROM uf_cwzl_jdbj
        GROUP BY hth,
                 nf
    </select>

</mapper>