package com.engine.housing.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import org.exolab.castor.types.Date;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

/**
 * 计算每日区域变化信息job
 * 每日凌晨1点计算
 */
public class CaculateAreaChangeJob extends BaseCronJob {
    //参数指定日期 yyyy-MM-dd
    private String specificDay;

    public String getSpecificDay() {
        return specificDay;
    }

    public void setSpecificDay(String specificDay) {
        this.specificDay = specificDay;
    }

    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        try {
            bb.writeLog("CaculateAreaChangeJob --- START");
            String qybhkzTable = Util.null2String(bb.getPropValue("863_mode", "qybhkz_table"));
            String qybhTable = Util.null2String(bb.getPropValue("863_mode", "qybh_table"));
            String yesterday;
            //默认取昨天的日期进行计算
            String today = TimeUtil.getToday();
            if (specificDay != null && !specificDay.isEmpty()) {
                try {
                    //测试转换日期，如果出错代表参数填写错误
                    Date.parseDate(specificDay);
                    today = specificDay;
                } catch (Exception de) {
                    de.printStackTrace();
                    bb.writeLog("参数日期格式错误：" + de.getMessage());
                }
            }
            yesterday = TimeUtil.dateAdd(today, -1);
            //查找空置区域变化表数据
            insertData(yesterday, qybhkzTable, qybhTable);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("Exception异常 ：" + e.getMessage());
        }
        bb.writeLog("CaculateAreaChangeJob --- END");
    }

    private void insertData(String date, String qybhkzTable, String qybhTable) throws Exception {
        RecordSet rs = new RecordSet();
        JSONObject jo;
        List<Object> listValue;
        String sb = "SELECT " +
                " '" + date + "' AS mydate, " +
                " d.lb, " +
                " d.id, " +
                " d.ldbh, " +
                " d.lzgs, " +
                " ISNULL( TBF.tuizu_mj, 0 ) AS tuizu_mj, " +
                " ISNULL( TBF.zhongzhi_mj, 0 ) AS zhongzhi_mj, " +
                " ISNULL( TBF.kongzhi_mj, 0 ) AS kongzhi_mj, " +
                " ISNULL( TBF.xinqian_mj, 0 ) AS xinqian_mj, " +
                " ISNULL( TBF.xuqian_mj, 0 ) AS xuqian_mj, " +
                " ISNULL( TBF.jing_mj, 0 ) AS jing_mj  " +
                "FROM " +
                " uf_zc_ldxx d " +

                " LEFT JOIN ( " +
                " SELECT DISTINCT " +
                "  TBE.*, " +
                "  ISNULL( TBE.tuizu_mj, 0 ) + ISNULL( TBE.zhongzhi_mj, 0 ) AS kongzhi_mj, " +
                "  ISNULL( TBE.xinqian_mj, 0 ) - ISNULL( TBE.tuizu_mj, 0 ) - ISNULL( TBE.zhongzhi_mj, 0 ) AS jing_mj  " +
                " FROM " +
                "  ( " +
                "  SELECT " +
                "   TBA.jsrq AS mydate, " +
                "   TBA.ldxx, " +
                "   TBA.tuizu_mj, " +
                "   TBA.zhongzhi_mj, " +
                "   TBB.xinqian_mj, " +
                "   TBB.xuqian_mj  " +
                "  FROM " +
                "   ( " +
                "   SELECT " +
                "    t.jsrq, " +
                "    t.ldxx, " +
                "    SUM ( CASE WHEN t.htlx = 3 THEN t.jzmj ELSE 0 END ) AS tuizu_mj, " +
                "    SUM ( CASE WHEN t.htlx = 4 THEN t.jzmj ELSE 0 END ) AS zhongzhi_mj  " +
                "   FROM " + qybhkzTable +
                "     t  " +
                "   GROUP BY " +
                "    t.jsrq, " +
                "    t.ldxx  " +
                "   ) TBA " +

                "   LEFT JOIN ( " +
                "   SELECT " +
                "    t.ksrq, " +
                "    t.ldxx, " +
                "    SUM ( CASE WHEN t.htlx = 0 THEN t.jzmj ELSE 0 END ) AS xinqian_mj, " +
                "    SUM ( CASE WHEN t.htlx = 1 THEN t.jzmj ELSE 0 END ) AS xuqian_mj  " +
                "   FROM " + qybhTable +
                "     t  " +
                "   GROUP BY " +
                "    t.ksrq, " +
                "    t.ldxx  " +
                "   ) TBB ON ( TBA.jsrq = TBB.ksrq AND TBA.ldxx = TBB.ldxx )" +

                " UNION ALL " +
                "  SELECT " +
                "   TBC.ksrq AS mydate, " +
                "   TBC.ldxx, " +
                "   TBD.tuizu_mj, " +
                "   TBD.zhongzhi_mj, " +
                "   TBC.xinqian_mj, " +
                "   TBC.xuqian_mj  " +
                "  FROM " +
                "   ( " +
                "   SELECT " +
                "    t.ksrq, " +
                "    t.ldxx, " +
                "    SUM ( CASE WHEN t.htlx = 0 THEN t.jzmj ELSE 0 END ) AS xinqian_mj, " +
                "    SUM ( CASE WHEN t.htlx = 1 THEN t.jzmj ELSE 0 END ) AS xuqian_mj  " +
                "   FROM " + qybhTable +
                "     t  " +
                "   GROUP BY " +
                "    t.ksrq, " +
                "    t.ldxx  " +
                "   ) TBC " +

                "   LEFT JOIN ( " +
                "   SELECT " +
                "    t.jsrq, " +
                "    t.ldxx, " +
                "    SUM ( CASE WHEN t.htlx = 3 THEN t.jzmj ELSE 0 END ) AS tuizu_mj, " +
                "    SUM ( CASE WHEN t.htlx = 4 THEN t.jzmj ELSE 0 END ) AS zhongzhi_mj  " +
                "   FROM " + qybhkzTable +
                "     t  " +
                "   GROUP BY " +
                "    t.jsrq, " +
                "    t.ldxx  " +
                "   ) TBD ON ( TBC.ksrq = TBD.jsrq AND TBC.ldxx = TBD.ldxx )  " +

                "  ) TBE  " +
                " ) TBF ON ( d.id = TBF.ldxx AND TBF.mydate = '" + date + "' )";
        bb.writeLog("查询昨日数据的sql：" + sb);
        rs.executeQuery(sb);
        JSONArray jaData = QueryResultUtil.getJSONArrayList(rs);
        if (jaData.size() > 0) {
            String insertFields = "rq,lb,ld,ldbh,lzgs,brtzmj,brzzmj,brxzkzmj,brxqmj,rxqmj,jmj";
            //默认系统管理员为创建人
            int Create = SDUtil.getSystemMangerByLoginId();
            //模块id
            int module = Util.getIntValue(bb.getPropValue("863_mode", "mjbh_module"));
            String insertTable = Util.null2String(bb.getPropValue("863_mode", "mjbh_table"));
            //先删除昨日日期的数据
            String deleteSql = "delete from " + insertTable + " where rq = ? ";
            rs.executeUpdate(deleteSql, date);
            // 遍历jsons数组对象，
            for (int i = 0; i < jaData.size(); i++) {
                listValue = new ArrayList<>();
                jo = jaData.getJSONObject(i);
                listValue.add(jo.getString("mydate"));
                listValue.add(jo.getInteger("lb"));
                listValue.add(jo.getString("id"));
                listValue.add(jo.getString("ldbh"));
                listValue.add(jo.getInteger("lzgs"));
                listValue.add(jo.getBigDecimal("tuizu_mj"));
                listValue.add(jo.getBigDecimal("zhongzhi_mj"));
                listValue.add(jo.getBigDecimal("kongzhi_mj"));
                listValue.add(jo.getBigDecimal("xinqian_mj"));
                listValue.add(jo.getBigDecimal("xuqian_mj"));
                listValue.add(jo.getBigDecimal("jing_mj"));
                bb.writeLog("插入的数据：" + listValue);
                InsertModuleUtil.ModuleInsert(insertTable, insertFields.split(","), listValue, Create, module, null);
            }
        }
    }
}
