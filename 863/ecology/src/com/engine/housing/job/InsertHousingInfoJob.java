package com.engine.housing.job;

import com.engine.contract.cornjob.InsertAreaNum;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.interfaces.schedule.BaseCronJob;

/**
 * 每日插入一条当天最新得区域信息job
 * 每日凌晨1点10分
 */
public class InsertHousingInfoJob extends BaseCronJob {

    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        try {
            //每日插入一条当天最新得区域信息
            InsertAreaNum insertAreaNum = new InsertAreaNum();
            insertAreaNum.doexecute(TimeUtil.getCurrentDateString());
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("catch expt:" + e.getMessage());
        }
    }

}
