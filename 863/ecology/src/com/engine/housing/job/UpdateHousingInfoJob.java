package com.engine.housing.job;

import cn.hutool.core.date.DateUtil;
import com.engine.contract.cornjob.InsertAreaNum;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.module.util.SDModuleRightThread;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.Arrays;
import java.util.Date;

/**
 * 更新房源信息job
 * 每日凌晨1点10分
 * 从specificDay执行到昨天
 */
@Getter
@Setter
public class UpdateHousingInfoJob extends BaseCronJob {
    //---Job 参数---
    /**
     * 参数指定日期 yyyy-MM-dd
     */
    private String specificDay;
    /**
     * 出租率回溯表
     */
    private String uf_mjhz_back;
    private String uf_mjfz_863_back;
    private String uf_mjhz_mp_back;
    //---Job 参数---
    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        try {
            bb.writeLog(this.getClass().getName() + "---START");
            String yesterday;
            //默认取昨天的日期进行计算
            String today = TimeUtil.getToday();
            //实际的今天
            Date actualToday = new Date();
            //如果指定了参数某天，则今天为参数日期
            if (specificDay != null && !specificDay.isEmpty()) {
                //测试转换日期，如果出错代表参数填写错误
                if (TimeUtil.getString2Date(specificDay, "yyyy-MM-dd") != null) {
                    today = specificDay;
                } else {
                    bb.writeLog("日期参数格式错误");
                    return;
                }
            }
            //从today日期到实际当前日期，所有的数据都要算一遍
            //判断日期是否大于等于今天
            Date firstDate = TimeUtil.getString2Date(today, "yyyy-MM-dd");
            //如果起始日期比实际当前日期还大，那么只跑当天日期的数据
            if (DateUtil.compare(firstDate, actualToday) > 0) {
                firstDate = actualToday;
            }
            //从起始日期开始，跑所有往后至今天的日期的数据
            long differDays = DateUtil.betweenDay(firstDate, actualToday, false);
            for (int i = 0; i <= differDays; i++) {
                //获取昨日的日期
                yesterday = TimeUtil.dateAdd(today, -1);
                bb.writeLog("yesterday:" + yesterday);
                //更新房源信息,先执行空置的区域变化，再执行租赁的区域变化
                //先更新为空置
                updateQybhkzData(yesterday);
                //再更新为租赁
                updateQybhData(yesterday);
                //根据更新的房源信息更新面积信息
                InsertAreaNum insertAreaNum = new InsertAreaNum();
                insertAreaNum.doexecute(today);
                today = TimeUtil.dateAdd(today, +1);
            }
            //执行insertAreaNum之后，需要将当天最新的出租率信息，插入到新增的表中，这样每天的出租率得以保存
            insertCurrentCzl();
        } catch (Exception e) {
            bb.writeLog("Exception:" + Arrays.toString(e.getStackTrace()));
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    /**
     * 插入当天出租率到三张回溯表
     */
    private void insertCurrentCzl() {
        //先删除最新日期的昨天的数据
        String today = TimeUtil.getToday();
        String yesterday = TimeUtil.dateAdd(today, -1);
        doInsertCurrentCzl(yesterday, "uf_mjhz", uf_mjhz_back);
        doInsertCurrentCzl(yesterday, "uf_mjfz_863", uf_mjfz_863_back);
        doInsertCurrentCzl(yesterday, "uf_mjhz_mp", uf_mjhz_mp_back);
    }

    private void doInsertCurrentCzl(String yesterday, String originTable, String backTable) {
        int moduleId;
        bb.writeLog("doInsertCurrentCzl");
        bb.writeLog("originTable:" + originTable);
        bb.writeLog("backTable:" + backTable);
        String backMainId, originMainId;
        RecordSet rs = new RecordSet();
        String sql;
        try {
            if (StringUtils.isNotBlank(originTable)) {
                moduleId = ModuleDataUtil.getModuleIdByName(backTable);
                if (moduleId == -1) {
                    bb.writeLog("未匹配到建模id");
                } else {
                    backMainId = getMainId(backTable, yesterday);
                    originMainId = getMainId(originTable, yesterday);
                    String backFields = getAllFields(backTable);
                    bb.writeLog("backMainId:" + backMainId);
                    bb.writeLog("originMainId:" + originMainId);
                    bb.writeLog("backFields:" + backFields);
                    if (!backMainId.isEmpty()) {
                        //删除昨日的数据
                        rs.executeUpdate("delete from " + backTable + " where id = ?", backMainId);
                        rs.executeUpdate("delete from " + backTable + "_dt1 where mainid = ?", backMainId);
                    }
                    //插入主表
                    sql = " SET IDENTITY_INSERT " + backTable + " ON; ";
                    sql += " INSERT INTO " + backTable + " (" + backFields + ") " +
                            " SELECT " + backFields + " FROM  " + originTable +
                            " WHERE id = " + originMainId + "; ";
                    sql += " SET IDENTITY_INSERT " + backTable + " OFF;";
                    bb.writeLog("insert main sql:" + sql);
                    if (rs.executeUpdate(sql)) {
                        //插入主表后，更新formmodeid为新的moduleId
                        rs.executeUpdate("update " + backTable + " set formmodeid = ?", moduleId);
                        //重构权限
                        SDModuleRightThread rightThread = new SDModuleRightThread();
                        rightThread.setModeId(moduleId);
                        rightThread.resetModeRight();
                        //插入明细表
                        String backTableDetail = backTable + "_dt1";
                        String originTableDetail = originTable + "_dt1";
                        String backFieldsDetail = getAllFields(backTable + "_dt1");

                        sql = " SET IDENTITY_INSERT " + backTableDetail + " ON; ";
                        sql += " INSERT INTO " + backTableDetail + " (" + backFieldsDetail + ") " +
                                " SELECT " + backFieldsDetail + " FROM  " + originTableDetail +
                                " WHERE  mainid = " + originMainId + "; ";
                        sql += " SET IDENTITY_INSERT " + backTableDetail + " OFF;";
                        bb.writeLog("insert detail sql:" + sql);
                        if (!rs.executeUpdate(sql)) {
                            bb.writeLog("插入昨日明细数据出错：" + rs.getExceptionMsg());
                        }

                    } else {
                        bb.writeLog("插入昨日主表数据出错：" + rs.getExceptionMsg());
                    }

                }
            } else {
                bb.writeLog("originTable为空");
            }
        } catch (Exception e) {
            bb.writeLog("doInsertCurrentCzl 异常：" + SDUtil.getExceptionDetail(e));
        }


    }

    private String getMainId(String tableName, String yesterday) {
        RecordSet rs = new RecordSet();
        String id = "";
        if (rs.executeQuery("select id from " + tableName + " where rq = ?", yesterday)) {
            if (rs.next()) {
                id = Util.null2String(rs.getString("id"));
            }
        }
        return id;
    }

    /**
     * 更新uf_zs_qyxx  建模-招商-区域信息
     * 更新为2空置，并更新面积等信息
     *
     * @param date
     */
    private void updateQybhkzData(String date) {
        RecordSet rs = new RecordSet();
        String nowStr = TimeUtil.getCurrentTimeString();
        //空置只考虑'退组'的合同
//        String sql = "UPDATE uf_zs_qyxx  " +
//                " SET qy = t1.qy, " +
//                " sfpt = t1.sfpt, " +
//                " xz = '1', " +
//                " jzmj = t1.jzmj, " +
//                " kczmj = t1.kczmj, " +
//                " yczmj = t1.yczmj, " +
//                " yzfkczmj = t1.yzfkczmj, " +
//                " bzsm = t1.bzsm, " +
//                " htlc = t1.htlc,  " +
//                " modedatamodifydatetime =  '" + nowStr + "' " +
//                " FROM " +
//                " uf_qybhkz t1 " +
//                " LEFT JOIN uf_zs_qyxx t2 ON ( t1.qymc = t2.id )  " +
//                " WHERE " +
//                " t1.jsrq = ?  and t1.htlx = 1 ";
        //2022-11-24 修改，状态改为 2 空置，合同类型
//        String sql = "UPDATE uf_zs_qyxx  " +
//                " SET qy = t1.qy, " +
//                " sfpt = t1.sfpt, " +
//                " xz = '2', " +
//                " jzmj = t1.jzmj, " +
//                " kczmj = t1.kczmj, " +
//                " yczmj = t1.yczmj, " +
//                " yzfkczmj = t1.yzfkczmj, " +
//                " bzsm = t1.bzsm, " +
//                " htlc = t1.htlc,  " +
//                " modedatamodifydatetime =  '" + nowStr + "' " +
//                " FROM " +
//                " uf_qybhkz t1 " +
//                " LEFT JOIN uf_zs_qyxx t2 ON ( t1.qymc = t2.id )  " +
//                " WHERE " +
//                " t1.jsrq = ? ";
        //2023-08-22 修改，只将退租的合同，刷成空置状态，日期取结算日期jsrq1，之前的结束日期jsrq不对（会导致续租后又刷成空置)
        String sql = "UPDATE uf_zs_qyxx  " +
                " SET qy = t1.qy, " +
                " sfpt = t1.sfpt, " +
                " xz = '2', " +
                " jzmj = t1.jzmj, " +
                " kczmj = t1.kczmj, " +
                " yczmj = t1.yczmj, " +
                " yzfkczmj = t1.yzfkczmj, " +
                " bzsm = t1.bzsm, " +
                " htlc = t1.htlc,  " +
                " modedatamodifydatetime =  '" + nowStr + "' " +
                " FROM " +
                " uf_qybhkz t1 " +
                " LEFT JOIN uf_zs_qyxx t2 ON ( t1.qymc = t2.id )  " +
                " WHERE " +
                " t1.jsrq1 = ? and t1.htlx = 3 ";
        rs.executeUpdate(sql, date);
    }

    /**
     * 更新uf_zs_qyxx 建模-招商-区域信息
     * 更新为租赁，并更新面积等信息
     *
     * @param date
     */
    private void updateQybhData(String date) {
        RecordSet rs = new RecordSet();
        String nowStr = TimeUtil.getCurrentTimeString();
        String sql = "UPDATE uf_zs_qyxx  " +
                " SET qy = t1.qy, " +
                " sfpt = t1.sfpt, " +
                // xz租赁状态 0为租赁
                " xz = '0', " +
                " jzmj = t1.jzmj, " +
                " kczmj = t1.kczmj, " +
                " yczmj = t1.yczmj, " +
                " yzfkczmj = t1.yzfkczmj, " +
                " bzsm = t1.bzsm, " +
                " htlc = t1.htlc,  " +
                " modedatamodifydatetime =  '" + nowStr + "' " +
                " FROM " +
                // 区域面积变化（新增租赁）
                " uf_qybh t1 " +
                " LEFT JOIN uf_zs_qyxx t2 ON ( t1.qymc = t2.id )  " +
                " WHERE " +
                " t1.ksrq = ? ";
        rs.executeUpdate(sql, date);
    }

    private String getAllFields(String tableName) {
        String result = "";
        StringBuilder sb = new StringBuilder();
        RecordSet rs = new RecordSet();
        String columnName;
        if (rs.executeQuery(" SELECT column_name " +
                "FROM INFORMATION_SCHEMA.COLUMNS " +
                "WHERE TABLE_NAME = '" + tableName + "'")) {
            while (rs.next()) {
                columnName = Util.null2String(rs.getString("column_name"));
                sb.append(columnName).append(CommonCst.COMMA_EN);
            }
            if (!sb.toString().isEmpty()) {
                result = sb.substring(0, sb.length() - 1);
            }
        }
        return result;
    }
}
