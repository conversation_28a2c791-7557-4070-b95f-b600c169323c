package com.engine.finance.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.util.InsertModuleUtil;
import com.engine.parent.query.util.QueryResultUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 更新保证金信息job
 * 每日凌晨1点10分
 *
 * <AUTHOR>
 */
public class UpdateBzjInfoJob extends BaseCronJob {
    /**
     * 保证金汇总模块id
     */
    private String bzjhzModuleId;
    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        try {
            RecordSet rs = new RecordSet();
            //执行更新语句,将已存在的企业数据进行更新
//            String sql = " update uf_bzjhz set xqbzj = b.xianqian_bzj, " +
//                    " xbzj =  b.xuqian_bzj, " +
//                    " bgbzj = b.biangeng_bzj, " +
//                    " tzbzj = b.tuizu_bzj " +
//                    " from uf_bzjhz a,V_BZJ_SUM b " +
//                    " where a.qymc = b.qymc ";
//            rs.executeUpdate(sql);
            //现将保证金汇总的都删除
            String sql = "delete from uf_bzjhz";
            bb.writeLog("update sql " + sql);
            rs.executeUpdate(sql);
            // 获取需要添加的企业数据
            // sql = "select a.* from V_BZJ_SUM a where NOT EXISTS ( select 1 from uf_bzjhz b where b.qymc = a.qymc )";
            sql = "select * from V_BZJ_SUM ";
            bb.writeLog("select sql " + sql);
            rs = new RecordSet();
            rs.executeQuery(sql);
            JSONArray ja = QueryResultUtil.getJSONArrayList(rs);
            bb.writeLog(ja);
            bb.writeLog(ja.size());
            if (ja.size() > 0) {
                bb.writeLog("数据量大于0");
                insertData(ja);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }


    private void insertData(JSONArray ja) {
        JSONObject obj;
        List<String> insertFields = new ArrayList<>();
        insertFields.add("qymc");
        insertFields.add("xqbzj");
        insertFields.add("xbzj");
        insertFields.add("bgbzj");
        insertFields.add("tzbzj");
        insertFields.add("bzjcz");
        List<Object> values;
        int creatId = SDUtil.getSystemMangerByLoginId();
        int moduleId = Util.getIntValue(bzjhzModuleId);
        String tableName = "uf_bzjhz";

        //循环所有文档数据
        for (int i = 0; i < ja.size(); i++) {
            BigDecimal tuizuBzj = new BigDecimal(0);
            obj = ja.getJSONObject(i);
            values = new ArrayList<>();
            values.add(obj.get("qymc"));
            values.add(obj.getBigDecimal("xianqian_bzj"));
            values.add(obj.getBigDecimal("xuqian_bzj"));
            values.add(obj.getBigDecimal("biangeng_bzj"));
            values.add(obj.getBigDecimal("tuizu_bzj"));
            if (obj.getBigDecimal("tuizu_bzj") != null) {
                tuizuBzj = obj.getBigDecimal("tuizu_bzj");
            }
            if (obj.getBigDecimal("xuqian_bzj") != null) {
                values.add(obj.getBigDecimal("xuqian_bzj").subtract(tuizuBzj));
            } else {
                values.add(null);
            }
            bb.writeLog(values);
            try {
                InsertModuleUtil.ModuleInsert(tableName, insertFields, values, creatId, moduleId, null);
            } catch (Exception e) {
                e.printStackTrace();
                bb.writeLog(e.getMessage());
            }
        }
    }

    public String getBzjhzModuleId() {
        return bzjhzModuleId;
    }

    public void setBzjhzModuleId(String bzjhzModuleId) {
        this.bzjhzModuleId = bzjhzModuleId;
    }
}
