package com.engine.finance.job;

import com.engine.contract.cmd.DepositCmd;
import com.weaver.formmodel.util.DateHelper;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName TestJob2.java
 * @Description 保证金日期刷新
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/2/21
 */
public class TestJob2 extends BaseCronJob {


    @Override
    public void execute() {
        User user = new User(1);
        Map<String, Object> params = new HashMap<>();
        DepositCmd updateReportCmd = new DepositCmd(params, user);
        updateReportCmd.doExecute(DateHelper.parseDate(DateHelper.getCurrentDate()),
                "uf_bzjxx",
                "uf_bzjxx_dt1",
                182,
                true);
    }


}
