package com.engine.finance.job;

import com.engine.contract.cmd.DepositCmd;
import com.weaver.formmodel.util.DateHelper;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Deposit<PERSON><PERSON><PERSON><PERSON>ob extends BaseCronJob {

    public String date;

    @Override
    public void execute() {
        User user = new User(1);
        Map<String, Object> params = new HashMap<>();
        DepositCmd updateReportCmd = new DepositCmd(params, user);

        if (date == null) {
            this.setDate(DateHelper.getCurrentDate());
        }
        Date dateobj = DateHelper.parseDate(date);
        if (dateobj == null) {
            updateReportCmd.doExecute(
                    new Date(),
                    "uf_uf_bzjhistory",
                    "uf_uf_bzjhistory_dt1",
                    183,
                    false);
        } else {
            updateReportCmd.doExecute(
                    dateobj,
                    "uf_uf_bzjhistory",
                    "uf_uf_bzjhistory_dt1",
                    183,
                    false);
        }
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
