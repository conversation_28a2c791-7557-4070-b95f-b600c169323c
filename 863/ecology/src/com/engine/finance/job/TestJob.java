package com.engine.finance.job;

import com.engine.contract.cmd.UpdateAllCmd;
import weaver.hrm.User;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.HashMap;
import java.util.Map;

/**
 * 季度报表刷新
 * //更新年度季度月度
 * //按照合同删除部分季度报表，然后生成新季度报表数据
 */
public class TestJob extends BaseCronJob {


    @Override
    public void execute() {
        User user = new User(1);
        Map<String, Object> params = new HashMap<>();
        UpdateAllCmd updateReportCmd = new UpdateAllCmd(params, user);
        updateReportCmd.doExecute();
    }
}
