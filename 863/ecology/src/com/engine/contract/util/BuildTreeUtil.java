package com.engine.contract.util;

import com.engine.contract.common.ContractTree;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 泛型改造要怎么改造呢?
 */
public class BuildTreeUtil {

    /**
     * list 转 tree
     *
     * @param list
     * @return
     */
    public static <T extends ContractTree> List<T> listToTree(List<T> list) {
        //最终树
        List<T> treeList = new ArrayList<>();
        //筛选根节点
        List<T> rootList = list.stream().filter(
                vo ->
                        vo.getYhtlc() == null ||
                                "".equals(vo.getYhtlc()) ||
                                "2".equals(vo.getHtlx())
        ).collect(Collectors.toList());
        //寻找子节点
        rootList.forEach(tree -> treeList.add(findChildren(tree, list)));

        return treeList;
    }

    /**
     * 寻找子节点
     *
     * @param tree
     * @param list
     * @return
     */
    private static <T extends ContractTree> T findChildren(T tree, List<T> list) {
        list.stream().filter(
                node ->
                        tree.getLylc() != null &&
                                node.getYhtlc() != null &&
                                !"2".equals(node.getHtlx()) &&
                                tree.getLylc().equals(node.getYhtlc())
        ).collect(Collectors.toList()).forEach(node -> {
            if (tree.getChildren() == null) {
                tree.setChildren(new ArrayList<>());
            }
            tree.getChildren().add(findChildren(node, list));
        });

        return tree;
    }

}
