package com.engine.contract.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.constant.CommonCst;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.util.ModuleDataUtil;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * FileName: ProcDataUtil.java
 * 执行存储过程工具类
 *
 * <AUTHOR>
 * @version v1.00
 * @Date 2022/4/21
 */
public class ReportDataUtil {

    /**
     * 将JSONArray 结果数据插入建模表中
     * 注意：存储过程结果的字段名，必须和【insertFields】参数名一致
     *
     * @param ja              数据集合（字段都是小写的）
     * @param insertFields    建模插入的字段集合
     * @param moduleTableName 建模表名
     * @param creatId         创建人id
     * @return
     */
    public static String insertData(JSONArray ja,
                                    String insertFields,
                                    String moduleTableName,
                                    Integer moduleid,
                                    int creatId) {
        String erroMsg = "";
        BaseBean bb = new BaseBean();
        bb.writeLog("ProcDataUtil -- insertData ---START");
        JSONObject jo;
        String[] fieldArray = insertFields.split(CommonCst.COMMA_EN);
        //先删除数据
        clearModuleData(moduleTableName);
        ModuleInsertBean mb = new ModuleInsertBean();
        mb.setFields(Arrays.asList(fieldArray));
        mb.setCreatorId(creatId);
        mb.setTableName(moduleTableName);
        mb.setModuleId(moduleid);
        List<List<Object>> values = new ArrayList<>();
        List<Object> eachValue;
        try {
            for (int i = 0; i < ja.size(); i++) {
                eachValue = new ArrayList<>();
                jo = ja.getJSONObject(i);
                for (String field : fieldArray) {
                    //根据配置的insertField 直接获取sql结果的值
                    eachValue.add(jo.getString(field));
                }
                values.add(eachValue);
            }
            mb.setValues(values);
            ModuleDataUtil.insertAc(mb);
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog("ProcDataUtil -- insertData expt :" + e.getMessage());
        }

        bb.writeLog("ProcDataUtil -- insertData ---END");
        return erroMsg;
    }

    /**
     * 清除建模表数据
     *
     * @param moduleTableName 建模表名
     */
    public static void clearModuleData(String moduleTableName) {
        RecordSet rs = new RecordSet();
        rs.executeUpdate("truncate table " + moduleTableName);
    }

}
