package com.engine.contract.util;

import com.wbi.util.DateHelper;
import weaver.conn.RecordSetTrans;
import weaver.formmode.data.ModeDataIdUpdate;
import weaver.formmode.setup.ModeRightInfo;
import weaver.hrm.User;


/**
 * 建模表工具列
 */
public class InsertModuleUtil4samtable {

    /**
     * 插入建模表一条数据
     *
     * @param table       表名uf_xxxx
     * @param insertField 插入字段的id数组
     * @param value       插入字段的id对应的值数组(和字段id顺序对应)
     * @param Create      创建人id
     * @param module      模块id
     */

    public static int insert(String table, String[] insertField, String[] value, int Create, int module, RecordSetTrans recordSetTrans) throws Exception {
//        ModeDataIdUpdate modeDataIdUpdate = new ModeDataIdUpdate();
        ModeRightInfo modeRightInfo = new ModeRightInfo();
        User user = new User(Create);   //用户id
// 初始化建模数据返回id
        int billid = ModeDataIdUpdate.getInstance().getModeDataNewIdByUUID(
                table, module, user.getUID(), (user.getLogintype()).equals("1") ? 0 : 1, DateHelper.getCurrentDate(), DateHelper.getCurrentTime());
//将数据的其他内容更新到表中
        StringBuilder stringBuffer = new StringBuilder();
        for (int i = 0; i < insertField.length; i++) {
            stringBuffer.append(insertField[i]).append("=").append("?");
            if (i != insertField.length - 1) {
                stringBuffer.append(", ");
            }
        }

        recordSetTrans.executeUpdate("update " + table + " set " + stringBuffer.toString() + " where id=" + billid, value);
//添加对应数据的权限


        return billid;
    }


}
