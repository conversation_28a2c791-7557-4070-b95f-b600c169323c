package com.engine.contract.util;

import com.engine.contract.common.AmountAndDay;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;

public class Utils {

    public static Date getLastMonthDate(Date date) {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        //设置日期
        calendar.setTime(date);
        // 将日期设置为下个月
        calendar.add(Calendar.MONTH, -1);
        // 获取上个月的时间
        return calendar.getTime();
    }


    public static Date getNextMonthDate(Date date) {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        //设置日期
        calendar.setTime(date);
        // 将日期设置为下个月
        calendar.add(Calendar.MONTH, 1);
        // 获取上个月的时间
        return calendar.getTime();
    }

    //获取某月天数
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }


    public static int getDiscrepantDays(Date dateStart, Date dateEnd) {
        //先将小时,分,秒设置为零,防止转换造成一天的误差
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(dateStart);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        Date date1 = calendar.getTime();
        calendar.setTime(dateEnd);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        Date date2 = calendar.getTime();
        long day = (date2.getTime() - date1.getTime()) / (24 * 3600 * 1000);
        return (int) day + 1;
    }

    //获取年份
    public static int getyear(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.YEAR);
    }

    //获取月份
    public static int getMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.MONTH) + 1;
    }


    //获取日期
    public static int getDays(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DATE);
    }

    //日月获取时间类
    public static Date setDatefunction(int year, int month, int day) {
        month--;
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month, day);  //年月日  也可以具体到时分秒如calendar.set(2015, 10, 12,11,32,52);
        return calendar.getTime();
    }

    //获取时间类
    public static Date setDatefunction(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(date);
        } catch (ParseException e) {
            return null;
        }
    }


    public static Date get36912(Date date) {
        int month = getMonth(date);
        int year = getyear(date);
        if (month % 4 == month) {
            return setDatefunction(year, 4, 1);
        } else if (month % 7 == month) {
            return setDatefunction(year, 7, 1);
        } else if (month % 10 == month) {
            return setDatefunction(year, 10, 1);
        } else {
            return setDatefunction(year + 1, 1, 1);
        }
    }

    public static Date getSeasonStart(int num, int year) {
        if (num == 1) {
            return setDatefunction(year, 1, 1);
        } else if (num == 2) {
            return setDatefunction(year, 4, 1);
        } else if (num == 3) {
            return setDatefunction(year, 7, 1);
        } else {
            return setDatefunction(year, 10, 1);
        }

    }

    public static int getSeason(int month) {
        if (month % 4 == month) {
            return 1;
        } else if (month % 7 == month) {
            return 2;
        } else if (month % 10 == month) {
            return 3;
        } else {
            return 4;
        }
    }


    public static boolean isSeasonstart(Date date) {
        if (getDays(date) == 1 && (
                getMonth(date) == 1 ||
                        getMonth(date) == 4 ||
                        getMonth(date) == 7 ||
                        getMonth(date) == 10)) {
            return true;
        }
        return false;
    }

    //某月剩下天数(算当天)
    public static int getnofullmonthDays(Date date) {
        return getDaysOfMonth(date) - getDays(date);
    }


    //加i月
    public static Date addmonnth(Date date, int i) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, i);
        return calendar.getTime();
    }

    //加（减）i天
    public static Date subDay(Date date, int i) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DATE, i);
        return calendar.getTime();
    }


    public static BigDecimal null20(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return new BigDecimal("0.00");
        }
        return bigDecimal;
    }

    public static BigDecimal null20(String str) {
        if (str == null || "".equals(str)) {
            return new BigDecimal("0.00");
        }
        return new BigDecimal(str);
    }

    public static HashMap<Integer, AmountAndDay> null2Object(HashMap<Integer, AmountAndDay> map) {
        if (map == null) {
            return new HashMap<Integer, AmountAndDay>();
        }
        return map;
    }

    public static BigDecimal[] null2Bigdecimal4number(BigDecimal[] bigDecimals) {
        if (bigDecimals == null) {
            BigDecimal[] bigDecimals1 = new BigDecimal[4];
            for (int i = 0; i < 4; i++) {
                bigDecimals1[i] = new BigDecimal("0.00");
            }
            return bigDecimals1;
        }
        return bigDecimals;
    }

    public static HashMap<Integer, BigDecimal[]> null2Object4Bigdecimal(HashMap<Integer, BigDecimal[]> map) {
        if (map == null) {
            return new HashMap<Integer, BigDecimal[]>();
        }
        return map;
    }

    public static AmountAndDay null2no1(AmountAndDay amountAndDay) {
        if (amountAndDay == null) {
            return new AmountAndDay(0, new BigDecimal("0.00"));
        }
        return amountAndDay;
    }

}
