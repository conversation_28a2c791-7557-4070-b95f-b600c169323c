package com.engine.contract.cornjob;

import com.engine.contract.util.InsertModuleUtil4samtable;
import com.engine.contract.util.Utils;
import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;

public class InsertAreaNumEvery {

    private RecordSetTrans recordSetTrans;

    private RecordSetTrans recordSetTran4report;

    private BaseBean baseBean;

    public RecordSetTrans getRecordSetTrans() {
        return recordSetTrans;
    }

    public void setRecordSetTrans(RecordSetTrans recordSetTrans) {
        this.recordSetTrans = recordSetTrans;
    }

    public InsertAreaNumEvery() {
        this.recordSetTrans = new RecordSetTrans();
        this.recordSetTran4report = new RecordSetTrans();
        this.baseBean = new BaseBean();
    }

    public RecordSetTrans getRecordSetTran4report() {
        return recordSetTran4report;
    }

    public void setRecordSetTran4report(RecordSetTrans recordSetTran4report) {
        this.recordSetTran4report = recordSetTran4report;
    }

    public BaseBean getBaseBean() {
        return baseBean;
    }

    public void setBaseBean(BaseBean baseBean) {
        this.baseBean = baseBean;
    }

    public boolean doexecute(String date) {
        Date nowdate = Utils.setDatefunction(date);
        boolean flag = init(nowdate);
        if (!flag) {
            return false;
        }
        return true;
    }


    public boolean doSeason(Date nowdate) {

        Date truedate = Utils.subDay(nowdate, -1);
        int season = Utils.getSeason(Utils.getMonth(truedate));

        String sql = "SELECT id FROM uf_czl_jd WHERE n=" + Utils.getyear(truedate) + "AND jd = " + season;
        String searchsql = "\t\t\tSELECT\n" +
                "\t\t\tSUM(taba.drczl)/COUNT(*) drczl,\n" +
                "\t\t\tSUM(tabb.drczl)/COUNT(*) drcmp,\n" +
                "\t\t\tSUM(tabc.drczl)/COUNT(*) drc863\n" +
                "\t\t\tFROM\n" +
                "\t\t\t\tuf_mjhz taba\n" +
                "\t\t\t\tLEFT JOIN uf_mjhz_mp tabb ON taba.rq = tabb.rq\n" +
                "\t\t\t\tLEFT JOIN uf_mjfz_863 tabc ON taba.rq = tabb.rq " +
                "\t\t\tWHERE taba.rq <= '" + DateHelper.getDate(truedate) + "' AND taba.rq >= '" + DateHelper.getDate(Utils.getSeasonStart(season, Utils.getyear(truedate))) + "'";
        RecordSet recordSet = new RecordSet();
        String[] cols = {"n", "jd", "yqpjczl", "pjczl", "mppjczl"};
        recordSet.execute(searchsql);
        if (recordSet.next()) {
            try {
                recordSetTran4report.execute(sql);
                if (recordSetTran4report.next()) {
                    recordSetTran4report.execute("update uf_czl_jd set yqpjczl = '" + Util.null2String(recordSet.getString("drczl")) + "'," +
                            " pjczl ='" + Util.null2String(recordSet.getString("drc863")) + "' , mppjczl = '" + Util.null2String(recordSet.getString("drcmp")) + "' " +
                            " where n = '" + Utils.getyear(truedate) + "' AND jd = " + season);
                } else {
                    InsertModuleUtil4samtable.insert("uf_czl_jd", cols, new String[]{
                            String.valueOf(Utils.getyear(truedate)),
                            String.valueOf(season),
                            Util.null2o(recordSet.getString("drczl")),
                            Util.null2o(recordSet.getString("drc863")),
                            Util.null2o(recordSet.getString("drcmp"))
                    }, 1, Integer.parseInt(baseBean.getPropValue("863_mode", "uf_czl_jd")), recordSetTran4report);

                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }

    public boolean doMonth(Date nowdate) {
        RecordSet recordSet = new RecordSet();
        Date truedate = Utils.subDay(nowdate, -1);
        String ny = DateHelper.getDate(truedate).substring(0, 7);
        int year = Utils.getyear(truedate);
        int month = Utils.getMonth(truedate);
        String sql = "SELECT id FROM uf_czl_yd WHERE ny = '" + ny + "'";
        String searchsql = "\t\t\tSELECT\n" +
                "\t\t\tSUM(taba.drczl)/COUNT(*) drczl,\n" +
                "\t\t\tSUM(tabb.drczl)/COUNT(*) drcmp,\n" +
                "\t\t\tSUM(tabc.drczl)/COUNT(*) drc863\n" +
                "\t\t\tFROM\n" +
                "\t\t\t\tuf_mjhz taba\n" +
                "\t\t\t\tLEFT JOIN uf_mjhz_mp tabb ON taba.rq = tabb.rq\n" +
                "\t\t\t\tLEFT JOIN uf_mjfz_863 tabc ON taba.rq = tabb.rq " +
                "\t\t\tWHERE taba.rq <= '" + DateHelper.getDate(truedate) + "' AND taba.rq >= '" + DateHelper.getDate(Utils.setDatefunction(year, month, 1)) + "'";
        String[] cols = {"ny", "yqpjczl", "pjczl", "mppjczl"};
        recordSet.execute(searchsql);
        if (recordSet.next()) {
            try {
                recordSetTran4report.execute(sql);
                if (recordSetTran4report.next()) {
                    recordSetTran4report.execute("update uf_czl_yd set yqpjczl = '" + Util.null2String(recordSet.getString("drczl")) + "'," +
                            " pjczl ='" + Util.null2String(recordSet.getString("drc863")) + "' , mppjczl = '" + Util.null2String(recordSet.getString("drcmp")) + "' " +
                            " where ny = '" + ny + "'");
                } else {
                    InsertModuleUtil4samtable.insert("uf_czl_yd", cols, new String[]{
                            ny,
                            Util.null2o(recordSet.getString("drczl")),
                            Util.null2o(recordSet.getString("drc863")),
                            Util.null2o(recordSet.getString("drcmp"))
                    }, 1, Integer.parseInt(baseBean.getPropValue("863_mode", "uf_czl_yd")), recordSetTran4report);

                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;

    }

    //年份
    public boolean doYear(Date nowdate) {
        RecordSet recordSet = new RecordSet();
        Date truedate = Utils.subDay(nowdate, -1);
        int year = Utils.getyear(truedate);
        int month = Utils.getMonth(truedate);
        String sql = "SELECT id FROM uf_czl_nd WHERE n = " + year;
        String searchsql = "\t\t\tSELECT\n" +
                "\t\t\tSUM(taba.drczl)/COUNT(*) drczl,\n" +
                "\t\t\tSUM(tabb.drczl)/COUNT(*) drcmp,\n" +
                "\t\t\tSUM(tabc.drczl)/COUNT(*) drc863\n" +
                "\t\t\tFROM\n" +
                "\t\t\t\tuf_mjhz taba\n" +
                "\t\t\t\tLEFT JOIN uf_mjhz_mp tabb ON taba.rq = tabb.rq\n" +
                "\t\t\t\tLEFT JOIN uf_mjfz_863 tabc ON taba.rq = tabb.rq " +
                "\t\t\tWHERE taba.rq <= '" + DateHelper.getDate(truedate) + "' AND taba.rq >= '" + DateHelper.getDate(Utils.setDatefunction(year, 1, 1)) + "'";
        String[] cols = {"n", "yqpjczl", "pjczl", "mppjczl"};
        recordSet.execute(searchsql);
        if (recordSet.next()) {
            try {
                recordSetTran4report.execute(sql);
                if (recordSetTran4report.next()) {
                    recordSetTran4report.execute("update uf_czl_nd set yqpjczl = '" + Util.null2String(recordSet.getString("drczl")) + "'," +
                            " pjczl ='" + Util.null2String(recordSet.getString("drc863")) + "' , mppjczl = '" + Util.null2String(recordSet.getString("drcmp")) + "' " +
                            " where n = " + year);
                } else {
                    InsertModuleUtil4samtable.insert("uf_czl_nd", cols, new String[]{
                            String.valueOf(year),
                            Util.null2o(recordSet.getString("drczl")),
                            Util.null2o(recordSet.getString("drc863")),
                            Util.null2o(recordSet.getString("drcmp"))
                    }, 1, Integer.parseInt(baseBean.getPropValue("863_mode", "uf_czl_nd")), recordSetTran4report);

                }
            } catch (Exception e) {
                e.printStackTrace();
                return false;
            }
        }
        return true;
    }


    //默认插入面积汇总表
    public boolean init(Date nowdate) {
        //获取配置文件
        String startdate = baseBean.getPropValue("863_mode", "start_date");
        String[] maincol = {"rq", "czllj", "pjczltsc", "pjczl", "jzmj", "kczmj", "yczmj", "sykczmj", "yzfkczmj", "drczl", "ptmj"};
        recordSetTrans.setAutoCommit(false);
        int mainid = 1;
        try {
            String modeid;
            String tablemame;
            for (int i = 0; i < 3; i++) {
                if (i == 0) {
                    tablemame = "uf_mjfz_863";
                    modeid = "mjfz_863";
                } else if (i == 1) {
                    tablemame = "uf_mjhz_mp";
                    modeid = "mjhz_mp";
                } else {
                    tablemame = "uf_mjhz";
                    modeid = "mjhz_module";
                }
                String[] value = new String[]{
                        DateHelper.getDate(Utils.subDay(nowdate, -1)),
                        getnumczl(i, nowdate),
                        String.valueOf(getDaysubtract(startdate, DateHelper.getDate(nowdate))),
                        getave(startdate, DateHelper.getDate(nowdate), i),
                        buildarea(i),
                        canhirearea(i),
                        hadhirearea(i),
                        remainderarea(i),
                        hadAndcantarea(i),
                        getrodayrate(i),
                        assorted(i)
                };
                RecordSet recordSet = new RecordSet();
                recordSet.execute("SELECT rq,id FROM " + tablemame + " WHERE rq = '" + DateHelper.getDate(Utils.subDay(nowdate, -1)) + "'");
                if (recordSet.next()) {
                    recordSetTrans.execute("DELETE FROM " + tablemame + "_dt1" + " WHERE mainid = '" + recordSet.getString("id") + "'");
                    recordSetTrans.execute("DELETE FROM " + tablemame + " WHERE rq = '" + DateHelper.getDate(Utils.subDay(nowdate, -1)) + "'");
                }
                new BaseBean().writeLog(Arrays.toString(maincol));
                new BaseBean().writeLog(Arrays.toString(value));
                mainid = InsertModuleUtil4samtable.insert(
                        tablemame, maincol, value, 1,
                        Integer.parseInt(baseBean.getPropValue("863_mode", modeid)),
                        recordSetTrans);
                //明细
                insertdetail(tablemame + "_dt1", mainid, i, recordSetTrans);
            }
            recordSetTrans.commit();
        } catch (Exception e) {
            recordSetTrans.rollback();
            e.printStackTrace();
            return false;
        }

        return true;
    }


    public boolean insertdetail(String table, int mainid, int needshowcompany, RecordSetTrans recordSetTrans) throws Exception {
        RecordSet recordSet = new RecordSet();
        String insql = "";
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            insql = " 0,1 ";
        }
        if (needshowcompany == 1 || needshowcompany == 2) {
            recordSetTrans.execute("insert into " + table + " (mainid,lb,ldmc,ldbh,lzgs,jzmj,ptmj,kczmj,yczmj,sykczmj,yzfkczmj,drczl) values (" +
                    mainid + ",6,24,'MP06',1,16887.89,0,16887.89,16887.89,0,0,1)");
        }
        //明细
        String sql = "SELECT\n" +
                "\ttabc.*,\n" +
                "\tisnull( tabd.ptmj, 0 ) ptmj,\n" +
                "\tCONVERT (\n" +
                "\t\tDECIMAL ( 10, 2 ),\n" +
                "\t\tisnull( ( yczmj + yzfkczmj ) / NULLIF ( ( kczmj + yzfkczmj ), 0 ), 0 ) \n" +
                "\t) czl \n" +
                "FROM\n" +
                "\t(\n" +
                "\tSELECT\n" +
                "\t\ttabb.lb,\n" +
                "\t\ttabb.id as ldxx,\n" +
                "\t\ttabb.ldbh,\n" +
                "\t\ttabb.lzgs,\n" +
                "\t\tSUM ( isnull( taba.jzmj, 0 ) ) jzmj,\n" +
                "\t\tSUM ( isnull( taba.kczmj, 0 ) ) sykczmj,\n" +
                "\t\t(SUM ( isnull( taba.kczmj, 0 ) )+ SUM ( isnull( taba.yczmj, 0 ) )) kczmj ,\n" +
                "\t\tSUM ( isnull( taba.yczmj, 0 ) ) yczmj,\n" +
                "\t\tSUM ( isnull( taba.yzfkczmj , 0 ) ) yzfkczmj \n" +
                "\t FROM\n" +
                "\t uf_zc_ldxx tabb\n" +
                "\t\tLEFT JOIN uf_zs_qyxx taba ON taba.ldxx = tabb.id \n" +
                "\t\tWHERE tabb.id<>24 AND taba.sfsx =0\n" +
                "\tGROUP BY\n" +
                "\t\ttabb.lb,\n" +
                "\t\ttabb.id,\n" +
                "\t\ttabb.ldbh,\n" +
                "\t\ttabb.lzgs \n" +
                "\t) tabc\n" +
                "\tLEFT JOIN ( SELECT SUM ( jzmj ) ptmj, ldxx AS ldxx2 FROM uf_zs_qyxx WHERE sfpt = 0 GROUP BY ldxx ) tabd ON tabd.ldxx2 = tabc.ldxx " +
                " where lzgs IN (" + insql + ") order by convert(int ,ldxx) DESC ";
        String[] cols = {"lb", "ldmc", "ldbh", "lzgs", "jzmj", "kczmj", "yczmj", "yzfkczmj", "ptmj"};
        recordSet.execute(sql);
        while (recordSet.next()) {
            recordSetTrans.execute("insert into " + table + " (mainid,lb,ldmc,ldbh,lzgs,jzmj,ptmj,kczmj,yczmj,sykczmj,yzfkczmj,drczl) values (" +
                    mainid + "," +
                    Util.null2String(recordSet.getString("lb")) + "," +
                    Util.null2String(recordSet.getString("ldxx")) + ",'" +
                    Util.null2String(recordSet.getString("ldbh")) + "'," +
                    Util.null2String(recordSet.getString("lzgs")) + "," +
                    Util.null2String(recordSet.getString("jzmj")) + "," +
                    Util.null2String(recordSet.getString("ptmj")) + "," +
                    Util.null2String(recordSet.getString("kczmj")) + "," +
                    Util.null2String(recordSet.getString("yczmj")) + "," +
                    Util.null2String(recordSet.getString("sykczmj")) + "," +
                    Util.null2String(recordSet.getString("yzfkczmj")) + "," +
                    Util.null2String(recordSet.getString("czl")) +
                    ")");
        }
        return true;
    }

    //当日出租率
    public String getrodayrate(int needshowcompany) {
        BigDecimal canandno = new BigDecimal(hadAndcantarea(needshowcompany)); //已租非可租
        BigDecimal have = new BigDecimal(hadhirearea(needshowcompany)).add(canandno);  //已出租
        BigDecimal can = new BigDecimal(canhirearea(needshowcompany)).add(canandno); //可出租
        return have.divide(can, 4, BigDecimal.ROUND_UP).toString();
    }

    //出租率累加
    public String getnumczl(int needshowcompany, Date date) throws Exception {
        String insql;
        if (needshowcompany == 1) {
            insql = " uf_mjhz_mp ";
        } else if (needshowcompany == 0) {
            insql = "uf_mjfz_863 ";
        } else {
            insql = " uf_mjhz ";
        }
        String sql = " SELECT czllj FROM " + insql + " WHERE rq < '" + DateHelper.getDate(Utils.subDay(date, -1)) + "' ORDER BY rq DESC";
        recordSetTrans.execute(sql);
        if (recordSetTrans.next()) {
            return new BigDecimal(Util.null2String(recordSetTrans.getString("czllj"))).add(new BigDecimal(getrodayrate(needshowcompany))).toString();
        }
        return getrodayrate(needshowcompany);
    }


    //天数差减一
    public int getDaysubtract(String startdate, String endDate) {
        return Utils.getDiscrepantDays(Utils.setDatefunction(startdate), Utils.setDatefunction(endDate)) - 1;
    }

    //平均出租率
    public String getave(String startdate, String enddate, int needshowcompany) throws Exception {
        return new BigDecimal(getnumczl(needshowcompany, Utils.setDatefunction(enddate))).divide(new BigDecimal(getDaysubtract(startdate, enddate)), 4, BigDecimal.ROUND_UP).toString();
    }

    //建筑面积汇总
    public String buildarea(int needshowcompany) {
        String insql = "";
        BigDecimal addnumber = new BigDecimal("0.00");
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT sum(taba.jzmj) jzmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id  WHERE taba.sfsx =0 AND tabb.lzgs IN (" + insql + ") ";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return addnumber.add(Utils.null20(Util.null2String(recordSet.getString("jzmj")))).toString();
        }
        return addnumber.toString();
    }

    //可出租面积汇总
    public String canhirearea(int needshowcompany) {

        String insql = "";
        BigDecimal addnumber = new BigDecimal("0.00");

        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "\tSELECT sum(taba.jzmj) jzmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id  WHERE taba.sfsx =0 AND taba.sfpt = 1 AND tabb.lzgs IN (" + insql + ")";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return addnumber.add(Utils.null20(Util.null2String(recordSet.getString("jzmj")))).toString();
        }
        return addnumber.toString();
    }


    //已出租面积汇总
    public String hadhirearea(int needshowcompany) {
        String insql = "";
        BigDecimal addnumber = new BigDecimal("0.00");
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            addnumber = addnumber.add(new BigDecimal("16887.89"));
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT sum(taba.yczmj) yczmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id WHERE taba.sfsx =0 AND tabb.lzgs IN (" + insql + ")";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return addnumber.add(Utils.null20(Util.null2String(Util.null2String(recordSet.getString("yczmj"))))).toString();
        }
        return addnumber.toString();
    }

    //剩余可出租面积汇总
    public String remainderarea(int needshowcompany) {
        String insql = "";
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "\tSELECT sum(taba.kczmj) kczmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id WHERE taba.sfsx =0 AND tabb.lzgs IN (" + insql + ")";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return Util.null2String(recordSet.getString("kczmj"));
        }
        return "0.00";
    }


    //已租非可租面积汇总
    public String hadAndcantarea(int needshowcompany) {
        String insql = "";
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "\tSELECT sum(taba.yzfkczmj) yzfkczmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id WHERE taba.sfsx =0 AND tabb.lzgs IN (" + insql + ")";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return Util.null2String(recordSet.getString("yzfkczmj"));
        }
        return "0.00";
    }

    //配套
    public String assorted(int needshowcompany) {
        String insql = "";
        if (needshowcompany == 0) {
            insql = " 0 ";
        }
        if (needshowcompany == 1) {
            insql = " 1 ";
        }
        if (needshowcompany == 2) {
            insql = " 0,1 ";
        }
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT sum(jzmj) jzmj FROM uf_zs_qyxx taba LEFT JOIN uf_zc_ldxx tabb ON taba.ldxx = tabb.id  WHERE taba.sfsx =0 AND taba.sfpt=0 AND tabb.lzgs IN (" + insql + ")";
        recordSet.execute(sql);
        if (recordSet.next()) {
            return Util.null2String(recordSet.getString("jzmj"));
        }
        return "0.00";
    }


}
