package com.engine.contract.cornjob;

import weaver.conn.RecordSetTrans;
import weaver.interfaces.schedule.BaseCronJob;

public class CheckStatus extends BaseCronJob {

    public CheckStatus() {


    }

    // uf_cwzl_jcht 	htlx
    @Override
    public void execute() {
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        String sql = "UPDATE uf_cwzl_jcht SET htzt = 1 WHERE \tjsrq < getdate() AND htlx IN (0,1,2)";
        String sql2 = "UPDATE uf_cwzl_jcht SET htzt = 1 WHERE \tzzsxrq < getdate() AND htlx =3 ";
        try {
            recordSetTrans.execute(sql);
            recordSetTrans.execute(sql2);
        } catch (Exception e) {
            recordSetTrans.rollback();
            e.printStackTrace();
        }
    }
}
