package com.engine.contract.cornjob;

import com.engine.contract.util.Utils;
import com.weaver.formmodel.util.DateHelper;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.Date;

public class testcornjob extends BaseCronJob {

    private String specificDay;

    public String getSpecificDay() {
        return specificDay;
    }

    public void setSpecificDay(String specificDay) {
        this.specificDay = specificDay;
    }

    public testcornjob() {
    }

    @Override
    public void execute() {
        InsertAreaNum insertAreaNum = new InsertAreaNum();
        insertAreaNum.doexecute(specificDay);
    }
}
