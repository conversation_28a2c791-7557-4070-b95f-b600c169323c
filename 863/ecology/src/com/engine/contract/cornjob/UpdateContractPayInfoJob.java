package com.engine.contract.cornjob;

import com.engine.contract.action.UpdateContractPayInfoAction;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.List;

/**
 * 定时任务更新合同付款信息
 * 解决跨年时，去年的合同金额没有更新到历年合计里的问题
 */
public class UpdateContractPayInfoJob extends BaseCronJob {

    private BaseBean bb;

    private void _initBaseBean() {
        bb = new BaseBean();
    }

    @Override
    public void execute() {
        _initBaseBean();
        bb.writeLog(this.getClass().getName() + "---START");
        String erroMsg = "";
        try {
            RecordSet rs = new RecordSet();
            List<String> listCon = new ArrayList<>();
            rs.executeQuery(" select htbh from uf_htfk ");
            while (rs.next()) {
                listCon.add(Util.null2String(rs.getString("htbh")));
            }
            bb.writeLog("listCon：" + listCon);
            if (!listCon.isEmpty()) {
                UpdateContractPayInfoAction ac = new UpdateContractPayInfoAction();
                bb.writeLog("UpdateContractPayInfoAction 对象创建OK");
                erroMsg = ac.updateData(listCon);
            } else {
                bb.writeLog("本次定时任务执行，无合同编号信息");
            }
        } catch (Exception e) {
            e.printStackTrace();
            bb.writeLog(e.getMessage());
            erroMsg = e.getMessage();
        }
        if (StringUtils.isNotBlank(erroMsg)) {
            bb.writeLog("本次定时任务执行数据失败，失败信息：" + erroMsg);
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }
}
