package com.engine.contract.service.impl;

import com.engine.contract.cmd.CreatePayablereportCmd;
import com.engine.contract.cmd.ShowCallReportCmd;
import com.engine.contract.cmd.UpdateAllCmd;
import com.engine.contract.cmd.UpdateAllPaymentCmd;
import com.engine.contract.service.ReportService;
import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Map;

public class ReportServiceImpl extends Service implements ReportService {

    @Override
    public Map<String, Object> updateReportData(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateAllCmd(params, user));
    }

    @Override
    public Map<String, Object> createPayablereport(Map<String, Object> params, User user) {
        return commandExecutor.execute(new CreatePayablereportCmd(params, user));
    }

    @Override
    public Map<String, Object> UpdateAfterReport(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UpdateAllPaymentCmd(params, user));
    }

    @Override
    public Map<String, Object> ShowCallReport(Map<String, Object> params, User user) {
        return commandExecutor.execute(new ShowCallReportCmd(params, user));
    }
}
