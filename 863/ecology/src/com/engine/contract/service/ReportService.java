package com.engine.contract.service;

import weaver.hrm.User;

import java.util.Map;

public interface ReportService {
    /**
     * 更新报表数据
     *
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> updateReportData(Map<String, Object> params, User user);


    Map<String, Object> createPayablereport(Map<String, Object> params, User user);

    Map<String, Object> UpdateAfterReport(Map<String, Object> params, User user);

    Map<String, Object> ShowCallReport(Map<String, Object> params, User user);
}
