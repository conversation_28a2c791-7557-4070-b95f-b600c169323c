package com.engine.contract.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ParamUtil;
import com.engine.common.util.ServiceUtil;
import com.engine.contract.service.ReportService;
import com.engine.contract.service.impl.ReportServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.Map;

public class ReportAction {
    private ReportService getService(User user) {

        return ServiceUtil.getService(ReportServiceImpl.class, user);
    }

    /**
     * 一键更新数据报表数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/updateReportData")
    @Produces(MediaType.TEXT_PLAIN)
    public String updateReportData(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).updateReportData(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 一键更新数据报表数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/createPayablereport")
    @Produces(MediaType.TEXT_PLAIN)
    public String createPayablereport(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).createPayablereport(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 一键更新数据报表数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/updateAfterReport")
    @Produces(MediaType.TEXT_PLAIN)
    public String updateAfterReport(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).UpdateAfterReport(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }


    /**
     * 一键更新数据报表数据
     *
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @POST
    @Path("/showCallReport")
    @Produces(MediaType.TEXT_PLAIN)
    public String showCallReport(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        Map<String, Object> result = new HashMap<>(2);
        try {
            User user = HrmUserVarify.getUser(request, response);
            if (user != null) {
                Map<String, Object> params = ParamUtil.request2Map(request);
                result = getService(user).ShowCallReport(params, user);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
            result.put("status", "-1");
            result.put("msg", "catch exception : " + ex.getMessage());
        }
        return JSONObject.toJSONString(result);
    }
}
