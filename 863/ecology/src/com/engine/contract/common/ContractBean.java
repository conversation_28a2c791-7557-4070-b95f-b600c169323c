package com.engine.contract.common;

import lombok.Data;

import java.util.List;

@Data
public class ContractBean extends ContractTree {

    /**
     * id
     */
    private String id;

    /**
     * 合同编号
     */
    private String htbh;

    /**
     * 是否递增 0 是 1 否
     */
    private String flag;

//    /**
//     * 合同类型
//     */
//    private String htlx;

    /**
     * 合同开始时间
     */
    private String startDate;

    /**
     * 起租日期
     */
    private String qzrq;

    /**
     * 结算日期
     */
    private String jsrq;

    /**
     * 合同结束时间
     */
    private String endDate;

    /**
     * 父合同失效时间
     */
    private String moveDate;

    /**
     * 租金
     */
    private String rent;

    /**
     * 面积
     */
    private String area;

//    /**
//     * 上一个合同 老合同 流程
//     */
//    private String lylc;
//
//    /**
//     * 上一个合同 老合同 流程
//     */
//    private String yhtlc;
    /**
     * 企业
     */
    private String qy;

    /**
     * 区域信息
     */
    private String qyxx;

    /**
     * 楼栋
     */
    private String ld;

    /**
     * 楼栋信息
     */
    private String ldxx;

    /**
     * 楼栋信息
     */
    private String ldbm;

    /**
     * 合同明细
     */
    private List<ContractDetail> details;

//    /**
//     * 子合同
//     */
//    private List<ContractBean> children;
}
