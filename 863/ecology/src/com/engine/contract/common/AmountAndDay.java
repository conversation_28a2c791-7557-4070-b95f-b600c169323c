package com.engine.contract.common;

import java.math.BigDecimal;

public class AmountAndDay {

    private int days;
    private BigDecimal decimal;

    public AmountAndDay(int days, BigDecimal decimal) {
        this.days = days;
        this.decimal = decimal;
    }

    public int getDays() {
        return days;
    }

    public void setDays(int days) {
        this.days = days;
    }

    public BigDecimal getDecimal() {
        return decimal;
    }

    public void setDecimal(BigDecimal decimal) {
        this.decimal = decimal;
    }
}
