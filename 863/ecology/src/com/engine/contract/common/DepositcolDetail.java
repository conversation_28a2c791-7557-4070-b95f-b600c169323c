package com.engine.contract.common;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;

@Data
public class DepositcolDetail {

    public DepositcolDetail() {
        this.historyContract = new ArrayList<>();
        this.Amount = BigDecimal.ZERO;
        this.BackAmount = BigDecimal.ZERO;
    }

    /**
     * 所有历史合同流程
     */
    private ArrayList<String> historyContract;

    /**
     * 当前在执行合同流程
     */
    private String currentContract;

    /**
     * 下个月合同流程
     */
    private String nextContract;


    /**
     * 上个月执行合同流程
     */
    private String lastContract;

    /**
     * 上个月保证金说明
     */
    private String LastRemark;


    /**
     * 下个月保证金说明
     */
    private String nextRemark;

    /**
     * 保证金
     */
    private BigDecimal Amount;

    /**
     * 下个月保证金
     */
    private BigDecimal NextAmount;

    /**
     * 上个月保证金
     */
    private BigDecimal LastAmount;

    /**
     * 退租保证金
     */
    private BigDecimal BackAmount;


    /**
     * 区域信息
     */
    private String qyxx;

}
