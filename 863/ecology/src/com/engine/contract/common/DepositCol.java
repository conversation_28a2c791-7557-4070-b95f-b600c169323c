package com.engine.contract.common;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class DepositCol {

    public DepositCol() {
        this.historyContract = new ArrayList<>();
        this.currentContract = new ArrayList<>();
        this.amount = BigDecimal.ZERO;
        this.backamount = BigDecimal.ZERO;
        this.details = new ArrayList<>();
        this.nextamountchange = BigDecimal.ZERO;
        this.lastamountchange = BigDecimal.ZERO;
        this.ischange = "0";
    }

    /**
     * 所有历史合同流程
     */
    private ArrayList<String> historyContract;

    /**
     * 当前在执行合同流程
     */
    private ArrayList<String> currentContract;

    /**
     * 保证金合计
     */
    private BigDecimal amount;

    /**
     * 下个月保证金变化量
     */
    private BigDecimal nextamountchange;

    /**
     * 上个月保证金变化量
     */
    private BigDecimal lastamountchange;

    /**
     * 是否发生了保证金变化
     */
    private String ischange;

    /**
     * 退租/变更退还保证金合计
     */
    private BigDecimal backamount;

    /**
     * 明细
     */
    private List<DepositcolDetail> details;
}
