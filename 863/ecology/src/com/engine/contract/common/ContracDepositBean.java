package com.engine.contract.common;


import lombok.Data;

import java.util.List;

@Data
public class ContracDepositBean extends ContractTree {

    /**
     * id
     */
    private String id;


    /**
     * 归属
     */
    private String gz;


    /**
     * 企业
     */
    private String qy;

    /**
     * 是否递增 0 是 1 否
     */
    private String flag;

    /**
     * 区域信息
     */
    private String qyxx;

    /**
     * 合同开始时间
     */
    private String startDate;

    /**
     * 合同结束时间
     */
    private String endDate;


    /**
     * 父合同失效时间
     */
    private String moveDate;

    /**
     * 保证金
     */
    private String deposit;


    /**
     * 合同明细
     */
    private List<ContactDepositDetail> details;


}
