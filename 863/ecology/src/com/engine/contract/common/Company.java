package com.engine.contract.common;

import java.util.Objects;

public class Company {

    /**
     * 企业名称
     */
    private String name;

    /**
     * 所属闵浦 还是863
     */
    private String affiliation;

    public Company(String name, String affiliation) {
        this.name = name;
        this.affiliation = affiliation;
    }

    // Getters and setters (optional)

    @Override
    public int hashCode() {
        return Objects.hash(name, affiliation);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        Company other = (Company) obj;
        return Objects.equals(name, other.name) && Objects.equals(affiliation, other.affiliation);
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAffiliation() {
        return affiliation;
    }

    public void setAffiliation(String affiliation) {
        this.affiliation = affiliation;
    }
}