package com.engine.contract.action;

import com.engine.parent.common.util.SDUtil;
import org.apache.commons.lang.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;
import weaver.workflow.request.WorkflowRequestMessage;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

/**
 * 从流程更新合同付款信息台账action
 *
 * <AUTHOR>
 */
public class UpdateContractPayInfoAction extends BaseBean implements Action {

    private static final String HTBH_FIELD = "htbh";

    @Override
    public String execute(RequestInfo requestInfo) {
        writeLog(this.getClass().getName() + "-----START");
        //RequestManager对象，获取一些流转的信息
        RequestManager rm = requestInfo.getRequestManager();
        String erroMsg;
        //合同编号
        String htbh = "";

        try {
            writeLog("requestid：" + rm.getRequestid());
            //获取主表信息
            Property[] propertys = requestInfo.getMainTableInfo().getProperty();
            for (Property property : propertys) {
                String str = property.getName();
                String value = Util.null2String(property.getValue());
                if (HTBH_FIELD.equals(str)) {
                    htbh = value;
                    break;
                }
            }
            List<String> contractList = new ArrayList<>();
            contractList.add(htbh);
            writeLog("contractList：" + contractList);
            erroMsg = updateData(contractList);

        } catch (Exception e) {
            writeLog("Exception:" + e.getMessage());
            erroMsg = "Exception:" + e.getMessage();
        }
        if (StringUtils.isNotBlank(erroMsg)) {
            //流程提交失败信息编号
            rm.setMessageid(WorkflowRequestMessage.WF_REQUEST_ERROR_CODE_07);
            //流程提交失败信息内容
            rm.setMessagecontent(erroMsg);
            return Action.FAILURE_AND_CONTINUE;
        }
        writeLog(this.getClass().getName() + "-----END");
        return Action.SUCCESS;

    }

    /**
     * 更新合同付款台账数据
     *
     * @param contractList
     * @return
     */
    public String updateData(List<String> contractList) {
        writeLog(this.getClass().getName() + "updateData -----START");
        String erroMsg = "";
        if (contractList != null && !contractList.isEmpty()) {
            boolean rsFlag;
            //合同编号,sql
            String htbh, sql;
            RecordSet rs = new RecordSet();
            StringBuilder sb = new StringBuilder();
            //合同金额
            BigDecimal htje;
            //本年度已付金额合计
            BigDecimal byfhj;
            //历年已付金额合计
            BigDecimal lyfhj;
            //已付金额合计
            BigDecimal yfhj;
            //本年度审批中金额合计
            BigDecimal bspzhj;
            //历年审批中金额合计
            BigDecimal lspzhj;
            //审批中金额合计
            BigDecimal spzhj;
            //应付款金额
            BigDecimal yfk;

            try {
                Calendar cal = Calendar.getInstance();
                int currentYear = cal.get(Calendar.YEAR);
                writeLog("currentYear：" + currentYear);
                //获取相关数据
                sb.append(" SELECT ");
                sb.append(" TBL.*, ");
                //sb.append(" ( CASE WHEN b.sfzjsj = 0 THEN b.zjsjje WHEN b.sfzjsj = 1 THEN b.yhtje ELSE NULL END ) AS htje  ");
                //2023-08-02 更新，直接取uf_htfkqk的htje字段
                sb.append(" c.htje ");
                sb.append(" FROM ");
                sb.append(" ( ");
                sb.append(" SELECT ");
                sb.append(" a.htbh, ");
                sb.append(" SUM( CASE WHEN a.n = ? AND a.gdzt = 1 THEN a.sjfkje ELSE 0 END ) AS byfhj, ");
                sb.append(" SUM( CASE WHEN a.n != ? AND a.gdzt = 1 THEN a.sjfkje ELSE 0 END ) AS lyfhj, ");
                sb.append(" SUM( CASE WHEN a.n = ? AND a.gdzt = 0 THEN a.spzje ELSE 0 END ) AS bspzhj, ");
                sb.append(" SUM( CASE WHEN a.n != ? AND a.gdzt = 0 THEN a.spzje ELSE 0 END ) AS lspzhj  ");
                sb.append(" FROM ");
                sb.append(" uf_htfk a  ");
                sb.append(" WHERE 1=1 ");
                //拼接合同号的数据
                sb.append(" and ( ");
                for (int i = 0; i < contractList.size(); i++) {
                    if (i > 0) {
                        sb.append(" or ");
                    }
                    sb.append(" (a.htbh = '").append(contractList.get(i)).append("') ");
                }
                sb.append("  ) ");
                sb.append(" GROUP BY ");
                sb.append(" a.htbh  ");
                sb.append(" ) TBL ");
                //sb.append(" LEFT JOIN uf_htzje b ON ( TBL.htbh = b.htbh ) ");
                sb.append(" LEFT JOIN uf_htfkqk c ON ( TBL.htbh = c.htbh ) ");
                writeLog("query sql:" + sb);
                rsFlag = rs.executeQuery(sb.toString(), currentYear, currentYear, currentYear, currentYear);
                if (!rsFlag) {
                    erroMsg = "查询出错：" + rs.getMsg();
                } else {

                    try {
                        while (rs.next()) {
                            //合同编号
                            htbh = Util.null2String(rs.getString("htbh"));
                            htje = SDUtil.getBigDecimalValue(rs.getString("htje"));
                            byfhj = SDUtil.getBigDecimalValue(rs.getString("byfhj"));
                            lyfhj = SDUtil.getBigDecimalValue(rs.getString("lyfhj"));
                            bspzhj = SDUtil.getBigDecimalValue(rs.getString("bspzhj"));
                            lspzhj = SDUtil.getBigDecimalValue(rs.getString("lspzhj"));
                            yfhj = byfhj.add(lyfhj);
                            spzhj = bspzhj.add(lspzhj);
                            //应付款金额 =【合同金额】-【已付金额合计】-【审批中金额合计】
                            yfk = htje.subtract(yfhj).subtract(spzhj);
                            //计算：合同付款情况
                            int fkqk;
                            if (yfhj.compareTo(BigDecimal.valueOf(0)) == 0) {
                                //未付款
                                fkqk = 0;
                            } else if (yfhj.compareTo(htje) == 0) {
                                //已结清
                                fkqk = 2;
                            } else {
                                //付款中
                                fkqk = 1;
                            }
                            List<Object> sqlParams = new ArrayList<>();
                            sqlParams.add(fkqk);
                            //sqlParams.add(htje);
                            sqlParams.add(byfhj);
                            sqlParams.add(lyfhj);
                            sqlParams.add(yfhj);
                            sqlParams.add(lspzhj);
                            sqlParams.add(bspzhj);
                            sqlParams.add(spzhj);
                            sqlParams.add(yfk);
                            sqlParams.add(htbh);

                            //更新数据
                            //2023-07-12更新，取消更新htje字段，客服说是前面已经由流程转数据更新过了
                            sql = " update uf_htfkqk set " +
                                    " fkqk = ?," +
//                                    " htje = ?, " +
                                    " byfhj = ?, " +
                                    " lyfhj = ?, " +
                                    " yfhj = ?, " +
                                    " lspzhj = ?," +
                                    " bspzhj = ?, " +
                                    " spzhj = ?, " +
                                    " yfk = ? " +
                                    " where htbh = ? ";
                            RecordSet rs1 = new RecordSet();
                            writeLog("update uf_htfkqk params:" + sqlParams);
                            rs1.executeUpdate(sql, sqlParams);
                            //更新合同信息
                            sql = " update uf_httzmx set fkqk = ? where htbh = ? ";
                            RecordSet rs2 = new RecordSet();
                            writeLog("update uf_httzmx fkqk:" + fkqk + ",htbh:" + htbh);
                            rs2.executeUpdate(sql, fkqk, htbh);
                        }

                    } catch (Exception e) {
                        erroMsg = e.getMessage();
                        writeLog("Catch Exception:" + e.getMessage());
                        return erroMsg;
                    }
                }
            } catch (Exception e) {
                erroMsg = e.getMessage();
                writeLog("Catch Exception:" + e.getMessage());
                return erroMsg;
            }
        } else {
            erroMsg = "参数合同号集合为空!";
        }
        if (StringUtils.isNotBlank(erroMsg)) {
            writeLog("erroMsg:" + erroMsg);
        }
        return erroMsg;
    }
}
