package com.engine.contract.action;

import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;
import weaver.workflow.request.RequestManager;


public class Workflow2contractStatus extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {

        String requestid =  requestInfo.getRequestid();

        RecordSet set = new RecordSet();

        String sql = "UPDATE uf_cwzl_jcht \n" +
                "SET htzt = 1 \n" +
                "WHERE\n" +
                "\tlylc IN ( SELECT yhtlc FROM uf_cwzl_jcht WHERE lylc = ? )";

        if(set.executeUpdate(sql,requestid)){
            return Action.SUCCESS;
        }
        RequestManager rm = requestInfo.getRequestManager();
        rm.setMessagecontent("状态更新失败");
        return Action.FAILURE_AND_CONTINUE;
    }
}
