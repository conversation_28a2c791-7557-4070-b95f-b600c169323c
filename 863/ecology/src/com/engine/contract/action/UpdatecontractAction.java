package com.engine.contract.action;

import com.engine.contract.cmd.UpdateAllCmd;
import weaver.general.BaseBean;
import weaver.hrm.User;
import weaver.interfaces.workflow.action.Action;
import weaver.soa.workflow.request.RequestInfo;

import java.util.HashMap;
import java.util.Map;


public class UpdatecontractAction extends BaseBean implements Action {

    @Override
    public String execute(RequestInfo requestInfo) {
        User user = requestInfo.getRequestManager().getUser();
        Map<String, Object> params = new HashMap<>();
        UpdateAllCmd updateReportCmd = new UpdateAllCmd(params, user);
        updateReportCmd.doExecute();
        return Action.SUCCESS;
    }
}
