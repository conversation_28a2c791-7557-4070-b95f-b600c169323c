//package com.engine.contract.cmd;
//
//import com.engine.common.biz.AbstractCommonCommand;
//import com.engine.common.entity.BizLogContext;
//import com.engine.contract.util.InsertModuleUtil4samtable;
//import com.engine.contract.util.Utils;
//import com.engine.core.interceptor.CommandContext;
//import weaver.conn.RecordSet;
//import weaver.conn.RecordSetTrans;
//import weaver.general.Util;
//import weaver.hrm.User;
//
//import java.math.BigDecimal;
//import java.util.HashMap;
//import java.util.Map;
//
//
////今年不欠钱不生成 补缴生成
//@Deprecated
//public class UpdateAfterReportCmd extends AbstractCommonCommand<Map<String, Object>> {
//    @Override
//    public BizLogContext getLogContext() {
//        return null;
//    }
//
//    public UpdateAfterReportCmd(Map<String, Object> params, User user) {
//        this.params = params;
//        this.user = user;
//    }
//
//    @Override
//    public Map<String, Object> execute(CommandContext commandContext) {
//        return doExecute();
//    }
//
//    public Map<String, Object> doExecute() {
//        Map<String, Object> result = new HashMap<>();
//        String sql = " SELECT\n" +
//                "taba.hth,taba.nf,taba.qy,taba.qyxx,taba.ld,taba.ldxx,taba.ldbm,\n" +
//                "(isnull (taba.jdysje1,0)-isnull (taba.jdjss1,0)) as qf1 ,\n" +
//                "(isnull (taba.jdysje2,0)-isnull (taba.jdjss2,0)) as qf2 ,\n" +
//                "(isnull (taba.jdysje3,0)-isnull (taba.jdjss3,0)) as qf3 ,\n" +
//                "(isnull (taba.jdysje4,0)-isnull (taba.jdjss4,0)) as qf4 ,\n" +
//                "tabb.*\n" +
//                "FROM\n" +
//                "\tuf_cwzl_jdbb taba\n" +
//                "left JOIN \n" +
//                "(\n" +
//                " SELECT\n" +
//                "  taba.hth hth,\n" +
//                "  taba.nf nf,\n" +
//                "  (isnull(bj1, 0) + isnull(oldbj1, 0)) AS bj1,\n" +
//                "  (isnull(bj2, 0) + isnull(oldbj2, 0)) AS bj2,\n" +
//                "  (isnull(bj3, 0) + isnull(oldbj3, 0)) AS bj3,\n" +
//                "  (isnull(bj4, 0) + isnull(oldbj4, 0)) AS bj4\n" +
//                "FROM\n" +
//                "  (\n" +
//                "    SELECT\n" +
//                "      hth,\n" +
//                "      SUM (yjdbj) bj1,\n" +
//                "      SUM (ejdbj) bj2,\n" +
//                "      SUM (sjdbj) bj3,\n" +
//                "      SUM (sijdbj) bj4,\n" +
//                "      nf\n" +
//                "    FROM\n" +
//                "      uf_cwzl_jdbj\n" +
//                "    GROUP BY\n" +
//                "      hth,\n" +
//                "      nf\n" +
//                "  ) AS taba\n" +
//                "left  JOIN (\n" +
//                "    SELECT\n" +
//                "      taba.id AS id,\n" +
//                "      tabb.id AS old,\n" +
//                "      tabc.bj1 AS oldbj1,\n" +
//                "      tabc.bj2 AS oldbj2,\n" +
//                "      tabc.bj3 AS oldbj3,\n" +
//                "      tabc.bj4 AS oldbj4,\n" +
//                "      tabc.nf AS oldnf\n" +
//                "    FROM\n" +
//                "      uf_cwzl_jcht taba\n" +
//                "      LEFT JOIN uf_cwzl_jcht tabb ON taba.yhthtbh = tabb.htbh\n" +
//                "      LEFT JOIN (\n" +
//                "        SELECT\n" +
//                "          hth,\n" +
//                "          SUM (yjdbj) bj1,\n" +
//                "          SUM (ejdbj) bj2,\n" +
//                "          SUM (sjdbj) bj3,\n" +
//                "          SUM (sijdbj) bj4,\n" +
//                "          nf\n" +
//                "        FROM\n" +
//                "          uf_cwzl_jdbj\n" +
//                "        GROUP BY\n" +
//                "          hth,\n" +
//                "          nf\n" +
//                "      ) AS tabc ON tabb.id = tabc.hth\n" +
//                "  ) tabb ON taba.hth = tabb.id\n" +
//                "  AND tabb.oldnf = taba.nf \n" +
//                "\n" +
//                ") tabb ON tabb.hth=taba.hth AND tabb.nf=taba.nf \n" +
//                "\n ";
//
//        RecordSet recordSet = new RecordSet();
//        recordSet.execute(sql);
//        String[] col = {"qf1", "qf2", "qf3", "qf4"};
//        BigDecimal[] qf = new BigDecimal[4];
//        RecordSetTrans recordSetTrans = new RecordSetTrans();
//        try {
//            recordSetTrans.execute("delete from uf_cwzl_bjbb");
//            while (recordSet.next()) {
//                for (int i = 0; i < 4; i++) {
//                    qf[i] = Utils.null20(Util.null2String(recordSet.getString(col[i])));
//                }
//                BigDecimal bj1 = Utils.null20(Util.null2String(recordSet.getString("bj1")));
//                BigDecimal bj2 = Utils.null20(Util.null2String(recordSet.getString("bj2")));
//                BigDecimal bj3 = Utils.null20(Util.null2String(recordSet.getString("bj3")));
//                BigDecimal bj4 = Utils.null20(Util.null2String(recordSet.getString("bj4")));
//                String hth = Util.null2String(recordSet.getString("hth"));
//                String nf = Util.null2String(recordSet.getString("nf"));
//                String qy = Util.null2String(recordSet.getString("qy"));
//                String qyxx = Util.null2String(recordSet.getString("qyxx"));
//                String ld = Util.null2String(recordSet.getString("ld"));
//                String ldxx = Util.null2String(recordSet.getString("ldxx"));
//                String ldbm = Util.null2String(recordSet.getString("ldbm"));
//                String[] cols = {"hth", "nf", "jdqjje1", "jdbjje1", "jdqjje2", "jdbjje2", "jdqjje3", "jdbjje3", "jdqjje4", "jdbjje4", "qy", "qyxx", "ld", "ldxx", "ldbm"};
//                String[] value = new String[cols.length];
//                value[0] = hth;
//                value[1] = nf;
//                for (int i = 0; i < 4; i++) {
//                    value[2 + i * 2] = qf[i].toString();
//                }
//                BigDecimal qfall = qf[0].add(qf[1]).add(qf[2]).add(qf[3]);
//                BigDecimal amount = bj1.add(bj2).add(bj3).add(bj4);
//                if ((qf[0].compareTo(new BigDecimal("0.00")) == 0 &&
//                        qf[1].compareTo(new BigDecimal("0.00")) == 0 &&
//                        qf[2].compareTo(new BigDecimal("0.00")) == 0 &&
//                        qf[3].compareTo(new BigDecimal("0.00")) == 0) || (qfall.compareTo(amount) == 0)
//                ) {
//                    boolean a = qfall.compareTo(amount) == 0;
//                    //不欠费跳过
//                } else {
////                    for (int i = 0; i < 4; i++) {
////                        if(amount.compareTo(qf[i])>=0){
////                            value[3+2*i] = qf[i].toString();
////                            amount = amount.subtract(qf[i]);
////                        }else {
////                            value[3+2*i] = amount.toString();
////                            amount = new BigDecimal("0.00");
////                        }
////                    }
//                    value[3] = bj1.toString();
//                    value[5] = bj2.toString();
//                    value[7] = bj3.toString();
//                    value[9] = bj4.toString();
//                    value[10] = qy;
//                    value[11] = qyxx;
//                    value[12] = ld;
//                    value[13] = ldxx;
//                    value[14] = ldbm;
//                    InsertModuleUtil4samtable.insert("uf_cwzl_bjbb", cols, value, 1, 102, recordSetTrans);
//                }
//            }
//        } catch (Exception e) {
//            recordSetTrans.rollback();
//            result.put("status", "1");
//            e.printStackTrace();
//        }
//        result.put("status", "1");
//        return result;
//    }
//
//}
