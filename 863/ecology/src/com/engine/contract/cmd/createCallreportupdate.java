package com.engine.contract.cmd;

import com.engine.contract.util.InsertModuleUtil4samtable;
import weaver.conn.RecordSetTrans;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import weaver.soa.workflow.request.*;

import java.util.HashMap;
import java.util.Map;


public class createCallreportupdate extends AbstractModeExpandJavaCodeNew {


    public createCallreportupdate() {

    }
    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {


        String[] col = {"hth","qy","dyrq","dndyjje","hzqy","qyxx","ld","ldxx","ldbm"};

        Map<String, String> result = new HashMap<>();
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        try {
            RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
            if(requestInfo!=null){
                MainTableInfo mainTableInfo = requestInfo.getMainTableInfo();
                DetailTableInfo detailTableInfo = requestInfo.getDetailTableInfo();
                Map<String,String> map = new HashMap<>();
                Property[] propertys = mainTableInfo.getProperty();
                for( Property property : propertys){
                    String str = property.getName();
                    String value = property.getValue();
                    map.put(str,value);
                }
                String[] value={map.get(col[0]),
                                map.get(col[1]),
                                map.get(col[2]),
                                map.get(col[3]),
                                map.get(col[4]),
                                map.get(col[5]),
                                map.get(col[6]),
                                map.get(col[7]),
                                map.get(col[8]),
                                };
                int mainid = InsertModuleUtil4samtable.insert("uf_cwzl_cjdgx",col,value,1,107,recordSetTrans);
                DetailTable[] detailtable = detailTableInfo.getDetailTable();
                if (detailtable.length > 0) {
                        for (int i = 0; i < detailtable.length; i++) {
                            DetailTable dt = detailtable[i];
                            String sql = "INSERT INTO uf_cwzl_cjdgx_dt1 (mainid,nf,jd,jfje,ksrq,jsrq) VALUES(";
                            String sql2 = "INSERT INTO uf_cwzl_cjdgx_dt2 (mainid,nf,jd,jfje,ksrq,jsrq) VALUES(";
                            // 指定明细表
                            Row[] row = dt.getRow();
                            // 当前明细表的所有数据,按行存储
                            for (Row r : row) {
                                // 指定行
                                Cell c[] = r.getCell();
                                // 每行数据再按列存储
                                HashMap<String, String> hashMap = new HashMap<>();
                                for (Cell c1 : c) {
                                // 指定列
                                    hashMap.put(c1.getName(), c1.getValue());
                                }
                                if(i==1){

                                    recordSetTrans.execute(sql+ mainid +","+ hashMap.get("nf")+","+hashMap.get("jd")+
                                            ","+hashMap.get("jfje")+",'"+hashMap.get("ksrq")+"','"+hashMap.get("jsrq")+"')");
                                }else if(i==2){
                                    recordSetTrans.execute(sql2+ mainid +","+hashMap.get("nf")+","+hashMap.get("jd")+
                                            ","+hashMap.get("jfje")+",'"+hashMap.get("ksrq")+"','"+hashMap.get("jsrq")+"')");
                                }
                            }
                        }
                }
            }
        } catch (Exception e) {
            result.put("errmsg","自定义出错信息");
            result.put("flag", "false");
        }
        return result;
    }
}
