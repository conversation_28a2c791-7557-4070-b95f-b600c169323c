package com.engine.contract.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.common.*;
import com.engine.contract.mapper.ContractMapper;
import com.engine.contract.util.BuildTreeUtil;
import com.engine.contract.util.ReportDataUtil;
import com.engine.core.interceptor.CommandContext;
import com.weaver.formmodel.util.DateHelper;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import weaver.conn.mybatis.MyBatisFactory;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.*;


//生成补缴报表
public class UpdateAllPaymentCmd extends AbstractCommonCommand<Map<String, Object>> {


    public UpdateAllPaymentCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    private BaseBean bb;

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        return doExecute();
    }


    public Map<String, Object> doExecute() {
        Map<String, Object> result = new HashMap<>();
        try {
            bb.writeLog(this.getClass().getName() + " --- START");
            SqlSessionFactory sqlSessionFactory = MyBatisFactory.sqlSessionFactory;
            bb.writeLog(sqlSessionFactory + " --- sqlSessionFactory");
            SqlSession sqlSession = sqlSessionFactory.openSession();
            bb.writeLog(sqlSession + " --- sqlSession");
            ContractMapper mapper = sqlSession.getMapper(ContractMapper.class);
            bb.writeLog(mapper + " --- mapper");
            //获取合同结果集
            List<ContractBean> contracts = mapper.selectAllContract();
            //获取已收金额结果集
            Map<String, AmountContractBean> receiveds = mapper.selectAmountReceived();
            bb.writeLog("receiveds--- " + receiveds.toString());
            //获取已补缴结果集
            Map<String, AmountContractBean> supplementary = mapper.selectsupplementary();
            bb.writeLog("supplementary--- " + supplementary.toString());
            //构建合同树
            List<ContractBean> ContractTree = CreateDatas(contracts);
            //遍历合同插入数据
            executeData(ContractTree, receiveds, supplementary);
            sqlSession.close();
            result.put("status", "1");
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("dec", e.getMessage());
            bb.writeLog("catch exception：" + e.getMessage());
        }
        return result;
    }


    /**
     * 构建合同树结构
     *
     * @param contracts
     */
    public List<ContractBean> CreateDatas(List<ContractBean> contracts) {
        return BuildTreeUtil.listToTree(contracts);
    }


    /**
     * 遍历合同插入数据
     */
    public void executeData(List<ContractBean> contracts, Map<String, AmountContractBean> receiveds, Map<String, AmountContractBean> supplementarys) throws Exception {
        //建模字段

        bb.writeLog("in ---- execute data");
        //以年度和合同为维度
        String[] cols = {"hth", "nf", "jdqjje1", "jdbjje1", "jdqjje2", "jdbjje2", "jdqjje3", "jdbjje3", "jdqjje4", "jdbjje4", "qy", "qyxx", "ld", "ldxx", "ldbm"};
        JSONArray jaList = new JSONArray();
        String insertFields = StringUtils.join(cols, ",");
        for (ContractBean contract :
                contracts) {
            bb.writeLog("contract" + contract.toString());
            HashMap<Integer, HashMap<Integer, AmountAndDay>> datas = new HashMap<>();
            List<String> historys = new ArrayList<>();
            //递归方法将金额获取到住数据 年-月-日 -金额
            String id = Util.null2String(contract.getId());
            String qy = Util.null2String(contract.getQy());
            String qyxx = Util.null2String(contract.getQyxx());
            String ld = Util.null2String(contract.getLd());
            String ldxx = Util.null2String(contract.getLdxx());
            String ldbm = Util.null2String(contract.getLdbm());
            Cal(contract, datas, historys);//处理datas
            String history = StringUtils.join(historys, ",");
            bb.writeLog("datas  " + datas);
            bb.writeLog("history  " + history);
            Set<Map.Entry<Integer, HashMap<Integer, AmountAndDay>>> entries = datas.entrySet();
            for (Map.Entry<Integer, HashMap<Integer, AmountAndDay>> entry :
                    entries) {

                Integer year = entry.getKey();
                HashMap<Integer, AmountAndDay> monthdatas = entry.getValue();

                //汇总四季度数据
                BigDecimal[] seadata = SumSea(monthdatas);
                bb.writeLog("seadata  " + Arrays.toString(seadata));


                //汇总所有实收数据
                BigDecimal[] ssdata = new BigDecimal[4];
                for (int i = 0; i < 4; i++) {
                    ssdata[i] = new BigDecimal("0.00");
                }
                for (String ht :
                        historys) {
                    String htid = ht + "_" + year;
                    //汇总所有合同四季度数据
                    BigDecimal[] recive = SumSeaRandS(receiveds, htid);
                    for (int i = 0; i < 4; i++) {
                        ssdata[i] = ssdata[i].add(recive[i]);
                    }
                }
                //补缴数据
                BigDecimal[] bjsj = new BigDecimal[4];
                for (int i = 0; i < 4; i++) {
                    bjsj[i] = new BigDecimal("0.00");
                }
                //加上补缴数据
                for (String ht :
                        historys) {
                    String htid = ht + "_" + year;
                    //汇总所有合同四季度数据
                    BigDecimal[] supplementary = SumSeaRandS(supplementarys, htid);
                    for (int i = 0; i < 4; i++) {
                        bjsj[i] = bjsj[i].add(supplementary[i]);
                        ssdata[i] = ssdata[i].add(supplementary[i]);
                    }

                }

                //插入月度数据
                boolean flag = true;
                JSONObject jo = new JSONObject();
                jo.put("hth", id);
                jo.put("qy", qy);
                jo.put("nf", year.toString());
                jo.put("qyxx", qyxx);
                jo.put("ld", ld);
                jo.put("ldxx", ldxx);
                jo.put("ldbm", ldbm);
                for (int i = 0; i < 4; i++) {
                    BigDecimal qjje = seadata[i].subtract(ssdata[i]);
                    jo.put("jdqjje" + (i + 1), seadata[i].subtract(ssdata[i]));
                    jo.put("jdbjje" + (i + 1), bjsj[i]);
                    if (qjje.compareTo(new BigDecimal("0.00")) > 0) {
                        flag = false;
                    }
                }
                if (!flag) {
                    jaList.add(jo);
                }
            }

        }
        ReportDataUtil.insertData(jaList, insertFields, "uf_cwzl_bjbb", 102, 1);
    }

    /**
     * 汇总四季度数据
     *
     * @return
     */
    public BigDecimal[] SumSea(HashMap<Integer, AmountAndDay> monthdatas) {
        BigDecimal[] result = new BigDecimal[4];
        for (int i = 0; i < 4; i++) {
            result[i] = new BigDecimal("0.00");
        }
        for (int i = 0; i < 4; i++) {
            for (int j = 1; j < 4; j++) {
                int month = (i * 3) + j;
                if (monthdatas.containsKey(month)) {
                    result[i] = result[i].add(monthdatas.get(month).getDecimal());
                }
            }
        }
        return result;
    }


    /**
     * 汇总四季度实收数据/补缴数据
     *
     * @return
     */
    public BigDecimal[] SumSeaRandS(Map<String, AmountContractBean> data, String htid) {
        BigDecimal[] result = new BigDecimal[4];
        for (int i = 0; i < 4; i++) {
            result[i] = new BigDecimal("0.00");
        }
        if (data.containsKey(htid)) {
            AmountContractBean seadata = data.get(htid);
            if (seadata.getSeone() != null) {
                result[0] = result[0].add(new BigDecimal(seadata.getSeone()));
            }
            if (seadata.getSetwo() != null) {
                result[1] = result[1].add(new BigDecimal(seadata.getSetwo()));
            }
            if (seadata.getSethree() != null) {
                result[2] = result[2].add(new BigDecimal(seadata.getSethree()));
            }
            if (seadata.getSefour() != null) {
                result[3] = result[3].add(new BigDecimal(seadata.getSefour()));
            }
        }
        return result;
    }

    /**
     * 递归计算应收金额
     *
     * @param contractBean
     * @param datas
     * @param historys
     * @return
     */
    public static Date Cal(ContractBean contractBean,
                           HashMap<Integer, HashMap<Integer, AmountAndDay>> datas,
                           List<String> historys) {

        //公共数据
        Date moveDate = DateHelper.parseDate(contractBean.getMoveDate());
        String id = contractBean.getId();
        String type = contractBean.getHtlx();
        String flag = contractBean.getFlag();
        historys.add(id);
        List<ContractTree> children = contractBean.getChildren();
        Date OverDate = null;
        if (children != null && children.size() != 0) {
            for (ContractTree child :
                    children) {
                if (children.size() <= 1 || !"3".equals(child.getHtlx())) {
                    OverDate = Cal((ContractBean) child, datas, historys);  //返回子合同合同截至时间
                }
            }
        }
        //退租合同直接返回中止时间
        if ("3".equals(type)) {
            return moveDate;
        }

        //判断是否递增
        if ("0".equals(flag)) {
            List<ContractDetail> detail = contractBean.getDetails();
            for (ContractDetail dt :
                    detail) {
                Date startdate = DateHelper.parseDate(dt.getStartDate());
                Date enddate = DateHelper.parseDate(dt.getEndDate());
                BigDecimal area = new BigDecimal(Util.null2o(dt.getArea()));
                BigDecimal rent = new BigDecimal(Util.null2o(dt.getRent()));
                if (OverDate == null) {
                    CalDatamap(datas, startdate, enddate, area, rent);
                } else {
                    if (startdate != null && enddate != null) {
                        if (OverDate.compareTo(startdate) >= 0 && OverDate.compareTo(enddate) <= 0) {
                            CalDatamap(datas, startdate, OverDate, area, rent);
                        } else if (OverDate.compareTo(enddate) >= 0) {
                            CalDatamap(datas, startdate, enddate, area, rent);
                        }
                    }
                }
            }
        } else {
            Date qzrq = DateHelper.parseDate(contractBean.getQzrq());
            Date jsrq = DateHelper.parseDate(contractBean.getJsrq());
            Date startdate = DateHelper.parseDate(contractBean.getStartDate());
            Date enddate = DateHelper.parseDate(contractBean.getEndDate());
            if (qzrq != null) {
                startdate = qzrq;
            }
            if (jsrq != null) {
                enddate = jsrq;
            }
            BigDecimal area = new BigDecimal(Util.null2o(contractBean.getArea()));
            BigDecimal rent = new BigDecimal(Util.null2o(contractBean.getRent()));
            if (startdate != null && enddate != null) {
                if (OverDate == null) {
                    CalDatamap(datas, startdate, enddate, area, rent);
                } else if (OverDate.compareTo(enddate) <= 0) {
                    CalDatamap(datas, startdate, OverDate, area, rent);
                } else {
                    CalDatamap(datas, startdate, enddate, area, rent);
                }
            }
        }
        return moveDate;
    }

    /**
     * 计算每月租金
     *
     * @param datas
     */
    public static void CalDatamap(HashMap<Integer, HashMap<Integer, AmountAndDay>> datas,
                                  Date startdate,
                                  Date enddate,
                                  BigDecimal area,
                                  BigDecimal rent
    ) {

        if (startdate != null && enddate != null && startdate.compareTo(enddate) < 0) {
            BigDecimal allrent = area.multiply(rent);
            Calendar start = Calendar.getInstance();
            start.setTime(startdate);
            long startTIme = start.getTimeInMillis();
            Calendar end = Calendar.getInstance();
            end.setTime(enddate);
            long endTime = end.getTimeInMillis();
            long oneDay = 1000 * 60 * 60 * 24L;
            long time = startTIme;
            while (time <= endTime) {
                Date now = new Date(time);
                Calendar days = Calendar.getInstance();
                days.setTime(now);
                int year = days.get(Calendar.YEAR);
                int month = days.get(Calendar.MONTH) + 1;
                if (datas.containsKey(year)) {
                    HashMap<Integer, AmountAndDay> yeardata = datas.get(year);
                    if (yeardata.containsKey(month)) {
                        AmountAndDay monthdata = yeardata.get(month);
                        monthdata.setDays(monthdata.getDays() + 1);
                        BigDecimal add = monthdata.getDecimal().add(allrent);
                        monthdata.setDecimal(add);
                    } else {
                        AmountAndDay monthdata = new AmountAndDay(1, allrent);
                        yeardata.put(month, monthdata);
                    }
                } else {
                    HashMap<Integer, AmountAndDay> yeardata = new HashMap<>();
                    AmountAndDay monthdata = new AmountAndDay(1, allrent);
                    yeardata.put(month, monthdata);
                    datas.put(year, yeardata);
                }
                time += oneDay;
            }
        }
    }

}
