//package com.engine.contract.cmd;
//
//import com.engine.common.biz.AbstractCommonCommand;
//import com.engine.common.entity.BizLogContext;
//import com.engine.contract.common.AmountAndDay;
//import com.engine.contract.util.InsertModuleUtil4samtable;
//import com.engine.contract.util.Utils;
//import com.engine.core.interceptor.CommandContext;
//import weaver.conn.RecordSet;
//import weaver.conn.RecordSetTrans;
//import weaver.general.BaseBean;
//import weaver.general.Util;
//import weaver.hrm.User;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.util.Date;
//import java.util.HashMap;
//import java.util.Map;
//
////更新年度季度月度
//@Deprecated
//public class UpdateReportCmd extends AbstractCommonCommand<Map<String, Object>> {
//
//
//    public UpdateReportCmd(Map<String, Object> params, User user) {
//        this.params = params;
//        this.user = user;
//
//    }
//
//    @Override
//    public BizLogContext getLogContext() {
//        return null;
//    }
//
//    //uf_cwzl_jcht 基础合同主表
//    //uf_cwzl_jdbb 季度报表
//    //uf_cwzl_jdsh 实缴单查询
//    //uf_cwzl_bjbb 补缴单
//    @Override
//    public Map<String, Object> execute(CommandContext commandContext) {
//        return doExecute();
//    }
//
//
//    public Map<String, Object> doExecute() {
//        Map<String, Object> result = new HashMap<>();
//        //建模字段
//        String[] insertcolmonth = {"hth", "qy", "nf", "yts1", "yysje1", "yts2", "yysje2", "yts3", "yysje3", "yts4", "yysje4", "yts5", "yysje5", "yts6", "yysje6",
//                "yts7", "yysje7", "yts8", "yysje8", "yts9", "yysje9", "yts10", "yysje10", "yts11", "yysje11", "yts12", "yysje12", "qyxx", "ld", "ldxx", "ldbm"};
//
//        String[] insertseason = {"hth", "qy", "nf", "jdts1", "jdysje1", "jdssje1", "jdts2", "jdysje2", "jdssje2", "jdts3", "jdysje3", "jdssje3", "jdts4", "jdysje4", "jdssje4", "qyxx", "ld", "ldxx", "ldbm", "jdjss1", "jdjss2", "jdjss3", "jdjss4"};
//
//        String[] insertyear = {"hth", "qy", "nf", "jdts", "jdysje", "jdssje", "qyxx", "ld", "ldxx", "ldbm", "ndqjhj", "ndbjhj"};
//
//        //执行sql
//        String sql = "SELECT  * " +
//                "FROM  " +
//                "    uf_cwzl_jcht " +
//                " where htzt = 0 ";
//        String delectsql = "DELETE FROM uf_cwzl_jdbb";
//        String delectsqlyear = "DELETE FROM uf_cwzl_ndbb";
//        String delectsqlmonth = "DELETE FROM uf_cwzl_yd";
//        RecordSetTrans recordSet = new RecordSetTrans();
//        RecordSetTrans recordSet1 = new RecordSetTrans();
//        RecordSetTrans recordSet2 = new RecordSetTrans();
//        recordSet.setAutoCommit(false);
//        recordSet1.setAutoCommit(false);
//        recordSet2.setAutoCommit(false);
//        RecordSet set = new RecordSet();
//        try {
//            recordSet.execute(delectsql);
//            recordSet1.execute(delectsqlyear);
//            recordSet2.execute(delectsqlmonth);
//            set.execute(sql);
//            while (set.next()) {
//                String oldflag = Util.null2String(set.getString("yhtsfdz"));//原合同是否是否递增
//                String flag = Util.null2String(set.getString("xhtsfdz")); //新合同是否递增
//                Date realstarttime = Utils.setDatefunction(Util.null2String(set.getString("ksrq")));//现合同开始
//                Date realendtime = Utils.setDatefunction(Util.null2String(set.getString("jsrq")));//现合同结束
//                Date oldstarttime = Utils.setDatefunction(Util.null2String(set.getString("yksrq")));//老合同开始
//                Date oldendtime = Utils.setDatefunction(Util.null2String(set.getString("yjsrq")));//老合同结束
//                String realArea = Util.null2String(set.getString("zlmj"));//本合同主表面积
//                String oldArea = Util.null2String(set.getString("yzlmj"));//老合同主表面积
//                String realAmount = Util.null2String(set.getString("je"));//本合同主表租金
//                String oldAmount = Util.null2String(set.getString("yje"));//老合同主表租金
//                String maintableid = Util.null2String(set.getString("id"));//主表id
//                String status = Util.null2String(set.getString("htlx"));//合同类型
//                String statusold = Util.null2String(set.getString("yhtlx"));//老合同类型
//                Date endcontract = Utils.setDatefunction(Util.null2String(set.getString("zzsxrq")));//终止合同日期
//                String qy = Util.null2String(set.getString("qy"));//企业
//                String yhthtbh = Util.null2String(set.getString("yhthtbh"));//原合同编号
//                String qyxx = Util.null2String(set.getString("qyxx"));
//                String ld = Util.null2String(set.getString("ld"));
//                String ldxx = Util.null2String(set.getString("ldxx"));
//                String ldbm = Util.null2String(set.getString("ldbm"));
//                writeLog(maintableid);
//                HashMap<Integer, HashMap<Integer, AmountAndDay>> realmap = new HashMap<>(), oldmap = new HashMap<>();
//                boolean isstartmonth = false;
//                boolean oldistart = false;
//                int isneed = 1;   //0 老 1 新老 2 新
//                switch (status) {
//                    case "0":    //新签
//                        oldstarttime = Utils.get36912(realstarttime);
//                        isstartmonth = true;
//                        isneed = 2;
//                        break;
//                    case "1":   //续签
//                        break;
//                    case "2":   //变更
//                        oldendtime = endcontract;
//                        break;
//                    case "3":    //退租
//                        isneed = 0;
//                        break;
//                    default:
//                        realendtime = endcontract;
//                        isneed = 0;
//                        break;
//                }
//
//                writeLog("isneed" + isneed);
//                if (isneed == 1) {
//                    if (!"".equals(yhthtbh) && oldstarttime != null && oldendtime != null && !"".equals(oldAmount) && !"".equals(oldArea) && !"".equals(oldflag)) {
//                        RecordSet select = new RecordSet();
//                        String selsql = "SELECT  " +
//                                "    *   " +
//                                "FROM  " +
//                                "    uf_cwzl_jcht   " +
//                                "WHERE  " +
//                                "    htbh IN ( SELECT yhthtbh FROM uf_cwzl_jcht WHERE id = '" + maintableid + "' )";
//                        new BaseBean().writeLog("select sql is " + selsql);
//                        select.execute(selsql);
//                        String mainid4zero;
//                        String status4zero;
//                        Date realstarttimefin;
//                        Date endtime;
//                        String flag4zero;
//                        String realArea4zero;
//                        String realAmount4zero;
//                        if (select.next()) {
//                            mainid4zero = Util.null2String(select.getString("id"));
//                            status4zero = Util.null2String(select.getString("htlx"));
//                            realstarttimefin = Utils.setDatefunction(Util.null2String(select.getString("ksrq")));//合同开始
//                            endtime = Utils.setDatefunction(Util.null2String(select.getString("jsrq")));//合同结束
//                            flag4zero = Util.null2String(select.getString("xhtsfdz")); //是否递增
//                            realArea4zero = Util.null2String(select.getString("zlmj"));//主表面积
//                            realAmount4zero = Util.null2String(select.getString("je"));//主表租金
//                            writeLog(realstarttimefin);
//                            writeLog(endtime);
//                            if (endtime != null && endcontract != null && "2".equals(status)) {
//                                if (endcontract.compareTo(endtime) < 0) {
//                                    endtime = endcontract;
//                                }
//                            }
//                            if ("0".equals(status4zero)) {
//                                oldistart = true;
//                            }
//                            if ("0".equals(flag4zero)) {
//                                oldmap = getDetailnumByDate(oldistart, mainid4zero, realstarttimefin, endtime, "uf_cwzl_jcht_dt1");
//                            } else {
//                                oldmap = getMonthmap(oldistart, realstarttimefin, endtime, realAmount4zero, realArea4zero);           //非递增
//                            }
//                        }
//                    }
//                    if ("0".equals(flag)) {
//
//                        realmap = getDetailnumByDate(isstartmonth, maintableid, realstarttime, realendtime, "uf_cwzl_jcht_dt1");
//                    } else {
//                        realmap = getMonthmap(isstartmonth, realstarttime, realendtime, realAmount, realArea);           //非递增
//                    }
//                }
//                if (isneed == 2) {
//                    if (realstarttime != null && realendtime != null && !"".equals(realAmount) && !"".equals(realArea)) {
//                        if ("0".equals(flag)) {
//                            realmap = getDetailnumByDate(isstartmonth, maintableid, realstarttime, realendtime, "uf_cwzl_jcht_dt1");
//                        } else {
//                            realmap = getMonthmap(isstartmonth, realstarttime, realendtime, realAmount, realArea);           //非递增
//                        }
//                    } else {
//                        continue;
//                    }
//                }
//
//                if (isneed == 0) {
//                    new BaseBean().writeLog("终止合同：" + realendtime + "-" + yhthtbh);
//                    if (realendtime == null) {
//                        continue;
//                    }
//                    if ("".equals(yhthtbh)) {
//                        continue;
//                    }
//                    RecordSet search = new RecordSet();
//                    String selsql = "SELECT  " +
//                            "    *   " +
//                            "FROM  " +
//                            "    uf_cwzl_jcht   " +
//                            "WHERE  " +
//                            "    htbh IN ( SELECT yhthtbh FROM uf_cwzl_jcht WHERE id = '" + maintableid + "' )";
//                    new BaseBean().writeLog("select sql is " + selsql);
//                    search.execute(selsql);
//                    String mainid4zero;
//                    String status4zero;
//                    Date realstarttimefin;
//                    String flag4zero;
//                    String realArea4zero;
//                    String realAmount4zero;
//                    if (search.next()) {
//                        mainid4zero = Util.null2String(search.getString("id"));
//                        status4zero = Util.null2String(search.getString("htlx"));
//                        realstarttimefin = Utils.setDatefunction(Util.null2String(search.getString("ksrq")));//现合同开始
//                        flag4zero = Util.null2String(search.getString("xhtsfdz")); //新合同是否递增
//                        realArea4zero = Util.null2String(search.getString("zlmj"));//本合同主表面积
//                        realAmount4zero = Util.null2String(search.getString("je"));//本合同主表租金
//                    } else {
//                        continue;
//                    }
//                    if ("0".equals(status4zero)) {
//                        oldistart = true;
//                    }
//                    if ("0".equals(flag4zero)) {
//                        oldmap = getDetailnumByDate(oldistart, mainid4zero, realstarttimefin, realendtime, "uf_cwzl_jcht_dt1");
//                    } else {
//                        oldmap = getMonthmap(oldistart, realstarttimefin, realendtime, realAmount4zero, realArea4zero);           //非递增
//                    }
//                }
//
//                if (oldstarttime == null) {
//                    oldstarttime = realstarttime;
//                }
//                int startyear = Utils.getyear(oldstarttime);
//                int endyear = Utils.getyear(realendtime);
//                writeLog("oldmap--" + oldmap.toString());
//                writeLog("realmap--" + realmap.toString());
//
//                HashMap<String, HashMap<Integer, BigDecimal[]>> callreport = getcallreportmap();
//                //插入建模表
//                for (int i = startyear; i <= endyear; i++) {
//
//                    BigDecimal[] amount = new BigDecimal[4]; //四个季度
//                    int dayyear = 0;                        //年度天数
//                    int[] seasond = new int[4];            //四个季度天数
//                    BigDecimal amountyear = new BigDecimal("0.00");
//                    BigDecimal[] amountmonth = new BigDecimal[12]; //十二个月度
//                    int[] monthday = new int[12];  //月度天数
//                    BigDecimal[] amountreal = searchans(maintableid, i); //查找对应年实收
//                    BigDecimal[] amountrealj = searchansnew(maintableid, i);
//                    BigDecimal amountrealyear = Utils.null20(amountreal[0]).add(Utils.null20(amountreal[1])).add(Utils.null20(amountreal[2])).add(Utils.null20(amountreal[3]));
//                    for (int j = 0; j < 4; j++) {
//                        amount[j] = new BigDecimal("0.00");
//                        int seasondays = 0; //季度天数
//                        for (int k = 0; k < 3; k++) {
//                            int month = j * 3 + k + 1;
//                            BigDecimal monthans = Utils.null2no1(Utils.null2Object(oldmap.get(i)).get(month)).getDecimal().add(Utils.null2no1(Utils.null2Object(realmap.get(i)).get(month)).getDecimal());
//                            monthday[j * 3 + k] = Utils.null2no1(Utils.null2Object(oldmap.get(i)).get(month)).getDays() + Utils.null2no1(Utils.null2Object(realmap.get(i)).get(month)).getDays();
//                            seasondays += monthday[j * 3 + k];
//                            amountmonth[month - 1] = monthans.setScale(2, RoundingMode.HALF_UP);
//                            amount[j] = amount[j].add(monthans).setScale(2, RoundingMode.HALF_UP);
//                        }
//                        seasond[j] = seasondays;
//                        dayyear += seasond[j];
//                        amountyear = amountyear.add(amount[j]);
//                    }
//                    BigDecimal[] afteramount = Utils.null2Bigdecimal4number(Utils.null2Object4Bigdecimal(callreport.get(maintableid)).get(i));
//                    BigDecimal amount4year = new BigDecimal("0.00");
//                    for (int j = 0; j < 4; j++) {
//                        amountreal[j] = Utils.null20(amountreal[j]).add(afteramount[j]);
//                        amount4year = amount4year.add(Utils.null20(afteramount[j]));
//                    }
//                    InsertModuleUtil4samtable.insert("uf_cwzl_jdbb", insertseason, new String[]{
//                            maintableid,
//                            qy,
//                            String.valueOf(i),
//                            String.valueOf(seasond[0]),
//                            amount[0].toString(),
//                            Utils.null20(amountreal[0]).toString(),
//                            String.valueOf(seasond[1]),
//                            amount[1].toString(),
//                            Utils.null20(amountreal[1]).toString(),
//                            String.valueOf(seasond[2]),
//                            amount[2].toString(),
//                            Utils.null20(amountreal[2]).toString(),
//                            String.valueOf(seasond[3]),
//                            amount[3].toString(),
//                            Utils.null20(amountreal[3]).toString(),
//                            qyxx,
//                            ld,
//                            ldxx,
//                            ldbm,
//                            Utils.null20(amountrealj[0]).toString(),
//                            Utils.null20(amountrealj[1]).toString(),
//                            Utils.null20(amountrealj[2]).toString(),
//                            Utils.null20(amountrealj[3]).toString(),
//                    }, 1, 100, recordSet);//插入季度年度
//
//                    InsertModuleUtil4samtable.insert("uf_cwzl_yd", insertcolmonth, new String[]{
//                            maintableid,
//                            qy,
//                            String.valueOf(i),
//                            String.valueOf(monthday[0]),
//                            amountmonth[0].toString(),
//                            String.valueOf(monthday[1]),
//                            amountmonth[1].toString(),
//                            String.valueOf(monthday[2]),
//                            amountmonth[2].toString(),
//                            String.valueOf(monthday[3]),
//                            amountmonth[3].toString(),
//                            String.valueOf(monthday[4]),
//                            amountmonth[4].toString(),
//                            String.valueOf(monthday[5]),
//                            amountmonth[5].toString(),
//                            String.valueOf(monthday[6]),
//                            amountmonth[6].toString(),
//                            String.valueOf(monthday[7]),
//                            amountmonth[7].toString(),
//                            String.valueOf(monthday[8]),
//                            amountmonth[8].toString(),
//                            String.valueOf(monthday[9]),
//                            amountmonth[9].toString(),
//                            String.valueOf(monthday[10]),
//                            amountmonth[10].toString(),
//                            String.valueOf(monthday[11]),
//                            amountmonth[11].toString(),
//                            qyxx,
//                            ld,
//                            ldxx,
//                            ldbm
//                    }, 1, 99, recordSet2);//插入月度年度
//
//
//                    InsertModuleUtil4samtable.insert("uf_cwzl_ndbb", insertyear, new String[]{
//                            maintableid,
//                            qy,
//                            String.valueOf(i),
//                            String.valueOf(dayyear),
//                            amountyear.toString(),
//                            amountrealyear.add(amount4year).setScale(2, BigDecimal.ROUND_HALF_UP).toString(),
//                            qyxx,
//                            ld,
//                            ldxx,
//                            ldbm,
//                            amountyear.subtract(amountrealyear).toString(),
//                            amount4year.toString()
//                    }, 1, 101, recordSet1);//插入年度年度
//                }
//            }
//            recordSet.commit();
//            recordSet1.commit();
//            recordSet2.commit();
//            result.put("status", "1");
//        } catch (Exception e) {
//            result.put("status", "-1");
//            writeLog(e.getMessage());
//            result.put("dec", e.getMessage());
//            recordSet.rollback();
//            recordSet1.rollback();
//            recordSet2.rollback();
//            e.printStackTrace();
//            return result;
//        }
//        return result;
//    }
//
//
//    //递增节点
//    public HashMap<Integer, HashMap<Integer, AmountAndDay>> getDetailnumByDate(boolean isstartmonth, String id, Date start, Date end, String table) {
//        HashMap<Integer, HashMap<Integer, AmountAndDay>> map = new HashMap<>();
//        RecordSet recordSet = new RecordSet();
//        String cols = "ksrq,jsrq,zlmj,je";
//        String sql = "SELECT " + cols + " FROM " + table + " where mainid = '" + id + "'";
//        recordSet.execute(sql);
////        Date indexstart = start;
////        if(isstartmonth){
////            indexstart = Utils.get36912(start);
////        }
//        while (recordSet.next()) {
//            Date startdate = Utils.setDatefunction(Util.null2String(recordSet.getString("ksrq")));//开始时间
//            Date enddate = Utils.setDatefunction(Util.null2String(recordSet.getString("jsrq")));//结束时间
//            String area = Util.null2String(recordSet.getString("zlmj"));//面积
//            String amount = Util.null2String(recordSet.getString("je"));//租金
//            if (startdate == null || enddate == null) {
//                new BaseBean().writeLog("递增合同数据不完整" + startdate + "-" + enddate);
//                continue;
//            }
////            if(startdate.compareTo(start)<0){
////                startdate =start;
////            }
////            if (startdate.compareTo(end) >= 0) {
////                continue;
////            }
//            if (enddate.compareTo(end) >= 0 && startdate.compareTo(end) <= 0) {
//                enddate = end;
//            }
//            int between = Utils.getMonth(enddate) - Utils.getMonth(startdate) +
//                    12 * (Utils.getyear(enddate) - Utils.getyear(startdate)) + 1;
//            BigDecimal areais = new BigDecimal(area);
//            BigDecimal amountis = new BigDecimal(amount);
//            BigDecimal ans;
//            Date monthindex = startdate;
//            for (int i = 0; i < between; i++) {
//                int year = Utils.getyear(monthindex);
//                int month = Utils.getMonth(monthindex);
//                BigDecimal dayis;
//                int days;
//                if (i == 0) {
//                    days = Utils.getnofullmonthDays(monthindex) + 1;
//                } else if (i == between - 1) {
//                    days = Utils.getDays(enddate);
//                } else {
//                    days = Utils.getDaysOfMonth(monthindex);
//                }
//                dayis = new BigDecimal(days);
//                ans = amountis.multiply(areais).multiply(dayis);
//                HashMap<Integer, AmountAndDay> monthmap;
//                if (map.containsKey(year)) {
//                    monthmap = map.get(year);
//                    if (monthmap.containsKey(month)) {
//                        AmountAndDay amountAndDay = monthmap.get(month);
//                        amountAndDay.setDecimal(monthmap.get(month).getDecimal().add(ans));
//                        amountAndDay.setDays(days);
//                        monthmap.put(month, monthmap.get(month));
//                    } else {
//                        monthmap.put(month, new AmountAndDay(days, ans));
//                    }
//                } else {
//                    monthmap = new HashMap<>();
//                    monthmap.put(month, new AmountAndDay(days, ans));
//                    map.put(year, monthmap);
//                }
//                monthindex = Utils.addmonnth(monthindex, 1);
//            }
//        }
//
//        return map;
//    }
//
//
//    // 非递增
//    public HashMap<Integer, HashMap<Integer, AmountAndDay>> getMonthmap(boolean istartmonth, Date starttime, Date endtime, String amount, String area) {
//        HashMap<Integer, HashMap<Integer, AmountAndDay>> map = new HashMap<>();
//        Date startmonth = starttime;
//        if (istartmonth) {
//            startmonth = Utils.get36912(starttime);
//        }
//        int between = Utils.getMonth(endtime) - Utils.getMonth(startmonth) +
//                12 * (Utils.getyear(endtime) - Utils.getyear(startmonth)) + 1;
//        Date monthindex = startmonth;
//        for (int i = 0; i < between; i++) {
//            HashMap<Integer, AmountAndDay> amountmonth;
//            int year = Utils.getyear(monthindex);
//            int month = Utils.getMonth(monthindex);
//            int days;
//            if (i == 0) {
//                days = Utils.getnofullmonthDays(monthindex) + 1;
//            } else if (i == between - 1) {
//                days = Utils.getDays(endtime);
//            } else {
//                days = Utils.getDaysOfMonth(monthindex);
//            }
//            BigDecimal dayis = new BigDecimal(days);
//            BigDecimal areais = new BigDecimal(area);
//            BigDecimal amountis = new BigDecimal(amount);
//            BigDecimal ans = amountis.multiply(areais).multiply(dayis);
//            if (map.containsKey(year)) {
//                amountmonth = map.get(year);
//                amountmonth.put(month, new AmountAndDay(days, ans));
//            } else {
//                amountmonth = new HashMap<>();
//                amountmonth.put(month, new AmountAndDay(days, ans));
//                map.put(year, amountmonth);
//            }
//            monthindex = Utils.addmonnth(monthindex, 1);
//        }
//        return map;
//    }
//
//    public BigDecimal[] searchans(String hth, int nf) {
//        RecordSet recordSet = new RecordSet();
//
//        BigDecimal[] strings = new BigDecimal[4];
//        String oldid = "";
//        String searchhth = "select \n" +
//                "tabb.id as ans \n" +
//                "from uf_cwzl_jcht taba\n" +
//                "left join uf_cwzl_jcht tabb \n" +
//                "on taba.yhthtbh = tabb.htbh where taba.id = '" + hth + "'";
//        boolean flag = recordSet.execute(searchhth);
//        if (!flag) {
//            new BaseBean().writeLog("sql+++" + searchhth);
//            new BaseBean().writeLog("sql+++" + recordSet.getExceptionMsg());
//        }
//        if (recordSet.next()) {
//            oldid = Util.null2String(recordSet.getString("ans"));
//        }
//        if (!"".equals(oldid)) {
//            hth += "," + oldid;
//        }
//        String sql = "SELECT " +
//                "nf, " +
//                "SUM(col1) col1, " +
//                "SUM(col2) col2, " +
//                "SUM(col3) col3, " +
//                "SUM(col4) col4 " +
//                "FROM ( " +
//                "    SELECT " +
//                "         hth, " +
//                "         nf as nf, " +
//                "         \"0\" as col1, " +
//                "         \"1\" as col2, " +
//                "         \"2\" as col3, " +
//                "         \"3\" as col4 " +
//                "    FROM " +
//                "        ( SELECT ssje, jd, hth,nf    FROM uf_cwzl_jdsh ) test PIVOT ( SUM ( ssje ) FOR jd IN ( \"0\" , \"1\", \"2\", \"3\" ) ) pvt  " +
//                ") taba WHERE nf = " + nf + " and hth IN (" + hth + ") GROUP BY nf ";
//        new BaseBean().writeLog("hth", hth);
//        new BaseBean().writeLog("oldid", oldid);
//        recordSet.execute(sql);
//        if (recordSet.next()) {
//            strings[0] = Utils.null20(Util.null2String(recordSet.getString("col1")));
//            strings[1] = Utils.null20(Util.null2String(recordSet.getString("col2")));
//            strings[2] = Utils.null20(Util.null2String(recordSet.getString("col3")));
//            strings[3] = Utils.null20(Util.null2String(recordSet.getString("col4")));
//        }
//        return strings;
//    }
//
//    public BigDecimal[] searchansnew(String hth, int nf) {
//        RecordSet recordSet = new RecordSet();
//        BigDecimal[] strings = new BigDecimal[4];
//        String oldid = "";
//        String searchhth = "select \n" +
//                "tabb.id as ans \n" +
//                "from uf_cwzl_jcht taba\n" +
//                "left join uf_cwzl_jcht tabb \n" +
//                "on taba.yhthtbh = tabb.htbh where taba.id =  '" + hth + "'";
//        boolean flag = recordSet.execute(searchhth);
//        if (!flag) {
//            new BaseBean().writeLog("sql+++" + searchhth);
//            new BaseBean().writeLog("sql+++" + recordSet.getExceptionMsg());
//        }
//        if (recordSet.next()) {
//            oldid = Util.null2String(recordSet.getString("ans"));
//        }
//
//        new BaseBean().writeLog("oldid", oldid);
//        if (!"".equals(oldid)) {
//            hth += "," + oldid;
//        }
//        new BaseBean().writeLog("hth", hth);
//        new BaseBean().writeLog("oldid", oldid);
//        String sql = "SELECT " +
//                "nf, " +
//                "SUM(col1) col1, " +
//                "SUM(col2) col2, " +
//                "SUM(col3) col3, " +
//                "SUM(col4) col4 " +
//                "FROM ( " +
//                "    SELECT " +
//                "         hth, " +
//                "         nf as nf, " +
//                "         \"0\" as col1, " +
//                "         \"1\" as col2, " +
//                "         \"2\" as col3, " +
//                "         \"3\" as col4 " +
//                "    FROM " +
//                "        ( SELECT ssje, jd, hth,nf    FROM uf_cwzl_jdsh ) test PIVOT ( SUM ( ssje ) FOR jd IN ( \"0\" , \"1\", \"2\", \"3\" ) ) pvt  " +
//                ") taba WHERE nf = " + nf + " and hth IN (" + hth + ") GROUP BY nf ";
//        new BaseBean().writeLog("sql historu------" + sql);
//        recordSet.execute(sql);
//        if (recordSet.next()) {
//            strings[0] = Utils.null20(Util.null2String(recordSet.getString("col1")));
//            strings[1] = Utils.null20(Util.null2String(recordSet.getString("col2")));
//            strings[2] = Utils.null20(Util.null2String(recordSet.getString("col3")));
//            strings[3] = Utils.null20(Util.null2String(recordSet.getString("col4")));
//        }
//        return strings;
//    }
//
//    public HashMap<String, HashMap<Integer, BigDecimal[]>> getcallreportmap() {
//        RecordSet recordSet = new RecordSet();
//        HashMap<String, HashMap<Integer, BigDecimal[]>> map = new HashMap<>();
//        String sql = "  SELECT\n" +
//                "  taba.hth hth,\n" +
//                "  taba.nf nf,\n" +
//                "  (isnull(bj1, 0) + isnull(oldbj1, 0)) AS bj1,\n" +
//                "  (isnull(bj2, 0) + isnull(oldbj2, 0)) AS bj2,\n" +
//                "  (isnull(bj3, 0) + isnull(oldbj3, 0)) AS bj3,\n" +
//                "  (isnull(bj4, 0) + isnull(oldbj4, 0)) AS bj4\n" +
//                "FROM\n" +
//                "  (\n" +
//                "    SELECT\n" +
//                "      hth,\n" +
//                "      SUM (yjdbj) bj1,\n" +
//                "      SUM (ejdbj) bj2,\n" +
//                "      SUM (sjdbj) bj3,\n" +
//                "      SUM (sijdbj) bj4,\n" +
//                "      nf\n" +
//                "    FROM\n" +
//                "      uf_cwzl_jdbj\n" +
//                "    GROUP BY\n" +
//                "      hth,\n" +
//                "      nf\n" +
//                "  ) AS taba\n" +
//                "left  JOIN (\n" +
//                "    SELECT\n" +
//                "      taba.id AS id,\n" +
//                "      tabb.id AS old,\n" +
//                "      tabc.bj1 AS oldbj1,\n" +
//                "      tabc.bj2 AS oldbj2,\n" +
//                "      tabc.bj3 AS oldbj3,\n" +
//                "      tabc.bj4 AS oldbj4,\n" +
//                "      tabc.nf AS oldnf\n" +
//                "    FROM\n" +
//                "      uf_cwzl_jcht taba\n" +
//                "      LEFT JOIN uf_cwzl_jcht tabb ON taba.yhthtbh = tabb.htbh\n" +
//                "      LEFT JOIN (\n" +
//                "        SELECT\n" +
//                "          hth,\n" +
//                "          SUM (yjdbj) bj1,\n" +
//                "          SUM (ejdbj) bj2,\n" +
//                "          SUM (sjdbj) bj3,\n" +
//                "          SUM (sijdbj) bj4,\n" +
//                "          nf\n" +
//                "        FROM\n" +
//                "          uf_cwzl_jdbj\n" +
//                "        GROUP BY\n" +
//                "          hth,\n" +
//                "          nf\n" +
//                "      ) AS tabc ON tabb.id = tabc.hth\n" +
//                "  ) tabb ON taba.hth = tabb.id\n" +
//                "  AND tabb.oldnf = taba.nf \n ";
//        recordSet.execute(sql);
//        while (recordSet.next()) {
//            String hth = Util.null2String(recordSet.getString("hth"));
//            String nf = Util.null2String(recordSet.getString("nf"));
//            BigDecimal[] bigDecimals = new BigDecimal[4];
//            bigDecimals[0] = Utils.null20(Util.null2String(recordSet.getString("bj1")));
//            bigDecimals[1] = Utils.null20(Util.null2String(recordSet.getString("bj2")));
//            bigDecimals[2] = Utils.null20(Util.null2String(recordSet.getString("bj3")));
//            bigDecimals[3] = Utils.null20(Util.null2String(recordSet.getString("bj4")));
//            HashMap<Integer, BigDecimal[]> contractmap = new HashMap<>();
//            contractmap.put(Integer.parseInt(nf), bigDecimals);
//            map.put(hth, contractmap);
//        }
//        return map;
//    }
//}
