package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.util.InsertModuleUtil4samtable;
import com.engine.contract.util.Utils;
import com.engine.core.interceptor.CommandContext;
import com.wbi.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.BaseBean;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;


//催缴单插入
public class ShowCallReportCmd extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public ShowCallReportCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        return doExecute();
    }


    public Map<String, Object> doExecute() {
        Map<String, Object> result = new HashMap<>();
        String ans = (String) params.get("checkid");
        if ("".equals(ans)) {
            result.put("ans", 0);
            return result;
        }
        String sql = "SELECT nf,hth,qy,qyxx,ld,ldxx,ldbm,\n" +
                "(isnull (jdqjje1,0)-isnull (jdbjje1,0)) as qf1,\n" +
                "(isnull (jdqjje2,0)-isnull (jdbjje2,0)) as qf2,\n" +
                "(isnull (jdqjje3,0)-isnull (jdbjje3,0)) as qf3,\n" +
                "(isnull (jdqjje4,0)-isnull (jdbjje4,0)) as qf4\n" +
                "FROM  " +
                "uf_cwzl_bjbb  WHERE id IN (" + ans + ")";
        String[] col = {"hth", "qy", "dyrq", "hzqy", "dndyjje", "qyxx", "ld", "ldxx", "ldbm"};
        String[] coldetail = {"mainid", "nf", "jd", "jfje", "ksrq", "jsrq"};
        new BaseBean().writeLog("ModeExpandTemplatetest");
        try {
            RecordSetTrans recordSetTrans = new RecordSetTrans();
            RecordSet recordSet = new RecordSet();
            recordSet.execute(sql);
            HashMap<String, Integer> map = new HashMap<>();
            String[] qf = new String[4];
            while (recordSet.next()) {
                String hth = Util.null2String(recordSet.getString("hth"));
                String year = Util.null2String(recordSet.getString("nf"));
                String company = Util.null2String(recordSet.getString("qy"));
                String qyxx = Util.null2String(recordSet.getString("qyxx"));
                String ld = Util.null2String(recordSet.getString("ld"));
                String ldxx = Util.null2String(recordSet.getString("ldxx"));
                String ldbm = Util.null2String(recordSet.getString("ldbm"));
                for (int i = 0; i < 4; i++) {
                    qf[i] = Util.null2String(recordSet.getString("qf" + (i + 1)));
                }
                int maindid;
                if (map.containsKey(hth)) {
                    maindid = map.get(hth);
                } else {
                    InsertModuleUtil4samtable.insert(
                            "uf_cwzl_cjd",
                            col,
                            new String[]{
                                    hth,
                                    company,
                                    DateHelper.getDate(new Date()),
                                    company,
                                    new BigDecimal(qf[0]).add(new BigDecimal(qf[1])).add(new BigDecimal(qf[2])).add(new BigDecimal(qf[3])).toString(),
                                    qyxx,
                                    ld,
                                    ldxx,
                                    ldbm
                            },
                            1,
                            108,
                            recordSetTrans
                    );
                    maindid = InsertModuleUtil4samtable.insert(
                            "uf_cwzl_cjdgx",
                            col,
                            new String[]{
                                    hth,
                                    company,
                                    DateHelper.getDate(new Date()),
                                    company,
                                    new BigDecimal(qf[0]).add(new BigDecimal(qf[1])).add(new BigDecimal(qf[2])).add(new BigDecimal(qf[3])).toString(),
                                    qyxx,
                                    ld,
                                    ldxx,
                                    ldbm
                            },
                            1,
                            107,
                            recordSetTrans
                    );
                    map.put(hth, maindid);
                }
                //1 4 7 10
                //

                for (int i = 0; i < getnowshowDattime(year); i++) {
                    if (new BigDecimal("0.00").compareTo(new BigDecimal(qf[i])) == 0) {
                        continue;
                    }
                    Date start = Utils.setDatefunction(Integer.parseInt(year), i * 3 + 1, 1);
                    Date end = Utils.setDatefunction(Integer.parseInt(year), i * 3 + 2, Utils.getDaysOfMonth(Utils.setDatefunction(Integer.parseInt(year), i * 3 + 2, 1)));
                    recordSetTrans.execute("INSERT INTO uf_cwzl_cjd_dt1 (" +
                            coldetail[0] + "," +
                            coldetail[1] + "," +
                            coldetail[2] + "," +
                            coldetail[3] + "," +
                            coldetail[4] + "," +
                            coldetail[5] + ") VALUES (" +
                            maindid + "," +
                            year + "," +
                            i + "," +
                            qf[i] + ",'" +
                            DateHelper.getDate(start) + "','" +
                            DateHelper.getDate(end) + "')"

                    );
                    recordSetTrans.execute("INSERT INTO uf_cwzl_cjd_dt2 (" +
                            coldetail[0] + "," +
                            coldetail[1] + "," +
                            coldetail[2] + "," +
                            coldetail[3] + "," +
                            coldetail[4] + "," +
                            coldetail[5] + ") VALUES (" +
                            maindid + "," +
                            year + "," +
                            i + "," +
                            qf[i] + ",'" +
                            DateHelper.getDate(start) + "','" +
                            DateHelper.getDate(end) + "')"

                    );

                    recordSetTrans.execute("INSERT INTO uf_cwzl_cjdgx_dt1 (" +
                            coldetail[0] + "," +
                            coldetail[1] + "," +
                            coldetail[2] + "," +
                            coldetail[3] + "," +
                            coldetail[4] + "," +
                            coldetail[5] + ") VALUES (" +
                            maindid + "," +
                            year + "," +
                            i + "," +
                            qf[i] + ",'" +
                            DateHelper.getDate(start) + "','" +
                            DateHelper.getDate(end) + "')"

                    );
                    recordSetTrans.execute("INSERT INTO uf_cwzl_cjdgx_dt2 (" +
                            coldetail[0] + "," +
                            coldetail[1] + "," +
                            coldetail[2] + "," +
                            coldetail[3] + "," +
                            coldetail[4] + "," +
                            coldetail[5] + ") VALUES (" +
                            maindid + "," +
                            year + "," +
                            i + "," +
                            qf[i] + ",'" +
                            DateHelper.getDate(start) + "','" +
                            DateHelper.getDate(end) + "')"

                    );
                }
            }
            StringBuilder data = new StringBuilder();
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                data.append(entry.getValue()).append(",");
            }
            result.put("ans", "1");
            if (data.length() > 1) {
                result.put("data", data.substring(0, data.length() - 1));
            }
        } catch (Exception e) {
            result.put("ans", "-1");
        }
        return result;
    }

    public static int getnowshowDattime(String year) {
        Date date = new Date();
        int month = Utils.getMonth(date);
        if (Integer.parseInt(year) > Utils.getyear(date)) {
            return 0;
        }
        if (Integer.parseInt(year) < Utils.getyear(date)) {
            return 4;
        }
        if (month < 4) {
            return 1;
        }
        if (month < 7) {
            return 2;
        }
        if (month < 10) {
            return 3;
        }
        return 4;
    }
}
