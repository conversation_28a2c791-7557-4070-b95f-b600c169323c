package com.engine.contract.cmd;

import com.engine.contract.util.InsertModuleUtil4samtable;
import weaver.conn.RecordSetTrans;
import weaver.formmode.customjavacode.AbstractModeExpandJavaCodeNew;
import java.util.HashMap;
import java.util.Map;
import weaver.soa.workflow.request.DetailTableInfo;
import weaver.soa.workflow.request.MainTableInfo;
import weaver.soa.workflow.request.Property;
import weaver.soa.workflow.request.RequestInfo;

//缴费单更新
public class createPayreportupdate extends AbstractModeExpandJavaCodeNew {


    public createPayreportupdate() {

    }
    @Override
    public Map<String, String> doModeExpand(Map<String, Object> param) {


        String[] col = {"hth","nf","jd","qy","jfjzrq","qsrq","jzrq","xjnje","dyrq","hzqsrq","hzjzrq","hznf","hzjd","hzqy","gsqy","ld","ldxx","ldbm"};
        Map<String, String> result = new HashMap<>();
        RecordSetTrans recordSetTrans = new RecordSetTrans();
        try {
            RequestInfo requestInfo = (RequestInfo)param.get("RequestInfo");
            if(requestInfo!=null){
                MainTableInfo mainTableInfo = requestInfo.getMainTableInfo();
                DetailTableInfo detailTableInfo = requestInfo.getDetailTableInfo();
                Map<String,String> map = new HashMap<>();
                Property[] propertys = mainTableInfo.getProperty();
                for( Property property : propertys){
                    String str = property.getName();
                    String value = property.getValue();
                    map.put(str,value);
                }
                String[] value={map.get(col[0]),
                                map.get(col[1]),
                                map.get(col[2]),
                                map.get(col[3]),
                                map.get(col[4]),
                                map.get(col[5]),
                                map.get(col[6]),
                                map.get(col[7]),
                                map.get(col[8]),
                                map.get(col[9]),
                                map.get(col[10]),
                                map.get(col[11]),
                                map.get(col[12]),
                                map.get(col[13]),
                                map.get(col[14]),
                                map.get(col[15]),
                                map.get(col[16]),
                                map.get(col[17])
                                };
                InsertModuleUtil4samtable.insert("uf_cwzl_jfdgx",col,value,1,106,recordSetTrans);
            }
        } catch (Exception e) {
            result.put("errmsg","自定义出错信息");
            result.put("flag", "false");
        }
        return result;
    }
}