package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.util.InsertModuleUtil4samtable;
import com.engine.contract.util.Utils;
import com.engine.core.interceptor.CommandContext;
import com.weaver.formmodel.util.DateHelper;
import weaver.conn.RecordSet;
import weaver.conn.RecordSetTrans;
import weaver.general.Util;
import weaver.hrm.User;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;



//缴费单
public class CreatePayablereportCmd  extends AbstractCommonCommand<Map<String, Object>> {
    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    public CreatePayablereportCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;

    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String id= Util.null2String(params.get("id"));
        RecordSet recordSet = new RecordSet();
        String sql = "SELECT\n" +
                "\ttaba.* ,\n" +
                "\ttabb.yksrq,\n" +
                "\ttabb.yjsrq,\n" +
                "\ttabb.ksrq,\n" +
                "\ttabb.jsrq,\n" +
                "\ttabb.yhtlx,\n" +
                "\ttabb.htlx,\n" +
                "\ttabb.qy,\n" +
                "\ttabb.qyxx,\n" +
                "\ttabb.yhtqy \n" +
                "FROM\n" +
                "\tuf_cwzl_jdbb taba\n" +
                "\tLEFT JOIN uf_cwzl_jcht tabb ON taba.hth = tabb.id\n" +
                "\tWHERE taba.id = "+ id;
        String[] tabseason = {"hth","nf","jdts1","jdysje1","jdssje1","jdts2","jdysje2","jdssje2","jdts3","jdysje3","jdssje3","jdts4","jdysje4","jdssje4",
                "yksrq","yjsrq","ksrq","jsrq","yhtlx","htlx","qy","qyxx","yhtqy","ld","ldxx","ldbm"};
            recordSet.execute(sql);

        String[] cols = new String[tabseason.length];
        if (recordSet.next()){
            for (int i = 0; i < tabseason.length; i++) {
                cols[i] = Util.null2String(recordSet.getString(tabseason[i]));
            }
        }
        Date date = Utils.addmonnth(new Date(),3);
        String area = cols[21];
        if("1".equals(cols[19])){
            Date endold = Utils.setDatefunction(cols[15]);
            if(endold!=null){
                if(date.getTime()<endold.getTime()){
                    area = cols[22];
                }
            }
        }else if("2".equals(cols[19])){
            Date startnew = Utils.setDatefunction(cols[16]);
            if(startnew!=null){
                if(date.getTime()<startnew.getTime()){
                    area = cols[22];
                }
            }
        }
        //缴费单
//        DateHelper.getDate();
        String[] insertcol = {"hth","nf","jd","qy","gsqy","jfjzrq","qsrq","jzrq","xjnje","dyrq","hzqsrq","hzjzrq","hznf","hzjd","hzqy","ld","ldxx","ldbm"};
        String[] insertvalue = {
                cols[0],
                cols[1],
                String.valueOf(getseason(cols[1])),
                cols[20],
                area,
                DateHelper.getDate(getendDate(cols[1])),
                DateHelper.getDate(getstart(cols[1])),
                DateHelper.getDate(getend(cols[1])),
                Utils.null20(cols[3*getseason(cols[1])+3]).subtract(Utils.null20(cols[3*getseason(cols[1])+4])).toString(),
                DateHelper.getDate(new Date()),
                DateHelper.getDate(getstart(cols[1])),
                DateHelper.getDate(getend(cols[1])),
                cols[1],
                String.valueOf(getseason(cols[1])),
                cols[20],
                cols[23],
                cols[24],
                cols[25]
                };
        try {
            RecordSetTrans recordSetTrans = new RecordSetTrans();
            RecordSet select = new RecordSet();
            select.execute("SELECT * FROM uf_cwzl_jfdgx WHERE nf = "+cols[1]+" AND hth = "+cols[0]+" AND jd = '" + getseason(cols[1])+"'");
            int value;
            if(!select.next()){
                value  =  InsertModuleUtil4samtable.insert("uf_cwzl_jfdgx",insertcol,insertvalue,1, 106,recordSetTrans);
            }else{
                value = Integer.parseInt(select.getString("id"));
            }
            InsertModuleUtil4samtable.insert("uf_cwzl_jfd",insertcol,insertvalue,1, 104,recordSetTrans);
            result.put("bilid",value);
        } catch (Exception e) {
            e.printStackTrace();
            result.put("status", "-1");
        }
        result.put("status", "1");
        return result;
    }


    public  Date getend(String year){
        Date now = new Date();
        if(Integer.parseInt(year)>Utils.getyear(now)){
            return Utils.setDatefunction(Integer.parseInt(year),3,31);
        }else{
            int month = Utils.getMonth(now);
            if(month<4){
                return Utils.setDatefunction(Integer.parseInt(year),6,30);
            }else if(month<7){
                return Utils.setDatefunction(Integer.parseInt(year),9,30);
            }
            return Utils.setDatefunction(Integer.parseInt(year),12,31);
        }
    }


    public Date getstart(String year){
        Date now = new Date();
        if(Integer.parseInt(year)>Utils.getyear(now)){
            return Utils.setDatefunction(Integer.parseInt(year),1,1);
        }else{
            int month = Utils.getMonth(now);
            if(month<4){
                return Utils.setDatefunction(Integer.parseInt(year),4,1);
            }else if(month<7){
                return Utils.setDatefunction(Integer.parseInt(year),7,1);
            }
            return Utils.setDatefunction(Integer.parseInt(year),10,1);
        }
    }


    public Date getendDate(String year){
        Date now = new Date();
        if(Integer.parseInt(year)>Utils.getyear(now)){
            return Utils.setDatefunction(Utils.getyear(now),12,31);
        }else{
            int month = Utils.getMonth(now);
            if(month<4){
                return Utils.setDatefunction(Integer.parseInt(year),3,31);
            }else if(month<7){
                return Utils.setDatefunction(Integer.parseInt(year),6,30);
            }
            return Utils.setDatefunction(Integer.parseInt(year),9,30);
        }
    }



    //获取缴费季度
    public int getseason(String year){
        Date now = new Date();
        if(Integer.parseInt(year)>Utils.getyear(now)){
            return 0;
        }else{
            int month = Utils.getMonth(now);
            if(month<4){
                return 1;
            }else if(month<7){
                return 2;
            }
            return 3;
        }
    }
}
