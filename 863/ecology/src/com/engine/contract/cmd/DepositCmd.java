package com.engine.contract.cmd;

import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.contract.common.*;
import com.engine.contract.mapper.DepoSitMapper;
import com.engine.contract.util.BuildTreeUtil;
import com.engine.contract.util.ReportDataUtil;
import com.engine.contract.util.Utils;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.constant.SDThreadPoolCst;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.weaver.formmodel.util.DateHelper;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import weaver.conn.RecordSet;
import weaver.conn.mybatis.MyBatisFactory;
import weaver.general.BaseBean;
import weaver.general.ThreadPoolUtil;
import weaver.general.Util;
import weaver.hrm.User;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;

/**
 * @FileName DepositCmd
 * @Description 保证金生成逻辑
 * <AUTHOR>
 * @Version v1.00
 * @Date 2023/7/12
 */
public class DepositCmd extends AbstractCommonCommand<Map<String, Object>> {


    public DepositCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        bb = new BaseBean();
        this.currentdate = new Date();
    }

    public DepositCmd() {
    }


    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    private BaseBean bb;

    private Date currentdate;

    //获取上个月时间
    private Date lastdate;

    //下个月时间
    private Date nextdate;

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {

        return doExecute(currentdate, "uf_bzjxx", "uf_bzjxx_dt1", 182, true);
    }

    public Map<String, Object> doExecute(Date date, String maintable, String dttable, int module, boolean needclear) {
        this.setCurrentdate(date);
        this.setLastdate(Utils.getLastMonthDate(date));
        this.setNextdate(Utils.getNextMonthDate(date));
        Map<String, Object> result = new HashMap<>();
        try {
            bb.writeLog(this.getClass().getName() + " --- START");
            SqlSessionFactory sqlSessionFactory = MyBatisFactory.sqlSessionFactory;
            bb.writeLog(sqlSessionFactory + " --- sqlSessionFactory");
            SqlSession sqlSession = sqlSessionFactory.openSession();
            bb.writeLog(sqlSession + " --- sqlSession");
            DepoSitMapper mapper = sqlSession.getMapper(DepoSitMapper.class);

            //获取新签保证金（包括补充保证金）
            //formtable_main_43 863-房屋租赁合同审批单（新签）
            /*
              SELECT   taba.qyxx, taba.xzqy AS qy, taba.id, 0 AS htlx, taba.sfhdzqj AS flag, taba.requestid AS lylc, taba.zlksrq AS startDate,
                              taba.zljsrq AS endDate, taba.bzj AS deposit, (CASE WHEN belongto = - 1 OR
                              belongto IS NULL THEN 0 ELSE 1 END) AS gz
              FROM      dbo.formtable_main_43 AS taba LEFT OUTER JOIN
                              dbo.workflow_requestbase AS tabb ON taba.requestid = tabb.requestid LEFT OUTER JOIN
                              dbo.HrmResource AS hrm ON hrm.id = taba.sqry
              WHERE   (tabb.currentnodetype = '3')
              union all
              select qyxx,qy,nid as id ,htlx,flag,requestId as lylc,startDate,endDate,deposit,belong as gz from uf_bcbzj
             */
            List<ContracDepositBean> newcontract = mapper.getNewContractAmount();
            bb.writeLog(newcontract.toString() + " --- newcontract");

            //续签保证金 formtable_main_78 863-房屋租赁合同审批单（续签）
            /*
              SELECT   taba.qyxx, taba.xzqy AS qy, taba.id, 1 AS htlx, taba.sfhdzqj AS flag, taba.requestid AS lylc, taba.yhtlc,
                              taba.zlksrq AS startDate, taba.zljsrq AS endDate, taba.zljsrq AS moveDate, taba.xqhtbzj AS deposit,
                              (CASE WHEN belongto = - 1 OR
                              belongto IS NULL THEN 0 ELSE 1 END) AS gz
              FROM      dbo.formtable_main_78 AS taba LEFT OUTER JOIN
                              dbo.workflow_requestbase AS tabb ON taba.requestid = tabb.requestid LEFT OUTER JOIN
                              dbo.HrmResource AS hrm ON hrm.id = taba.sqry
              WHERE   (tabb.currentnodetype = '3')
             */
            List<ContracDepositBean> renewcontract = mapper.getRenewContractAmount();
            bb.writeLog(renewcontract.toString() + " --- renewcontract");


            //变更保证金 formtable_main_100 863-房屋租赁合同审批单（变更）
            /*
             SELECT
              	taba.qyxx,
              	taba.xzqy AS qy,
              	taba.id,
              	2 AS htlx,
              	taba.sfhdzqj AS flag,
              	taba.requestid AS lylc,
              	taba.yhtlc,
              	taba.zlksrq AS startDate,
              	taba.zljsrq AS moveDate,
              	taba.zljsrq AS endDate,
              	( CASE WHEN (taba.bghsbbzj IS NOT NULL and taba.bghsbbzj != '')  THEN taba.bghsbbzj ELSE cast(taba.bzj as varchar) END ) AS deposit,
              	( CASE WHEN belongto = - 1 OR belongto IS NULL THEN 0 ELSE 1 END ) AS gz
              FROM
              	dbo.formtable_main_100 AS taba
              	LEFT OUTER JOIN dbo.workflow_requestbase AS tabb ON taba.requestid = tabb.requestid
              	LEFT OUTER JOIN dbo.HrmResource AS hrm ON hrm.id = taba.sqry
              WHERE
              	( tabb.currentnodetype = '3' )
             */
            List<ContracDepositBean> altercontract = mapper.getalterContractAmount();
            bb.writeLog(altercontract.toString() + " --- altercontract");

            //退租 formtable_main_45 863-房屋退租结算单
            /*
             SELECT   taba.qyxx, taba.xzqy AS qy, taba.id, 3 AS htlx, taba.requestId AS lylc, taba.ylc AS yhtlc, NULL AS startDate,
                              (CASE WHEN taba.jsrq IS NOT NULL THEN taba.jsrq ELSE taba.htjsrq END) AS moveDate, NULL AS endDate,
                              taba.bzjje AS deposit, (CASE WHEN belongto = - 1 OR
                              belongto IS NULL THEN 0 ELSE 1 END) AS gz
              FROM      dbo.formtable_main_45 AS taba LEFT OUTER JOIN
                              dbo.workflow_requestbase AS tabb ON taba.requestId = tabb.requestid LEFT OUTER JOIN
                              dbo.HrmResource AS hrm ON hrm.id = taba.sqry
              WHERE   (tabb.currentnodetype = '3')
             */
            List<ContracDepositBean> endcontract = mapper.getEndContractAmount();
            bb.writeLog(endcontract.toString() + " --- endcontract");

            List<ContracDepositBean> list = merge(newcontract, renewcontract, altercontract, endcontract);


            bb.writeLog(list.toString() + " --- list");

            HashMap<Company, DepositCol> datas = doCreateData(list);

            //插入数据
            insertData(datas, maintable, dttable, module, needclear, date);

            sqlSession.close();
            result.put("status", "1");
        } catch (Exception e) {
            result.put("status", "-1");
            result.put("dec", e.getMessage());
            bb.writeLog("catch exception：" + e.getMessage());
        }
        return result;
    }

    public List<String> getmainCols() {
        List<String> list = new ArrayList<>();
        list.add("qy");
        list.add("gz");
        list.add("dqzzxlc");
        list.add("lslc");
        list.add("bzj");
        list.add("tzbzj");
        list.add("dqny");
        list.add("sgybhl");
        list.add("xgybhl");
        list.add("ischange");
        return list;
    }

    public String getDetailsql(DepositcolDetail detail, String billid, String table) {
        List<String> list = new ArrayList<>();
        list.add("'" + billid + "'");
        list.add("'" + detail.getCurrentContract() + "'");
        list.add("'" + String.join(",", detail.getHistoryContract()) + "'");
        list.add("'" + detail.getQyxx() + "'");
        list.add("'" + detail.getAmount() + "'");
        list.add("'" + detail.getBackAmount() + "'");
        list.add("'" + detail.getLastRemark() + "'");
        list.add("'" + detail.getNextRemark() + "'");
        list.add("'" + detail.getNextContract() + "'");
        list.add("'" + detail.getLastContract() + "'");
        list.add("'" + detail.getNextAmount() + "'");
        list.add("'" + detail.getLastAmount() + "'");
        String sql = " insert into " +
                table +
                " (mainid,dqzzxhtlc,sylshtlc,qyxx,bzj,tzbzj,sgybzjsm,xgybzjsm,xgyhtlc,sgyhtlc,xgybzj,sgybzj ) VALUES (" +
                String.join(",", list) + ") ";
        bb.writeLog("sql------------" + sql);
        return sql;
    }


    /**
     * 执行插入操作
     *
     * @param obj
     */
    public void insertData(HashMap<Company, DepositCol> obj, String maintable, String dttable, int module, boolean needclear, Date date) {

        Set<Map.Entry<Company, DepositCol>> sets = obj.entrySet();

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM");
        //清除表数据
        String yearAndMonth = dateFormat.format(date);

        if (needclear) {
            ReportDataUtil.clearModuleData(maintable);
            ReportDataUtil.clearModuleData(dttable);
        }
//        ReportDataUtil.clearModuleData("uf_bzjxx");
//        ReportDataUtil.clearModuleData("uf_bzjxx_dt1");

        for (Map.Entry<Company, DepositCol> set :
                sets) {
            Company company = set.getKey();
            String qy = company.getName();
            String gz = company.getAffiliation();
            DepositCol main = set.getValue();
            bb.writeLog("qy------" + qy);
            bb.writeLog("main------" + main.toString());
            try {
                List<DepositcolDetail> details = main.getDetails();
//                BigDecimal amount = main.getAmount().subtract(main.getBackamount());
                ModuleInsertBean insertBean = new ModuleInsertBean();
                List<Object> data = new ArrayList<>();
                data.add(qy);
                data.add(gz);
                data.add(String.join(",", main.getCurrentContract()));
                data.add(String.join(",", main.getHistoryContract()));
                data.add(main.getAmount().toString());
                data.add(main.getBackamount().toString());
                data.add(yearAndMonth);
                data.add(main.getLastamountchange());
                data.add(main.getNextamountchange());
                data.add(main.getIschange());
                insertBean.setModuleId(module);
                insertBean.setCreatorId(1);
                insertBean.setTableName(maintable);
                insertBean.setFields(getmainCols());
                insertBean.setValue(data);
                bb.writeLog("insertBean---------------" + insertBean);
                //step 1: 检查参数
                String erroMsg = ModuleDataUtil.checkParam(insertBean);
                if (erroMsg.isEmpty()) {
                    //step 2:  创建ExecutorService线程池 异步执行操作
                    getExecutor().execute(() -> {
                        //step 3: 执行插入操作
                        ModuleResult moduleResult = ModuleDataUtil.doInsertDataOne(insertBean);
                        int billid = moduleResult.getBillid();
                        RecordSet rs = new RecordSet();
                        for (DepositcolDetail detail :
                                details) {
                            String sql = getDetailsql(detail, String.valueOf(billid), dttable);
                            rs.execute(sql);
                        }
                    });
                } else {
                    bb.writeLog(erroMsg);
                }
            } catch (Exception e) {
                bb.writeLog("-------" + SDUtil.getExceptionDetail(e));
                SDUtil.getExceptionDetail(e);
            }

        }
    }


    /**
     * 获取线程池
     *
     * @return
     */
    private static ExecutorService getExecutor() {
        ExecutorService exeService;
        //判断是否有缓存的线程池
        if (!ThreadPoolUtil.cachePoolExits(SDThreadPoolCst.MODULE_DATA_THREAD_NAME)) {
            //IO 密集型任务，线程池大小一般设置为 CPU 核心数乘以一个系数，常见的系数是 2 或者 2.5。这里主要是数据库交互
            int poolSize = Runtime.getRuntime().availableProcessors() * SDThreadPoolCst.POOL_SIZE_RATIO;
            exeService = ThreadPoolUtil.getThreadPool(SDThreadPoolCst.MODULE_DATA_THREAD_NAME, String.valueOf(poolSize));
        } else {
            exeService = ThreadPoolUtil.getCachePool(SDThreadPoolCst.MODULE_DATA_THREAD_NAME);
        }
        return exeService;
    }

    /**
     * 生成保证金数据
     *
     * @return
     */
    public HashMap<Company, DepositCol> doCreateData(List<ContracDepositBean> list) {
        bb.writeLog("in docreateData");
        HashMap<Company, DepositCol> data = new HashMap<>();
        try {
            for (ContracDepositBean contract : list
            ) {
                String qy = Util.null2String(contract.getQy());
                String gz = Util.null2String(contract.getGz());
                if (!"".equals(qy) && !"".equals(gz)) {
                    DepositCol depositCol;
                    Company company = new Company(qy, gz);
                    if (data.containsKey(company)) {
                        depositCol = data.get(company);
                    } else {
                        depositCol = new DepositCol();
                        data.put(company, depositCol);
                    }
                    //新建明细
                    DepositcolDetail detail = new DepositcolDetail();
                    //递归处理
                    injectData(contract, depositCol, detail);

                    //添加提示语句 修改需求 直接显示变化差
                    if (detail.getLastAmount() == null) {
                        detail.setLastAmount(BigDecimal.ZERO);
                    }
                    if (detail.getNextAmount() == null) {
                        detail.setNextAmount(BigDecimal.ZERO);
                    }
                    if (detail.getAmount() == null && detail.getCurrentContract() == null) {
                        detail.setAmount(BigDecimal.ZERO);
                    }

                    detail.setLastRemark("上月保证金变化为：" +
                            "变更金额为: (" + detail.getAmount() + "-" + detail.getLastAmount() + " = " + detail.getAmount().subtract(detail.getLastAmount()));
                    detail.setNextRemark("下月保证金变化为：" +
                            "变更金额为: (" + detail.getNextAmount() + "-" + detail.getAmount() + ") = " + detail.getNextAmount().subtract(detail.getAmount()));
                    if (detail.getNextAmount().subtract(detail.getAmount()).compareTo(BigDecimal.ZERO) != 0
                            || detail.getAmount().subtract(detail.getLastAmount()).compareTo(BigDecimal.ZERO) != 0
                    ) {
                        depositCol.setIschange("1");
                    }
                    depositCol.setLastamountchange(depositCol.getLastamountchange().add(detail.getAmount().subtract(detail.getLastAmount())));
                    depositCol.setNextamountchange(depositCol.getNextamountchange().add(detail.getNextAmount().subtract(detail.getAmount())));
                    //添加明细
                    depositCol.getDetails().add(detail);
                }
            }

            return data;

        } catch (Exception e) {
            bb.writeLog(e.getMessage() + "--" + e);
        }
        return data;
    }


    /**
     * 地递归添加保证金数据结构到root
     *
     * @param contract
     * @param root
     * @param leaf
     * @return
     */
    public Date injectData(ContracDepositBean contract, DepositCol root, DepositcolDetail leaf) {
        //公共数据
        Date moveDate = DateHelper.parseDate(contract.getMoveDate());
        String requestid = contract.getLylc();
        String type = contract.getHtlx();
        String flag = contract.getFlag();
        String qyxx = Util.null2String(contract.getQyxx());
        String deposit = Util.null2o(contract.getDeposit()); //主表保证金
        BigDecimal amount = new BigDecimal(deposit);
        Date OverDate = null;
        List<ContractTree> children = contract.getChildren();
        if (children != null && children.size() != 0) {
            for (ContractTree child :
                    children) {
                if (children.size() <= 1 || !"3".equals(child.getHtlx())) {
                    OverDate = injectData((ContracDepositBean) child, root, leaf);
                }
            }
        }

        //放入requestid
        root.getHistoryContract().add(requestid);
        leaf.getHistoryContract().add(requestid);
        //放入区域信息
        leaf.setQyxx(qyxx);
        // 退租合同处理
        if ("3".equals(type)) {

            //添加退租保证金
            leaf.setBackAmount(amount);
            root.setBackamount(root.getBackamount().add(amount));

            //获取负数保证金
//            BigDecimal amountnagate = amount.negate();
            BigDecimal amountnagate = BigDecimal.ZERO;

            if (moveDate != null) {
                //添加在执行合同
                if (currentdate.compareTo(moveDate) > 0) {
                    leaf.setCurrentContract(requestid);
                    leaf.setAmount(amountnagate);
                    root.getCurrentContract().add(requestid);
                }

                if (lastdate.compareTo(moveDate) > 0) {
                    leaf.setLastAmount(amountnagate);
                    leaf.setLastContract(requestid);
                }

                if (nextdate.compareTo(moveDate) > 0) {
                    leaf.setNextAmount(amountnagate);
                    leaf.setNextContract(requestid);
                }
            }

            return moveDate;
        }

        //判断是否递增
        if ("0".equals(flag)) {
            List<ContactDepositDetail> detail = contract.getDetails();
            for (ContactDepositDetail dt :
                    detail) {
                Date startdate = DateHelper.parseDate(dt.getStartDate());
                Date enddate = DateHelper.parseDate(dt.getEndDate());
                if (OverDate == null) {
                    executeRootAndleaf(startdate, enddate, requestid, amount, leaf, root);
                } else {
                    if (startdate != null && enddate != null) {
                        if (OverDate.compareTo(startdate) >= 0 && OverDate.compareTo(enddate) <= 0) {
                            executeRootAndleaf(startdate, OverDate, requestid, amount, leaf, root);
                        } else if (OverDate.compareTo(enddate) >= 0) {
                            executeRootAndleaf(startdate, enddate, requestid, amount, leaf, root);
                        }
                    }
                }
            }
        } else {
            Date startdate = DateHelper.parseDate(contract.getStartDate());
            Date enddate = DateHelper.parseDate(contract.getEndDate());
            if (startdate != null && enddate != null) {
                if (OverDate == null) {
                    executeRootAndleaf(startdate, enddate, requestid, amount, leaf, root);
                } else if (OverDate.compareTo(enddate) <= 0) {
                    executeRootAndleaf(startdate, OverDate, requestid, amount, leaf, root);
                } else {
                    executeRootAndleaf(startdate, enddate, requestid, amount, leaf, root);
                }
            }
        }
        return moveDate;
    }


    /**
     * 处理数据
     *
     * @param startdate
     * @param enddate
     * @param requestid
     * @param amount
     * @param leaf
     * @param root
     */
    public void executeRootAndleaf(Date startdate, Date enddate,
                                   String requestid,
                                   BigDecimal amount,
                                   DepositcolDetail leaf,
                                   DepositCol root
    ) {
        if (CheckInDates(startdate, enddate, currentdate)) {
            leaf.setAmount(amount);
            root.setAmount(root.getAmount().add(amount));
            leaf.setCurrentContract(requestid);
            root.getCurrentContract().add(requestid);
        }
        if (CheckInDates(startdate, enddate, lastdate)) {
            leaf.setLastAmount(amount);
            leaf.setLastContract(requestid);
        }
        if (CheckInDates(startdate, enddate, nextdate)) {
            leaf.setNextAmount(amount);
            leaf.setNextContract(requestid);
        }
    }


    /**
     * 判断是否在日期中
     *
     * @param start
     * @param end
     * @param check
     * @return
     */
    public boolean CheckInDates(Date start, Date end, Date check) {
        return check.compareTo(start) >= 0 && check.compareTo(end) <= 0;
    }

    /**
     * 合并构建合同树
     *
     * @return
     */
    public List<ContracDepositBean> merge(
            List<ContracDepositBean> newcontract,
            List<ContracDepositBean> renewcontract,
            List<ContracDepositBean> altercontract,
            List<ContracDepositBean> endcontract
    ) {
        List<ContracDepositBean> list = new ArrayList<>();
        list.addAll(newcontract);
        list.addAll(renewcontract);
        list.addAll(altercontract);
        list.addAll(endcontract);
        //构建合同树
        return createData(list);
    }

    /**
     * 构建合同树
     *
     * @return
     */
    public List<ContracDepositBean> createData(List<ContracDepositBean> lists) {
        return BuildTreeUtil.listToTree(lists);
    }

    public Date getCurrentdate() {
        return currentdate;
    }

    public Date getLastdate() {
        return lastdate;
    }

    public void setCurrentdate(Date currentdate) {
        this.currentdate = currentdate;
    }

    public void setLastdate(Date lastdate) {
        this.lastdate = lastdate;
    }

    public Date getNextdate() {
        return nextdate;
    }

    public void setNextdate(Date nextdate) {
        this.nextdate = nextdate;
    }
}
