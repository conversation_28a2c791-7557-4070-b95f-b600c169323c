package com.engine.contract.mapper;

import com.engine.contract.common.AmountContractBean;
import com.engine.contract.common.ContractBean;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ContractMapper {

    List<ContractBean> selectAllContract();

    @MapKey("htid")
    Map<String, AmountContractBean> selectAmountReceived();

    @MapKey("htid")
    Map<String, AmountContractBean> selectsupplementary();
}
