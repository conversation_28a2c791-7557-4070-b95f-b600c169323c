package com.engine.contract.mapper;


import com.engine.contract.common.ContracDepositBean;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface DepoSitMapper {

    /**
     * 获取新签合同保证金
     *
     * @return
     */
    List<ContracDepositBean> getNewContractAmount();


    /**
     * 获取续签合同保证金
     *
     * @return
     */
    List<ContracDepositBean> getRenewContractAmount();

    /**
     * 获取变更合同保证金
     *
     * @return
     */
    List<ContracDepositBean> getalterContractAmount();

    /**
     * 获取退租合同保证金
     *
     * @return
     */
    List<ContracDepositBean> getEndContractAmount();
}
