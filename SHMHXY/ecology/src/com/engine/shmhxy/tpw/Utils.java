package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.weaver.general.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;

import java.util.*;

public class Utils {
    private static final Logger log = LoggerFactory.getLogger(Utils.class);
    private static final RecordSet rs = new RecordSet();
    private static final int COUNT = 10;
    /**
     * HRM restful 同步类
     */
    private static HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();

    //建模表单中同步配置表的主表信息
    public static JSONObject getMainBaseInfo(String sql) {
        try {
            JSONObject jsonObject = new JSONObject();
            rs.execute(sql);
            while (rs.next()) {
                String[] columnNames = rs.getColumnName();
                if (columnNames != null && columnNames.length > 0) {
                    for (String columnName : columnNames) {
                        jsonObject.put(columnName, rs.getString(columnName));
                    }
                }
                break;
            }
            return jsonObject;
        } catch (Exception e) {
            log.info("获取建模表中配置表的信息getMainBaseInfo:" + sql + "出错！错误信息:" + SDUtil.getExceptionDetail(e));
        }
        return null;
    }

    //获取token
    public static String getTokenInfo(JSONObject tc) {
        String urlAdd = tc.getString("jkzydz") + tc.getString("tokenjkdz");
        String username = tc.getString("jkyhm");
        String password = tc.getString("jkmm");
        String url = urlAdd + "?username=" + username + "&password=" + password;
        log.info("获取token请求url" + url);
        RestResult tokenResult = HttpUtil.getData(url);
        log.info("获取的tokenResult信息" + JSON.toJSONString(tokenResult));
        String body = tokenResult.getResponseInfo().getBody();
        if (tokenResult.isSuccess() && StringUtils.isNotBlank(body)) {
            String token = (String) JSON.parseObject(body).get("token");
            if (StringUtils.isBlank(token)) {
                return null;
            }
            return token;
        } else {
            return null;
        }
    }


    //分页获取中台数据部门信息
    public static JSONArray getThirdHrmInfoWithPage(ArrayList<Object> errMsgList,String token, String configFormName, SFSyncDetailLog sfSyncDetailLog) {
        JSONArray mergedArray = null;
        try {
            if (sfSyncDetailLog == null) {
                sfSyncDetailLog = new SFSyncDetailLog();
            }

            JSONArray xybw = new JSONArray();
            mergedArray = new JSONArray();
            int Jkjg = 0;
            JSONObject fo = getMainBaseInfo("select * from " + configFormName);
            rs.execute("select * from  uf_zzjgtb where id = " + fo.getString("xzzpz"));
            String url = "";
            while (rs.next()) {
                url = rs.getString("jkzydz") + fo.getString("jkdz");
                break;
            }
            if (StringUtils.isBlank(url)) {
                log.info("发送请求的url为空！不能执行发送请求，后续操作不再执行");
                return null;
            }
            boolean flag = true;
            int page = 1;
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json, text/plain, */*");
            headers.put("X-Token", token);
            JSONObject bodyParam = new JSONObject();
            JSONObject qqbw = new JSONObject();
            bodyParam.put("filter", new JSONArray()); // 空数组
            bodyParam.put("rows", 1000);
            bodyParam.put("sidx", "cityosLandingTs");
            bodyParam.put("sord", "asc");
            bodyParam.put("page", page);
            long startTime = System.currentTimeMillis(); // 获取结束时间
            //请求时间
            sfSyncDetailLog.setQqsj(TimeUtil.getCurrentTimeString());
            do {
                //发送分页 post请求 获取学校部门基本数据
                bodyParam.put("page", page);
                RestResult restResult = HttpUtil.postDataWithHeader(url, headers, bodyParam.toJSONString());
                xybw.add(restResult);
                String body = restResult.getResponseInfo().getBody();
                if (restResult.isSuccess() && StringUtils.isNotBlank(body)) {
                    JSONObject jb = JSON.parseObject(body);
                    JSONArray rows = jb.getJSONArray("rows");
                    mergedArray.addAll(rows);
                    if (String.valueOf(page).equals(jb.getString("totalPage")) || "0".equals(jb.getString("totalPage"))) {
                        flag = false;
                    } else {
                        page++;
                    }
                } else {
                    flag = false;
                    Jkjg = 1;
                }
            } while (flag);
            String jkm = "";
            if (StringUtils.isNotBlank(configFormName)) {
                switch (configFormName) {
                    case "uf_xxbmtb":
                        jkm = "请求学校部门数据";
                        break;
                    case "uf_bjbmtb":
                        jkm = "请求学校班级数据";
                        break;
                    case "uf_lsrytb":
                        jkm = "请求学校老师数据";
                        break;
                    case "uf_xsrytb":
                        jkm = "请求学校学生数据";
                        break;
                }
            }
            //接口名
            sfSyncDetailLog.setJkm(jkm);
            qqbw.put("请求Url",url);
            qqbw.put("请求header",headers);
            qqbw.put("请求Body",bodyParam);
            //请求报文
            String qqbwUUID = UUID.randomUUID().toString();
            sfSyncDetailLog.setQqbw(qqbwUUID);
            log.info("中台数据的请求信息："+qqbwUUID+"请求报文："+JSONObject.toJSONString(qqbw));
            //响应报文
            String uuid = UUID.randomUUID().toString();
//            sfSyncDetailLog.setXybw(Utils.truncateText(JSONObject.toJSONString(xybw)));
            sfSyncDetailLog.setXybw(uuid);
            log.info("中台数据的响应信息："+uuid+"响应报文："+JSONObject.toJSONString(xybw));
            //响应时间
            sfSyncDetailLog.setXysj(TimeUtil.getCurrentTimeString());
            //接口耗时
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
            double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
            sfSyncDetailLog.setJkhs(String.valueOf(elapsedSeconds));
            //接口结果
            sfSyncDetailLog.setJkjg(Jkjg);
        } catch (Exception e) {
            log.info("getThirdHrmInfoWithPage方法出错:" + SDUtil.getExceptionDetail(e));
            sfSyncDetailLog.setJkjg(1);
        }

        return mergedArray;
    }

    public static JSONArray getThirdHrmInfoWithPage3(ArrayList<Object> errMsgList,String token, String configFormName) {
        JSONArray mergedArray = null;
        try {

            JSONArray xybw = new JSONArray();
            mergedArray = new JSONArray();
            int Jkjg = 0;
            JSONObject fo = getMainBaseInfo("select * from " + configFormName);
            rs.execute("select * from  uf_zzjgtb where id = " + fo.getString("xzzpz"));
            String url = "";
            while (rs.next()) {
                url = rs.getString("jkzydz") + fo.getString("jkdz");
                break;
            }
            if (StringUtils.isBlank(url)) {
                log.info("发送请求的url为空！不能执行发送请求，后续操作不再执行");
                return null;
            }
            boolean flag = true;
            int page = 1;
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json, text/plain, */*");
            headers.put("X-Token", token);
            JSONObject bodyParam = new JSONObject();
            JSONObject qqbw = new JSONObject();
            bodyParam.put("filter", new JSONArray()); // 空数组
            bodyParam.put("rows", 1000);
            bodyParam.put("sidx", "cityosLandingTs");
            bodyParam.put("sord", "asc");
            bodyParam.put("page", page);
            long startTime = System.currentTimeMillis(); // 获取结束时间
            //请求时间
            do {
                //发送分页 post请求 获取学校部门基本数据
                bodyParam.put("page", page);
                RestResult restResult = HttpUtil.postDataWithHeader(url, headers, bodyParam.toJSONString());
                xybw.add(restResult);
                String body = restResult.getResponseInfo().getBody();
                if (restResult.isSuccess() && StringUtils.isNotBlank(body)) {
                    JSONObject jb = JSON.parseObject(body);
                    JSONArray rows = jb.getJSONArray("rows");
                    mergedArray.addAll(rows);
                    if (String.valueOf(page).equals(jb.getString("totalPage"))) {
                        flag = false;
                    } else {
                        page++;
                    }
                } else {
                    flag = false;
                    Jkjg = 1;
                }
            } while (flag);
            //接口名
            qqbw.put("请求Url",url);
            qqbw.put("请求header",headers);
            qqbw.put("请求Body",bodyParam);
            //请求报文
            String qqbwUUID = UUID.randomUUID().toString();
            log.info("中台数据的岗位接口请求信息："+qqbwUUID+"请求报文："+JSONObject.toJSONString(qqbw));
            //响应报文
            String uuid = UUID.randomUUID().toString();
            log.info("中台数据的岗位接口响应信息："+uuid+"响应报文："+JSONObject.toJSONString(xybw));
        } catch (Exception e) {
            log.info("getThirdHrmInfoWithPage3方法出错:" + SDUtil.getExceptionDetail(e));
        }

        return mergedArray;
    }


    //分页获取中台数据部门信息  有过滤条件
    public static JSONArray getThirdHrmInfoWithPage2(ArrayList<Object> errMsgList,String token, String configFormName, SFSyncDetailLog sfSyncDetailLog,JSONArray filerArray) {
        JSONArray mergedArray = null;
        try {
            if (sfSyncDetailLog == null) {
                sfSyncDetailLog = new SFSyncDetailLog();
            }

            JSONArray xybw = new JSONArray();
            mergedArray = new JSONArray();
            int Jkjg = 0;
            JSONObject fo = getMainBaseInfo("select * from " + configFormName);
            rs.execute("select * from  uf_zzjgtb where id = " + fo.getString("xzzpz"));
            String url = "";
            while (rs.next()) {
                url = rs.getString("jkzydz") + fo.getString("jkdz");
                break;
            }
            if (StringUtils.isBlank(url)) {
                log.info("发送请求的url为空！不能执行发送请求，后续操作不再执行");
                return null;
            }
            boolean flag = true;
            int page = 1;
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json, text/plain, */*");
            headers.put("X-Token", token);
            JSONObject bodyParam = new JSONObject();
            JSONObject qqbw = new JSONObject();
            bodyParam.put("filter",filerArray);
            bodyParam.put("rows", 1000);
            bodyParam.put("sidx", "cityosLandingTs");
            bodyParam.put("sord", "asc");
            bodyParam.put("page", page);
            long startTime = System.currentTimeMillis(); // 获取结束时间
            //请求时间
            sfSyncDetailLog.setQqsj(TimeUtil.getCurrentTimeString());
            do {
                //发送分页 post请求 获取学校部门基本数据
                bodyParam.put("page", page);
                RestResult restResult = HttpUtil.postDataWithHeader(url, headers, bodyParam.toJSONString());
                if("uf_lsrytb".equals(configFormName)){
                    log.info("中台数据的响应信息restResult："+JSONObject.toJSONString(restResult));
                }
                xybw.add(restResult);
                String body = restResult.getResponseInfo().getBody();
                if (restResult.isSuccess() && StringUtils.isNotBlank(body)) {
                    JSONObject jb = JSON.parseObject(body);
                    JSONArray rows = jb.getJSONArray("rows");
                    mergedArray.addAll(rows);
                    if (String.valueOf(page).equals(jb.getString("totalPage")) || "0".equals(jb.getString("totalPage"))) {
                        flag = false;
                    } else {
                        page++;
                    }
                } else {
                    flag = false;
                    Jkjg = 1;
                }
            } while (flag);
            String jkm = "";
            if (StringUtils.isNotBlank(configFormName)) {
                switch (configFormName) {
                    case "uf_xxbmtb":
                        jkm = "请求学校部门数据";
                        break;
                    case "uf_bjbmtb":
                        jkm = "请求学校班级数据";
                        break;
                    case "uf_lsrytb":
                        jkm = "请求学校老师数据";
                        break;
                    case "uf_xsrytb":
                        jkm = "请求学校学生数据";
                        break;
                }
            }
            //接口名
            sfSyncDetailLog.setJkm(jkm);
            qqbw.put("请求Url",url);
            qqbw.put("请求header",headers);
            qqbw.put("请求Body",bodyParam);
            //请求报文
            String qqbwUUID = UUID.randomUUID().toString();
            sfSyncDetailLog.setQqbw(qqbwUUID);
            log.info("中台数据的请求信息："+qqbwUUID+"请求报文："+JSONObject.toJSONString(qqbw));
            //响应报文
            String uuid = UUID.randomUUID().toString();
//            sfSyncDetailLog.setXybw(Utils.truncateText(JSONObject.toJSONString(xybw)));
            sfSyncDetailLog.setXybw(uuid);
            log.info("中台数据的响应信息："+uuid+"响应报文："+JSONObject.toJSONString(xybw));
            //响应时间
            sfSyncDetailLog.setXysj(TimeUtil.getCurrentTimeString());
            //接口耗时
            long endTime = System.currentTimeMillis(); // 获取结束时间
            long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
            double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
            sfSyncDetailLog.setJkhs(String.valueOf(elapsedSeconds));
            //接口结果
            sfSyncDetailLog.setJkjg(Jkjg);
        } catch (Exception e) {
            log.info("getThirdHrmInfoWithPage方法出错:" + SDUtil.getExceptionDetail(e));
            sfSyncDetailLog.setJkjg(1);
        }

        return mergedArray;
    }


    //获取中台组织架构同步-建模表单中同步配置的信息--明细表
    public static JSONArray getDetailBaseInfo(String sql) {
        try {
            JSONArray JA = new JSONArray();
            String[] columnNames = null;
            rs.execute(sql);
            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                if (columnNames == null || columnNames.length <= 0) {
                    columnNames = rs.getColumnName();
                }
                if (columnNames != null && columnNames.length > 0) {
                    for (String columnName : columnNames) {
                        jsonObject.put(columnName, rs.getString(columnName));
                    }
                    JA.add(jsonObject);
                }
            }
            return JA;
        } catch (Exception e) {
            log.info("getDetailBaseInfo出错:" + sql + "错误信息:" + SDUtil.getExceptionDetail(e));
        }
        return null;
    }

    public static String UtilParams(JSONObject config, JSONObject dept) {
        try {
            String index = Util.null2String(config.getString("zhgz"));
            if ("0".equals(index)) {//不做转换
                return Util.null2String(dept.getString(config.getString("jkzd")));
            } else if ("1".equals(index)) {//固定值
                return Util.null2String(config.getString("gdz"));
            } else if ("2".equals(index)) {//sql语句
                String newParams = Util.null2String(dept.getString(config.getString("jkzd")));
                String OAJKZD = Util.null2String(config.getString("oajkzd"));
                String sql = config.getString("zdysql");
                String newSql = sql.replace("?", "'" + newParams + "'");
                rs.execute(newSql);
                if (rs.next()) {
                    return Util.null2String(rs.getString("newValue"));
                }
            }
        } catch (Exception e) {
            log.info("参数转换异常" + SDUtil.getExceptionDetail(e));
        }
        return null;
    }
    public static String UtilParams2(JSONObject config, JSONObject dept,HashMap<String, String> jobtitleMap) {
        try {
            String newParams = "";
            String YHXX = Util.null2String(dept.getString("YHXX"));
            //做匹配
            if(jobtitleMap.containsKey(YHXX)){
                newParams = Util.null2String(jobtitleMap.get(YHXX));
            }
            String sql = config.getString("zdysql");
            String newSql = sql.replace("?", "'" + newParams + "'");
            rs.execute(newSql);
            if (rs.next()) {
                return Util.null2String(rs.getString("newValue"));
            }

        } catch (Exception e) {
            log.info("参数转换异常" + SDUtil.getExceptionDetail(e));
        }
        return null;
    }

    public static void custFields2(JSONObject config, JSONObject dept, JSONObject jo,HashMap<String, String> jobtitleMap) {
        String OAJKZD = Util.null2String(config.getString("oajkzd"));
        String jkzdValue = UtilParams(config, dept);
        if("jobtitle".equals(OAJKZD)){
            jkzdValue = UtilParams2(config, dept,jobtitleMap);
        }
        jo.put(OAJKZD, jkzdValue);
    }
    public static void custFields(JSONObject config, JSONObject dept, JSONObject jo) {
        String OAJKZD = Util.null2String(config.getString("oajkzd"));
        String jkzdValue = UtilParams(config, dept);
        if(StringUtils.isNotBlank(jkzdValue)){
            jo.put(OAJKZD, jkzdValue);
        }
    }

    public static Map<String, Object> buildDeptParam(String action, String code, String name, String parentCode, String subCompanyCode, String canceled, JSONObject custObject) {
        Map<String, Object> result = new HashMap<>();
        result.put("@action", action);
        result.put("code", code);
        result.put("shortname", name);
        result.put("fullname", name);
        result.put("org_code", subCompanyCode);
        if (StringUtils.isNotBlank(parentCode)) {
            result.put("parent_code", parentCode);
        }
//        if ("delete".equals(action)) {
        if(StringUtils.isNotBlank(canceled)){
            result.put("canceled", canceled);
        }

//        }
        //设置自定义字段信息
        result.put("custom_data", custObject);
        return result;
    }

    //发送具体同步OA部门请求
    public static String syncEachDeptData(List data, ArrayList xybw,ArrayList errResInfo) {
        if (xybw == null) {
            xybw = new ArrayList();
        }
        if(errResInfo == null){
            errResInfo = new ArrayList();
        }
        String errorMsg = "";
        for (int j = 0; j < data.size(); j++) {
            List<Map<String, Object>> deptData = (List<Map<String, Object>>) data.get(j);
            Map<String, Map<String, String>> restResult;
            List<Map<String, Object>> batchData;
            try {

                int batchSize = 1000; // 每批次处理的最大数量
                int totalSize = deptData.size();
                for (int i = 0; i < totalSize; i += batchSize) {
                    int endIndex = Math.min(i + batchSize, totalSize);
                    batchData = deptData.subList(i, endIndex);
                    // 1、执行同步
                    restResult = manager.synDepartment("code", batchData);
                    xybw.add(restResult);
                    if (restResult.toString().contains("失败")) {
                        errorMsg =  "失败";
                        errResInfo.add(restResult);
                    }

                }
            } catch (Exception e) {
                errorMsg = "syncEachDeptData异常：" + SDUtil.getExceptionDetail(e);
                errResInfo.add(errorMsg);
            }
        }
        return errorMsg;
    }
    // 截取满足 MySQL TEXT 类型最大长度限制的文本
    public static String truncateText(String text) {
        if(StringUtils.isNotBlank(text)){
            if (text.length() <= 1000) {
                return text;
            } else {
                return text.substring(0, 1000);
            }
        }
        return text;
    }
    public static int countOccurrences(String str, String target) {
        int count = 0;
        int index = 0;
        while ((index = str.indexOf(target, index)) != -1) {
            count++;
            index += target.length();
        }
        return count;
    }


}
