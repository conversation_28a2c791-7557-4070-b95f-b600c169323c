package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleInsertBean;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import weaver.general.BaseBean;
import weaver.general.Util;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @FileName SFSyncLogUtil
 * @Description
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/3/6
 */
public class SFSyncLogUtil {

    private static final BaseBean bb = new BaseBean();
    private static final Logger logger = LoggerFactory.getLogger(SyncClassDept.class);

    /**
     * 将数据日志插入
     *
     * @param log
     * @return
     */
    public static String insertLog(SFSyncLog log, List<SFSyncDetailLog> detailList) {
        String errorMsg = "";
        String fieldName;
        Object fieldValue;
        bb.writeLog("SFSyncLogUtil insertData ---START");

        try {
            Class<?> clazz = SFSyncLog.class;
            Field[] fields = clazz.getDeclaredFields();
            List<String> insertFields = new ArrayList<>();
            List<Object> value = new ArrayList<>();
            for (Field field : fields) {
                field.setAccessible(true); // 设置可访问私有字段
                //字段名
                fieldName = field.getName();
                //获取字段值
                fieldValue = field.get(log);
                insertFields.add(fieldName);
                value.add(fieldValue);
            }
            int moduleId = ModuleDataUtil.getModuleIdByName("uf_zzjglog");
            ModuleInsertBean mb = new ModuleInsertBean();
            mb.setTableName("uf_zzjglog")
                    .setFields(insertFields)
                    .setValue(value)
                    .setCreatorId(1)
                    .setModuleId(moduleId);
            ModuleResult mrMain = ModuleDataUtil.insertOne(mb);
            if (mrMain.isSuccess()) {

                errorMsg = insertDetailLog(detailList, mrMain.getBillid());

                //插入明细表
            } else {
                errorMsg = mrMain.getErroMsg();
            }
        } catch (IllegalAccessException e) {
            bb.writeLog("SFSyncLogUtil insertData error :" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("SFSyncLogUtil insertData ---END");
        return errorMsg;
    }

    /**
     * 记录明细日志
     *
     * @param
     * @param mainLogId
     * @return
     */
    public static String insertDetailLog(List<SFSyncDetailLog> detailList, int mainLogId) {
        bb.writeLog("SFSyncDetailLog insertDetailLog ---START");
        String errorMsg;
        String fieldName;
        Object fieldValue;
        try {
            Class<?> clazz = SFSyncDetailLog.class;
            Field[] fields = clazz.getDeclaredFields();
            List<Field> list = Arrays.asList(fields);
            List<String> insertFields = new ArrayList<>();
            List<List<Object>> values = new ArrayList<>();

            for (Field field : list) {
                field.setAccessible(true); // 设置可访问私有字段
                fieldName = field.getName();
                insertFields.add(fieldName);
            }
            insertFields.add("mainid");

            for (int i = 0; i < detailList.size(); i++) {
                List<Object> value = new ArrayList<>();
                SFSyncDetailLog log = detailList.get(i);
                for (Field field : list) {
                    field.setAccessible(true); // 设置可访问私有字段
                    fieldValue = field.get(log);
                    value.add(fieldValue);
                }
                value.add(mainLogId);
                values.add(value);
            }

            ModuleInsertBean mb = new ModuleInsertBean();
            mb.setTableName("uf_zzjglog_dt1")
                    .setFields(insertFields)
                    .setValues(values);
            ModuleResult mr = ModuleDataUtil.insertDetailAc(mb);
            errorMsg = mr.getErroMsg();
        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("SFSyncDetailLog insertDetailLog error :" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("SFSyncDetailLog insertDetailLog ---END");
        return errorMsg;
    }
}
