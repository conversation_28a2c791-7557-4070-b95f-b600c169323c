package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.weaver.general.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;

import java.util.*;

/**
 * @FileName SyncStudentHrm
 * @Description
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/3/6
 */
public class SyncStudentHrm {
    private static final Logger log = LoggerFactory.getLogger(SyncStudentHrm.class);
    private RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
    /**
     * HRM restful 同步类
     */
    private static HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();

    //同步学生信息
    public void syncStudentHrmInfo(String token, JSONObject tc, String subcompanycode, String uuid) {

        List<SFSyncDetailLog> syncDetailList = new ArrayList<>();
        SFSyncLog sfSyncLog = new SFSyncLog();
        ArrayList<Object> errMsgList = new ArrayList<>();
        int tbjg = 0;
        //获取开始时间
        long startTime = System.currentTimeMillis();
        //同步批次号
        sfSyncLog.setPch(uuid);
        sfSyncLog.setBz("查看日志请在当前服务器地址后面拼接/log/sd/sd.log，例如：http://127.0.0.1:8080/log/sd/sd.log ，将页面保存至本地，按照请求/响应报文中的UUID即可搜索查看");
        //同步类型
        sfSyncLog.setTblx(3);
        //同步日期
        sfSyncLog.setTbrq(TimeUtil.getCurrentDateString());
        //同步开始时间
        sfSyncLog.setTbkssj(TimeUtil.getCurrentTimeString());
        SFSyncDetailLog sfSyncDetailLogZt = new SFSyncDetailLog();
        SFSyncDetailLog sfSyncDetailLogOA = new SFSyncDetailLog();
        syncDetailList.add(sfSyncDetailLogZt);
        syncDetailList.add(sfSyncDetailLogOA);
        try {
            if (StringUtils.isNotBlank(tc.getString("id"))) {
                sfSyncLog.setZpz(Integer.parseInt(tc.getString("id")));
            }
//            JSONArray OADeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartmentdefined");
            JSONObject sm = Utils.getMainBaseInfo("select * from uf_xsrytb");
            String jkzlzd = sm.getString("jkzlzd");
            String oazlzd = sm.getString("oazlzd");
            String oazlzdwz = sm.getString("oazlzdwz");
            String jkbjdmzd = sm.getString("jkbjdmzd");
            String oabjdmzd = sm.getString("oabjdmzd");
            //判断OA增量字段的位置再那张表中
            String formName = "";
            String sqlWhere = "";
            if ("0".equals(oazlzdwz)) {
                formName = "HrmResource";
            } else if ("1".equals(oazlzdwz)) {
                formName = " cus_fielddata ";
                sqlWhere = " where scope = 'HrmCustomFieldByInfoType' and scopeid = -1";
            } else if ("2".equals(oazlzdwz)) {
                formName = " cus_fielddata ";
                sqlWhere = "where scope = 'HrmCustomFieldByInfoType' and scopeid = 1 ";
            } else {
                formName = " cus_fielddata ";
                sqlWhere = "where scope = 'HrmCustomFieldByInfoType' and scopeid = 3 ";
            }

            JSONArray filerArray = new JSONArray();
            JSONObject filerObject = new JSONObject();
            filerObject.put("name", "SFZJ");
            filerObject.put("operator", "NE");
            filerObject.put("value", "0");
            filerArray.add(filerObject);
//            JSONArray oldstudentHrmInfo = Utils.getThirdHrmInfoWithPage(errMsgList, token, "uf_xsrytb", sfSyncDetailLogZt);
//            log.info("原始数据oldstudentHrmInfo："+JSONObject.toJSONString(oldstudentHrmInfo));
//            JSONArray studentHrmInfo = Utils.getThirdHrmInfoWithPage2(errMsgList, token, "uf_xsrytb", sfSyncDetailLogZt, filerArray);
            JSONArray studentHrmInfo = Utils.getThirdHrmInfoWithPage(errMsgList, token, "uf_xsrytb", sfSyncDetailLogZt);
            log.info("中台接口返回的学生数据："+JSONObject.toJSONString(studentHrmInfo));
            HashMap<String, String> oaDeptMap = new HashMap<>();
            HashMap<String, String> oaHrmMap = new HashMap<>();
            HashMap<String, String> oaCustHrmMap = new HashMap<>();
            if (recordSet.executeQuery("select * from HrmResource where SUBCOMPANYID1 = " + Util.null2String(tc.getString("djbmszfb")))) {
                while (recordSet.next()) {
                    // key 代表人员WORKCODE
                    // value 代表人员id
                    oaHrmMap.put(recordSet.getString("WORKCODE"), recordSet.getString("ID"));
                }
            }
            if (recordSet.executeQuery("select * from " + formName + sqlWhere)) {
                while (recordSet.next()) {
                    // key 代表人员id
                    // value 代表人员同步的增量字段值
                    oaCustHrmMap.put(recordSet.getString("ID"), recordSet.getString(oazlzd));
                }
            }
            if (recordSet.executeQuery("select * from hrmdepartmentdefined")) {
                while (recordSet.next()) {
                    // key 代表班级代码
                    // value 代表班级系统id
                    oaDeptMap.put(recordSet.getString("bjdm"), recordSet.getString("xtid"));
                }
            }


            JSONObject fbcode = new JSONObject();
            fbcode.put("subcompanycode", subcompanycode);
            String fb = "{JSON}" + JSONObject.toJSONString(fbcode);
            JSONObject gwCode = new JSONObject();
            gwCode.put("jobtitlecode", "XS");
            String gw = "{JSON}" + JSONObject.toJSONString(gwCode);
            String jobgroupid = tc.getString("ryzwlb");
            String jobactivityid = tc.getString("ryzw");
            ArrayList<Object> errList = new ArrayList<>();
            List<Map<String, Object>> hrmData = new ArrayList<>();
            for (int i = 0; i < studentHrmInfo.size(); i++) {
                JSONObject student = studentHrmInfo.getJSONObject(i);
                if (StringUtils.isNotBlank(sqlWhere)) {
                    //增量字段存于人员自定义字段当中
                    if (oaHrmMap.containsKey(Util.null2String(student.getString("XH")))) {
                        //oa系统中的增量字段值
                        String hrmId = oaHrmMap.get(Util.null2String(student.getString("XH")));
                        String oaIncrementalVal = oaCustHrmMap.get(hrmId);
                        if (StringUtils.isNotBlank(oaIncrementalVal) && oaIncrementalVal.equals(Util.null2String(student.getString(jkzlzd)))) {
                            studentHrmInfo.remove(i);
                            i--;
                        } else {
                            String jkbjdmzdValue = student.getString(jkbjdmzd);
                            if (StringUtils.isNotBlank(jkbjdmzdValue)) {
                                if (oaDeptMap.containsKey(jkbjdmzdValue)) {
                                    Map<String, Object> hrm = new HashMap<>();
                                    String xtid = Util.null2String(oaDeptMap.get(jkbjdmzdValue));
                                    //将该教职员工设置在该部门下
                                    JSONObject departmentcode = new JSONObject();
                                    departmentcode.put("departmentcode", xtid);
                                    String bm = "{JSON}" + JSONObject.toJSONString(departmentcode);
                                    hrm.put("subcompany", fb);
                                    hrm.put("department", bm);
                                    //拼装请求参数 AssReqParams
                                    assReqParams(hrm, student);
                                    hrm.put("jobtitle", gw);
                                    hrm.put("jobgroupid", jobgroupid);
                                    hrm.put("jobactivityid", jobactivityid);
                                    hrmData.add(hrm);
                                }
                            } else {
                                errList.add(student);
                            }
                        }
                    } else {
                        String jkbjdmzdValue = student.getString(jkbjdmzd);
                        if (StringUtils.isNotBlank(jkbjdmzdValue)) {
                            if (oaDeptMap.containsKey(jkbjdmzdValue)) {
                                Map<String, Object> hrm = new HashMap<>();
                                String xtid = Util.null2String(oaDeptMap.get(jkbjdmzdValue));
                                //将该教职员工设置在该部门下
                                JSONObject departmentcode = new JSONObject();
                                departmentcode.put("departmentcode", xtid);
                                String bm = "{JSON}" + JSONObject.toJSONString(departmentcode);
                                hrm.put("subcompany", fb);
                                hrm.put("department", bm);
                                //拼装请求参数 AssReqParams
                                assReqParams(hrm, student);
                                hrm.put("jobtitle", gw);
                                hrm.put("jobgroupid", jobgroupid);
                                hrm.put("jobactivityid", jobactivityid);
                                hrmData.add(hrm);
                            }
                        } else {
                            errList.add(student);
                        }
                    }
                }
            }
            log.info("workcode为空的学生信息" + JSONObject.toJSONString(errList));
            log.info("同步学生请求数据" + JSONObject.toJSONString(hrmData));
            String resMsg = syncHrmData(hrmData, errMsgList, sfSyncDetailLogOA);
            //同步信息
            JSONObject tBStudentInfo = new JSONObject();
            tBStudentInfo.put("--本次同步的数量--", hrmData.size());
            int errCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList), "失败");
            int expCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList), "syncHrmData异常");
            tBStudentInfo.put("--本次同步失败的数量--", errCount + expCount);
            String tbxxUUID = UUID.randomUUID().toString();
            tBStudentInfo.put("--本次同步请求参数信息--", tbxxUUID);
            sfSyncLog.setTbxx(JSONObject.toJSONString(tBStudentInfo));
            log.info("同步学生信息：" + tbxxUUID + "同步信息:" + JSONObject.toJSONString(hrmData));
        } catch (Exception e) {
            tbjg = 1;
        }
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫转换为秒
        //同步结束时间
        sfSyncLog.setTbjssj(TimeUtil.getCurrentTimeString());
        //耗时（秒）
        sfSyncLog.setTbhs(String.valueOf(elapsedSeconds));
        //同步结果
        if (tbjg == 0 && sfSyncDetailLogZt.getJkjg() == 0 && sfSyncDetailLogOA.getJkjg() == 0) {
            sfSyncLog.setTbjg(0);
        } else {
            sfSyncLog.setTbjg(1);
        }
        //同步失败信息
        String tbsbUUID = UUID.randomUUID().toString();
        sfSyncLog.setSbxx(JSONObject.toJSONString(errMsgList));
        log.info("同步学生失败信息：" + tbsbUUID + "失败信息：" + JSONObject.toJSONString(errMsgList));
        SFSyncLogUtil.insertLog(sfSyncLog, syncDetailList);
    }

    //拼装请求参数 AssReqParams
    private void assReqParams(Map<String, Object> hrm, JSONObject studentObject) {
        JSONObject base_custom_data = new JSONObject();
        JSONObject person_custom_data = new JSONObject();
        JSONObject work_custom_data = new JSONObject();
        //获取教师明细表配置
        JSONArray studentFormDetails = Utils.getDetailBaseInfo("select * from uf_xsrytb_dt1");
        for (int i = 0; i < studentFormDetails.size(); i++) {
            JSONObject jo = (JSONObject) studentFormDetails.get(i);
            String oazdwz = Util.null2String(jo.getString("oazdwz"));
            String oajkzd = Util.null2String(jo.getString("oajkzd"));

            //存放的位置 1.2.3自定义字段
            switch (oazdwz) {
                case "1":
                    //base_custom_data
                    Utils.custFields(jo, studentObject, base_custom_data);
                    break;
                case "2":
                    //person_custom_data
                    Utils.custFields(jo, studentObject, person_custom_data);
                    break;
                case "3":
                    //work_custom_data
                    Utils.custFields(jo, studentObject, work_custom_data);
                    break;
                default:
                    String val = Utils.UtilParams(jo, studentObject);
                    if (StringUtils.isNotBlank(val)) {
                        hrm.put(oajkzd, val);
                    }
            }
        }
        hrm.put("base_custom_data", base_custom_data);
        hrm.put("person_custom_data", person_custom_data);
        hrm.put("work_custom_data", work_custom_data);
    }

    /**
     * 调用标准rest接口方法，同步人员
     *
     * @param hrmData
     */
    @SuppressWarnings("unchecked")
    private String syncHrmData(List<Map<String, Object>> hrmData, ArrayList errList, SFSyncDetailLog sfSyncDetailLogOA) {
        if (sfSyncDetailLogOA == null) {
            sfSyncDetailLogOA = new SFSyncDetailLog();
        }
        long startTime = System.currentTimeMillis(); // 获取结束时间
        //请求时间
        sfSyncDetailLogOA.setQqsj(TimeUtil.getCurrentTimeString());
        //接口名
        sfSyncDetailLogOA.setJkm("OA同步学生人员");
        //请求报文
        String qquuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setQqbw(Util.null2String(qquuid));
        if (hrmData != null) {
            log.info("OA同步学生请求报文信息: " + qquuid + " 请求报文信息:" + JSONObject.toJSONString(hrmData));
        } else {
            log.info("OA同步学生请求报文信息: " + qquuid + " 请求报文信息为空");
        }

        if (errList == null) {
            errList = new ArrayList();
        }
        Map<String, Map<String, String>> restResult;
        List<Map<String, Object>> batchData;
        String errorMsg = "";
        ArrayList<Object> xybw = new ArrayList<>();
        try {
            int batchSize = 1000; // 每批次处理的最大数量
            int totalSize = hrmData.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                batchData = hrmData.subList(i, endIndex);
                // 1、执行同步
                restResult = manager.synHrmresource("workcode", batchData);
                xybw.add(restResult);
                if (restResult.toString().contains("失败")) {
                    errorMsg = "失败";
                    errList.add(JSONObject.toJSONString(restResult));
                }
            }
        } catch (Exception e) {
            errorMsg = "syncHrmData异常：" + SDUtil.getExceptionDetail(e);
            errList.add(errorMsg);
            log.info("syncHrmData异常：" + SDUtil.getExceptionDetail(e));
        }

        //响应报文
        String xxuuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setXybw(Util.null2String(xxuuid));
        log.info("OA同步学生响应报文信息: " + xxuuid + " 响应报文信息:" + JSONObject.toJSONString(xybw));
        //响应时间
        sfSyncDetailLogOA.setXysj(TimeUtil.getCurrentTimeString());
        //接口耗时
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
        sfSyncDetailLogOA.setJkhs(Util.null2String(String.valueOf(elapsedSeconds)));
        //接口结果
        if (StringUtils.isBlank(errorMsg)) {
            sfSyncDetailLogOA.setJkjg(0);
        } else {
            sfSyncDetailLogOA.setJkjg(1);
        }
        return errorMsg;
    }
}
