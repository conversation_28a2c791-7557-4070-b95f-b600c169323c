package com.engine.shmhxy.tpw.module.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.shmhxy.tpw.module.service.AttendanceSummaryService;
import com.engine.shmhxy.tpw.module.service.impl.AttendanceSummaryServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;
public class AttendanceSummaryWeb {
    private AttendanceSummaryService getService(User user) {
        return ServiceUtil.getService(AttendanceSummaryServiceImpl.class, user);
    }

    /**
     *
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/attendanceSummary")
    @Produces(MediaType.TEXT_PLAIN)
    public String attendanceSummary(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).attendanceSummary(params, user)
        );
    }
}