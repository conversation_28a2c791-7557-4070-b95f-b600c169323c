package com.engine.shmhxy.tpw.module.service.impl;

import com.engine.shmhxy.tpw.module.cmd.AttendanceSummaryCmd;
import com.engine.shmhxy.tpw.module.service.AttendanceSummaryService;

import com.engine.core.impl.Service;
import weaver.hrm.User;

import java.util.Collections;
import java.util.Map;

public class AttendanceSummaryServiceImpl extends Service implements AttendanceSummaryService {

    @Override
    public Map<String, Object> attendanceSummary(Map<String, Object> params, User user) {
        return commandExecutor.execute(new AttendanceSummaryCmd(params, user));
    }
}
