package com.engine.shmhxy.tpw.module.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.shmhxy.tpw.module.bean.AttendanceSummary;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.general.TimeUtil;
import weaver.hrm.User;

import java.util.*;

public class AttendanceSummaryCmd extends AbstractCommonCommand<Map<String, Object>> {
    //    private final Logger log = LoggerFactory.getLogger(this.getClass());
    private RecordSet rs = new RecordSet();

    private BaseBean bb = new BaseBean();

    public AttendanceSummaryCmd(Map<String, Object> params, User user) {
        this.user = user;
        this.params = params;
    }

    @Override
    public BizLogContext getLogContext() {
        return null;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        bb.writeLog(this.getClass().getName() + "---START");
        Map<String, Object> result = new HashMap<>();
        String errorMsg = "";
        try {

            bb.writeLog("AttendanceSummaryCmd的请求数据=====>" + JSONObject.toJSONString(this.params));
            if (params != null && !params.isEmpty()) {
                String tableName = String.valueOf(params.get("tableName"));
                String startDate = String.valueOf(params.get("startDate"));
                String endDate = String.valueOf(params.get("endDate"));
                String status = String.valueOf(params.get("status"));
                String flagTable = String.valueOf(params.get("flagTable"));
//                String id = String.valueOf(params.get("id"));
                String xm = String.valueOf(params.get("xm"));
                String gh = String.valueOf(params.get("gh"));
                String bm = String.valueOf(params.get("bm"));


                if (StringUtils.isNotBlank(tableName)) {
                    rs.executeUpdate("TRUNCATE TABLE  " + tableName);
                }

                //默认查询所有教师的所有的考勤数据
                String paramsV1DateSql = "";
                String paramsV2DateSql = "";
                String paramsSql = "";
                if (StringUtils.isNotBlank(xm)) {
                    paramsSql = " and hr.lastname = '" + xm + "' ";
                }
                if (StringUtils.isNotBlank(gh)) {
                    paramsSql = paramsSql + " and hr.workcode = '" + gh + "' ";
                }
                if (StringUtils.isNotBlank(bm)) {
//                    paramsSql = paramsSql + " and hr.departmentid = '" + bm + "' ";
                    paramsSql = paramsSql + " and hr.departmentid in (" + bm + ") ";
                }
                if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
                    paramsV1DateSql = " and v1.rq >= '" + startDate + "' and v1.rq <= '" + endDate + "' ";
                    paramsV2DateSql = " and v2.rq >= '" + startDate + "' and v2.rq <= '" + endDate + "' ";
                }

                String sql = "select   " +
                        "    hr.id as id,   " +
                        "    hr.lastname as xm,   " +
                        "    hr.workcode as gh,   " +
                        "    hr.departmentid as bm,   " +
                        "    v1.sjcqts as sjcqts,   " +
                        "    v2.zccqts as zccqts " +
//                        "    v1.sjcqts - v2.zccqts as yccqts " +
                        "from HrmResource hr  " +
                        "left join   " +
                        "( " +
                        "    select   " +
                        "        v1.workcode,  " +
                        "        count(*) as sjcqts  " +
                        "    from V_jskqrz v1  " +
                        "    where 1=1 and  v1.sbdkdz <> 0 " + paramsV1DateSql +
                        "    group by v1.workcode  " +
                        ") v1 on v1.workcode = hr.workcode  " +
                        "left join " +
                        "( " +
                        "  select " +
                        "    v2.workcode, " +
                        "    count(*) as zccqts " +
                        "  from V_jskqrz v2 " +
                        "    where 1=1 and v2.sbcqzk = 0  and v2.xbcqzk = 0 " + paramsV2DateSql +
                        "    group by v2.workcode " +
                        ") v2 on v2.workcode = hr.workcode " +
                        "  where 1=1 " +
                        "  and hr.jobtitle not in (" + status + ") " + paramsSql;
                ArrayList<AttendanceSummary> attendanceSummarys = new ArrayList<>();
                bb.writeLog("sql语句：" + sql);
                rs.executeQuery(sql);
                while (rs.next()) {
                    AttendanceSummary attendanceSummary = new AttendanceSummary();
                    attendanceSummary.setRyid(rs.getString("id"));
                    attendanceSummary.setXm(rs.getString("xm"));
                    attendanceSummary.setBm(rs.getString("bm"));
                    attendanceSummary.setGh(rs.getString("gh"));
                    String sjcqts = rs.getString("sjcqts");
                    String zccqts = rs.getString("zccqts");
                    if (StringUtils.isNotBlank(sjcqts)) {
                        attendanceSummary.setSjcqts(sjcqts);
                    } else {
                        attendanceSummary.setSjcqts("0");
                    }

                    if (StringUtils.isNotBlank(zccqts)) {
                        attendanceSummary.setZccqts(zccqts);
                    } else {
                        attendanceSummary.setZccqts("0");
                    }

                    if (StringUtils.isNotBlank(sjcqts) && StringUtils.isNotBlank(zccqts)) {
                        attendanceSummary.setYccqts(String.valueOf(Integer.parseInt(sjcqts) - Integer.parseInt(zccqts)));
                    } else if (StringUtils.isNotBlank(sjcqts) && StringUtils.isBlank(zccqts)) {
                        attendanceSummary.setYccqts(sjcqts);
                    } else if (StringUtils.isBlank(sjcqts)) {
                        attendanceSummary.setYccqts("0");
                    }

                    if (StringUtils.isNotBlank(startDate)) {
                        attendanceSummary.setKsrq(startDate);
                    } else {
                        attendanceSummary.setKsrq("2000-01-01");
                    }
                    if (StringUtils.isNotBlank(endDate)) {
                        attendanceSummary.setJsrq(endDate);
                    } else {
                        attendanceSummary.setJsrq(TimeUtil.getCurrentDateString());
                    }
                    attendanceSummarys.add(attendanceSummary);
                }
                if (!attendanceSummarys.isEmpty()) {
                    int moduleId = ModuleDataUtil.getModuleIdByName(tableName);
                    ModuleDataUtil.insertObjList(attendanceSummarys, tableName, moduleId, 1);
                }
                if (StringUtils.isNotBlank(flagTable)) {
                    String uuid = updateQueryKey(user.getUID(), flagTable);
                    result.put("querykey", uuid);
                }
            } else {
                errorMsg = "params请求参数为空";
            }

        } catch (Exception e) {
            errorMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("AttendanceSummaryCmd异常：" + errorMsg);
        }
        result.put("errorMsg", errorMsg);
        result.put("status", errorMsg.isEmpty());
        bb.writeLog("result:" + result);
        bb.writeLog(this.getClass().getName() + "---END");
        return result;
    }


    private String updateQueryKey(int userId, String flagTable) {
        String uuid = String.valueOf(UUID.randomUUID());
        RecordSet rs = DBUtil.getThreadLocalRecordSet();
        if (!rs.executeUpdate("update " + flagTable + " set cxuid = '" + uuid + "',cxr=" + userId + ",cxsj='" + TimeUtil.getCurrentTimeString() + "'")) {
            bb.writeLog("updateQueryKey失败:" + rs.getExceptionMsg());
        }
        return uuid;
    }
}