package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
    * @FileName SFSyncLog
    * @Description 
    * <AUTHOR>
    * @Version v1.00
    * @Date 2024/3/6
    */
@Data
public class SFSyncLog {
    /**
     * 批次号
     */
    private String pch;
    /**
     * 总配置
     */
    private int zpz;
    /**
     * 同步类型
     */
    private int tblx;
    /**
     * 同步日期
     */
    private String tbrq;
    /**
     * 同步开始时间
     */
    private String tbkssj;
    /**
     * 同步结束时间
     */
    private String tbjssj;
    /**
     * 同步耗时(秒)
     */
    private String tbhs;
    /**
     * 同步结果
     */
    private int tbjg;
    /**
     * 同步信息
     */
    private String tbxx;
    /**
     * 失败信息
     */
    private String sbxx;
    /**
     * 备注
     */
    private String bz;
}
