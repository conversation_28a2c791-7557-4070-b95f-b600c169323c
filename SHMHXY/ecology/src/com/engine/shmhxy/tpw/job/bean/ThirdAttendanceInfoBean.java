package com.engine.shmhxy.tpw.job.bean;

import lombok.Data;

/**
 * @FileName ThirdAttendanceInfoBean
 * @Description 从第三方获取的原始考勤记录
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/28
 */
@Data
public class ThirdAttendanceInfoBean {
    //打卡时间
    private String checkinTime;
    //打卡wifi名称
    private String wifiName;
    //打卡备注
    private String notes;
    //位置打卡地点经度
    private String lng;
    //打卡地点详情
    private String locationDetail;
    //规则id
    private String groupId;
    //用户id
    private String userid;
    //打卡规则名称
    private String groupName;
    //打卡设备id
    private String deviceId;
    //打卡类型
    private String checkinType;
    //异常类型
    private String exceptionType;
    //打卡地点title
    private String locationTitle;
    //打卡的MAC地址/bssid
    private String wifiMac;
    //打卡的附件media_id
    private String mediaIds;
    //位置打卡地点纬度
    private String lat;
    //标准打卡时间
    private String schCheckinTime;
    //操作标志
    private String cityOsOp;
    //入库时间
    private String cityOsLandingTs;
}
