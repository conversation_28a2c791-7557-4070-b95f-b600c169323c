package com.engine.shmhxy.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @FileName SyncESBInsertModuleJob
 * @Description 通过前端配置实现ESB接口组合多次调用完成大于1000行的建模数据写入
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/4/10
 */
@Getter
@Setter
public class SyncESBInsertModuleJob extends BaseCronJob {
    private static final BaseBean bb = new BaseBean();
    private RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        bb.writeLog(this.getClass().getName() + "---START");

        try {
            //查询建模表单中的数据,筛选出启用状态的ESB组合
            ArrayList<JSONObject> esbOpenList = queryEsbOpenList();
            //开启异步执行
            if (esbOpenList.isEmpty()) {
                //这里是建模表单里面没有要要执行的esb组合事件
                bb.writeLog(this.getClass().getName() + "没有要要执行的esb组合事件");
            } else {
                ExecutorService executorService = Executors.newFixedThreadPool(5);
                CountDownLatch latch = new CountDownLatch(esbOpenList.size());
                bb.writeLog("当前需要执行的事件组合数据" + JSONObject.toJSONString(esbOpenList));
                for (JSONObject esbObject : esbOpenList) {
                    executorService.submit(() -> {
                        try {
                            //step 6:处理每一组数据
                            String process = process(esbObject);
                            if (StringUtils.isNotBlank(process)) {
                                bb.writeLog("事件组合调用失败，事件组合信息：" + process);
                            }
                        } finally {
                            latch.countDown(); // 线程完成任务后减少计数器
                        }
                    });
                }
            }
        } catch (Exception e) {
            bb.writeLog("实现ESB接口组合多次调用异常：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog(this.getClass().getName() + "---END");
    }

    /**
     * @return
     * @FileName SyncESBInsertModuleJob
     * @Description 获取状态为启用的ESB组合事件集合
     * <AUTHOR>
     * @Version v1.00
     * @Date 2024/4/11
     */
    private ArrayList<JSONObject> queryEsbOpenList() {
        ArrayList<JSONObject> esbOpenList = new ArrayList<>();

        JSONObject jsonObject = null;
        String sql = "select * from uf_sjzdy where sfkq = 0";
        rs.execute(sql);
        while (rs.next()) {
            jsonObject = new JSONObject();
            String getDataEvent = rs.getString("hqsjsjm");
            String insertDataEvent = rs.getString("crjmsjm");
            int queryCount = rs.getInt("dccrsl");
            jsonObject.put("getDataEvent", getDataEvent);
            jsonObject.put("insertDataEvent", insertDataEvent);
            jsonObject.put("queryCount", queryCount);
            esbOpenList.add(jsonObject);
        }
        return esbOpenList;
    }

    /**
     * @FileName SyncESBInsertModuleJob
     * @Description 执行ESB事件
     * <AUTHOR>
     * @Version v1.00
     * @Date 2024/4/10
     */
    private String process(JSONObject esbObject) {
        bb.writeLog(this.getClass().getName() + "执行ESB事件组合开始--START");
        String errMsg = "";
        try {
            String getDataEventName = (String) esbObject.get("getDataEvent");
            String insertDataEventName = (String) esbObject.get("insertDataEvent");
            Integer queryCount = esbObject.getInteger("queryCount");
            EsbEventResult er = EsbUtil.callEsbEvent(getDataEventName, "");
            if (er.isSuccess()) {
                JSONObject data = er.getData();
                JSONArray rows = (JSONArray) data.get("rows");
                bb.writeLog(getDataEventName + "事件查询到的数据" + JSONObject.toJSONString(rows));
                // 获取rows的长度
                int totalRows = rows.size();
                // 定义每次截取的大小
                int batchSize = queryCount;
                // 开始截取数据
                JSONObject reqInfo = null;
                int count = 0;
                for (int i = 0; i < totalRows; i += batchSize) {
                    if (i % 10000 == 0 && i > 0) {
                        Thread.sleep(30000);
                    }
                    int endIndex = Math.min(i + batchSize, totalRows); // 确定本次截取的结束索引
                    JSONArray subArray = new JSONArray(rows.subList(i, endIndex)); // 截取数据并转换为JSONArray
                    reqInfo = new JSONObject();
                    reqInfo.put("rows", subArray);
                    //发送插入建模中的请求
                    EsbEventResult insertResulr = EsbUtil.callEsbEvent(insertDataEventName, JSONObject.toJSONString(reqInfo));
                    if (!insertResulr.isSuccess()) {
                        errMsg = er.getErroMsg();
                    }
                    count++;
                }
                bb.writeLog("事件组合" + JSONObject.toJSONString(esbObject) + "共执行了" + count + "次");
            } else {
                errMsg = er.getErroMsg();
            }
            bb.writeLog(this.getClass().getName() + "执行ESB事件组合开始--END");
        } catch (Exception e) {
            errMsg = SDUtil.getExceptionDetail(e);
            bb.writeLog("执行ESB事件组合异常：" + SDUtil.getExceptionDetail(e));
        }
        return errMsg;
    }
}
