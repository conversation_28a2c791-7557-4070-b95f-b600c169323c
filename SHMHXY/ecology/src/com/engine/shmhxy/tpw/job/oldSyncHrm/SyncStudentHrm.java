package com.engine.shmhxy.tpw.job.oldSyncHrm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.weaver.general.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.general.TimeUtil;

import java.util.*;

/**
 * @FileName SyncStudentHrm
 * @Description
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/3/6
 */
public class SyncStudentHrm {
    private static final Logger log = LoggerFactory.getLogger(SyncStudentHrm.class);
    /**
     * HRM restful 同步类
     */
    private static HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();

    //同步学生信息
    public void syncStudentHrmInfo(String token, JSONObject tc, String subcompanycode, String uuid) {
        List<SFSyncDetailLog> syncDetailList = new ArrayList<>();
        SFSyncLog sfSyncLog = new SFSyncLog();
        ArrayList<Object> errMsgList = new ArrayList<>();
        int tbjg = 0;
        //获取开始时间
        long startTime = System.currentTimeMillis();
        //同步批次号
        sfSyncLog.setPch(uuid);
        sfSyncLog.setBz("查看日志请在当前服务器地址后面拼接/log/sd/sd.log，例如：http://**************:8080/log/sd/sd.log ，将页面保存至本地，按照请求/响应报文中的UUID即可搜索查看");
        //同步类型
        sfSyncLog.setTblx(3);
        //同步日期
        sfSyncLog.setTbrq(TimeUtil.getCurrentDateString());
        //同步开始时间
        sfSyncLog.setTbkssj(TimeUtil.getCurrentTimeString());
        SFSyncDetailLog sfSyncDetailLogZt = new SFSyncDetailLog();
        SFSyncDetailLog sfSyncDetailLogOA = new SFSyncDetailLog();
        syncDetailList.add(sfSyncDetailLogZt);
        syncDetailList.add(sfSyncDetailLogOA);
        try {
            if (StringUtils.isNotBlank(tc.getString("id"))) {
                sfSyncLog.setZpz(Integer.parseInt(tc.getString("id")));
            }
            JSONArray OADeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartmentdefined");
            JSONObject sm = Utils.getMainBaseInfo("select * from uf_xsrytb");
            String jkzlzd = sm.getString("jkzlzd");
            String oazlzd = sm.getString("oazlzd");
            String oazlzdwz = sm.getString("oazlzdwz");
            String jkbjdmzd = sm.getString("jkbjdmzd");
            String oabjdmzd = sm.getString("oabjdmzd");
            //判断OA增量字段的位置再那张表中
            String FormName = "";
            String sqlWhere = "";
            if ("0".equals(oazlzdwz)) {
                FormName = "HrmResource";
            } else if ("1".equals(oazlzdwz)) {
                FormName = " cus_fielddata ";
                sqlWhere = " where scope = 'HrmCustomFieldByInfoType' and scopeid = -1";
            } else if ("2".equals(oazlzdwz)) {
                FormName = " cus_fielddata ";
                sqlWhere = "where scope = 'HrmCustomFieldByInfoType' and scopeid = 1 ";
            } else {
                FormName = " cus_fielddata ";
                sqlWhere = "where scope = 'HrmCustomFieldByInfoType' and scopeid = 3 ";
            }
            JSONArray hrmResourceArray = Utils.getDetailBaseInfo("select * from HrmResource where SUBCOMPANYID1 = "+Util.null2String(tc.getString("djbmszfb")));
            JSONArray hrmResourceCustArray = Utils.getDetailBaseInfo("select * from " + FormName + sqlWhere);
            List<Map<String, Object>> hrmData = new ArrayList<>();
            //获取OA所有的学生人员信息
            //【教职工】只需同步“状态”为“01”的在职人员
            JSONArray filerArray = new JSONArray();
            JSONObject filerObject = new JSONObject();
            filerObject.put("name","SFZJ");
            filerObject.put("operator","EQ");
            filerObject.put("value","1");
            filerArray.add(filerObject);
            JSONArray studentHrmInfo = Utils.getThirdHrmInfoWithPage(errMsgList,token, "uf_xsrytb", sfSyncDetailLogZt);
            // todo
//            JSONArray studentHrmInfo = Utils.getThirdHrmInfoWithPage2(errMsgList,token, "uf_xsrytb", sfSyncDetailLogZt,filerArray);
            //根据增量字段筛选要同步的学生数据
            for (int i = 0; i < studentHrmInfo.size(); i++) {
                JSONObject studentObject = studentHrmInfo.getJSONObject(i);
                for (int j = 0; j < hrmResourceArray.size(); j++) {
                    JSONObject hr = (JSONObject) hrmResourceArray.get(j);
                    boolean sameHrmflag = false;
                    if (StringUtils.isNotBlank(sqlWhere)) {
                        //增量字段在自定表当中
                        if (hr.getString("WORKCODE").equals(studentObject.getString("XH"))) {
                            for (int k = 0; k < hrmResourceCustArray.size(); k++) {
                                JSONObject jo = (JSONObject) hrmResourceCustArray.get(k);
                                if (jo.get("ID").equals(hr.getString("ID")) && studentObject.getString(jkzlzd).equals(jo.getString(oazlzd))) {
                                    //移除第三方不同步的人员信息
                                    studentHrmInfo.remove(i);
                                    i--;
                                    sameHrmflag = true;
                                    break;
                                }
                            }
                            if (sameHrmflag) {
                                break;
                            }
                        }
                    } else {
                        if (hr.getString("WORKCODE").equals(studentObject.getString("XH")) && studentObject.getString(jkzlzd).equals(hr.getString(oazlzd))) {
                            //移除第三方不同步的人员信息
                            studentHrmInfo.remove(i);
                            i--;
                            break;
                        }
                    }
                }
            }

            JSONObject fbcode = new JSONObject();
            fbcode.put("subcompanycode", subcompanycode);
            String fb = "{JSON}" + JSONObject.toJSONString(fbcode);
            JSONObject gwCode = new JSONObject();
            gwCode.put("jobtitlecode", "XS");
            String gw = "{JSON}" + JSONObject.toJSONString(gwCode);
            String jobgroupid = tc.getString("ryzwlb");
            String jobactivityid = tc.getString("ryzw");
            ArrayList<Object> errList = new ArrayList<>();
            //判断学生分配至具体的OA部门下  构建同步请求信息
            for (int i = 0; i < studentHrmInfo.size(); i++) {
                Map<String, Object> hrm = new HashMap<>();
                JSONObject studentObject = studentHrmInfo.getJSONObject(i);
                String jkbjdmzdValue = studentObject.getString(jkbjdmzd);
                //比较接口部门字段的值与在OA部门自定义字段【系统ID】相等
                if (StringUtils.isNotBlank(jkbjdmzdValue)) {
                    for (int j = 0; j < OADeptInfo.size(); j++) {
                        JSONObject oaDept = (JSONObject) OADeptInfo.get(j);
                        String xtid = oaDept.getString("xtid");
                        String oabjdmzdValue = oaDept.getString("bjdm");
                        if (jkbjdmzdValue.equals(oabjdmzdValue)) {
                            //将该教职员工设置在该部门下
                            JSONObject departmentcode = new JSONObject();
                            departmentcode.put("departmentcode", xtid);
                            String bm = "{JSON}" + JSONObject.toJSONString(departmentcode);
                            hrm.put("subcompany", fb);
                            hrm.put("department", bm);
                            hrm.put("jobtitle", gw);
                            hrm.put("jobgroupid", jobgroupid);
                            hrm.put("jobactivityid", jobactivityid);
                            //拼装请求参数 AssReqParams
                            assReqParams(hrm, studentObject);
                            hrmData.add(hrm);
                            break;
                        }
                    }
                } else {
                    errList.add(studentObject);
                }
            }

            log.info("workcode为空的学生信息" + JSONObject.toJSONString(errList));
            log.info("同步学生请求数据" + JSONObject.toJSONString(hrmData));
            String resMsg = syncHrmData(hrmData, errMsgList, sfSyncDetailLogOA);
            //同步信息
            JSONObject tBStudentInfo = new JSONObject();
            tBStudentInfo.put("--本次同步的数量--",hrmData.size());
            int errCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList),"失败");
            int expCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList),"syncHrmData异常");
            tBStudentInfo.put("--本次同步失败的数量--",errCount+expCount);
            String tbxxUUID = UUID.randomUUID().toString();
            tBStudentInfo.put("--本次同步请求参数信息--",tbxxUUID);
            sfSyncLog.setTbxx(JSONObject.toJSONString(tBStudentInfo));
            log.info("同步学生信息："+tbxxUUID+"同步信息:"+JSONObject.toJSONString(hrmData));
        } catch (Exception e) {
            tbjg = 1;
        }
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫转换为秒
        //同步结束时间
        sfSyncLog.setTbjssj(TimeUtil.getCurrentTimeString());
        //耗时（秒）
        sfSyncLog.setTbhs(String.valueOf(elapsedSeconds));
        //同步结果
        if(tbjg == 0 && sfSyncDetailLogZt.getJkjg() == 0 && sfSyncDetailLogOA.getJkjg() == 0 ){
            sfSyncLog.setTbjg(0);
        }else{
            sfSyncLog.setTbjg(1);
        }
        //同步失败信息
        String tbsbUUID = UUID.randomUUID().toString();
        sfSyncLog.setSbxx(JSONObject.toJSONString(errMsgList));
        log.info("同步学生失败信息："+tbsbUUID+"失败信息："+JSONObject.toJSONString(errMsgList));
        SFSyncLogUtil.insertLog(sfSyncLog, syncDetailList);
    }

    //拼装请求参数 AssReqParams
    private void assReqParams(Map<String, Object> hrm, JSONObject studentObject) {
        JSONObject base_custom_data = new JSONObject();
        JSONObject person_custom_data = new JSONObject();
        JSONObject work_custom_data = new JSONObject();
        //获取教师明细表配置
        JSONArray studentFormDetails = Utils.getDetailBaseInfo("select * from uf_xsrytb_dt1");
        for (int i = 0; i < studentFormDetails.size(); i++) {
            JSONObject jo = (JSONObject) studentFormDetails.get(i);
            String oazdwz = Util.null2String(jo.getString("oazdwz"));
            String oajkzd = Util.null2String(jo.getString("oajkzd"));

            //存放的位置 1.2.3自定义字段
            switch (oazdwz) {
                case "1":
                    //base_custom_data
                    Utils.custFields(jo, studentObject, base_custom_data);
                    break;
                case "2":
                    //person_custom_data
                    Utils.custFields(jo, studentObject, person_custom_data);
                    break;
                case "3":
                    //work_custom_data
                    Utils.custFields(jo, studentObject, work_custom_data);
                    break;
                default:
                    hrm.put(oajkzd, Utils.UtilParams(jo, studentObject));
            }
        }
        hrm.put("base_custom_data", base_custom_data);
        hrm.put("person_custom_data", person_custom_data);
        hrm.put("work_custom_data", work_custom_data);
    }

    /**
     * 调用标准rest接口方法，同步人员
     *
     * @param hrmData
     */
    @SuppressWarnings("unchecked")
    private String syncHrmData(List<Map<String, Object>> hrmData, ArrayList errList, SFSyncDetailLog sfSyncDetailLogOA) {
        ArrayList<Object> result = new ArrayList<>();
        if (sfSyncDetailLogOA == null) {
            sfSyncDetailLogOA = new SFSyncDetailLog();
        }
        long startTime = System.currentTimeMillis(); // 获取结束时间
        //请求时间
        sfSyncDetailLogOA.setQqsj(TimeUtil.getCurrentTimeString());
        //接口名
        sfSyncDetailLogOA.setJkm("OA同步学生人员");
        //请求报文
        String qquuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setQqbw(Util.null2String(qquuid));
        if(hrmData!=null){
            log.info("OA同步学生请求报文信息: "+qquuid+" 请求报文信息:"+JSONObject.toJSONString(hrmData));
        }else {
            log.info("OA同步学生请求报文信息: "+qquuid+" 请求报文信息为空");
        }

        if (errList == null) {
            errList = new ArrayList();
        }
        Map<String, Map<String, String>> restResult;
        List<Map<String, Object>> batchData;
        String errorMsg = "";
        ArrayList<Object> xybw = new ArrayList<>();
        try {
            int batchSize = 1000; // 每批次处理的最大数量
            int totalSize = hrmData.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                batchData = hrmData.subList(i, endIndex);
                // 1、执行同步
                restResult = manager.synHrmresource("workcode", batchData);
                xybw.add(restResult);
                if (restResult.toString().contains("失败")) {
                    errorMsg = "失败";
                    errList.add(JSONObject.toJSONString(restResult));
                }
                result.add(restResult);
            }
        } catch (Exception e) {
            errorMsg = "syncHrmData异常：" + SDUtil.getExceptionDetail(e);
            errList.add(errorMsg);
            log.info("syncHrmData异常：" + SDUtil.getExceptionDetail(e));
        }

        //响应报文
        String xxuuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setXybw(Util.null2String(xxuuid));
        log.info("OA同步学生响应报文信息:"+xxuuid+" 响应报文信息:"+JSONObject.toJSONString(xybw));
        //响应时间
        sfSyncDetailLogOA.setXysj(TimeUtil.getCurrentTimeString());
        //接口耗时
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
        sfSyncDetailLogOA.setJkhs(Util.null2String(String.valueOf(elapsedSeconds)));
        //接口结果
        if(StringUtils.isBlank(errorMsg)){
            sfSyncDetailLogOA.setJkjg(0);
        }else {
            sfSyncDetailLogOA.setJkjg(1);
        }
        return errorMsg;
    }

}
