package com.engine.shmhxy.tpw.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.shmhxy.tpw.job.bean.ThirdAttendanceInfoBean;
import com.weaver.general.Util;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.BaseBean;
import weaver.interfaces.schedule.BaseCronJob;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * @FileName AttendanceLogsJob
 * @Description 考勤日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/28
 */
@Getter
@Setter
public class AttendanceLogsJob extends BaseCronJob {
    /**
     * 事件标识名
     */
    private String eventName;
    /**
     * 要插入的建模表名
     */
    private String originalTable;
    /**
     * 考勤开始时间
     */
    private String kqkssj;
    /**
     * 考勤结束时间
     */
    private String kqjssj;
    /**
     * 开始同步考勤数据的秒级时间戳
     */
    private String timestamp;

    private BaseBean bb = new BaseBean();
    private RecordSet rs = new RecordSet();

    @Override
    public void execute() {
        try {
            String errMsg = "";
            bb.writeLog("AttendanceLogsJob----START");
            bb.writeLog("requestParams:eventName===>" + eventName);
            bb.writeLog("requestParams:originalTable===>" + originalTable);
            bb.writeLog("requestParams:kqkssj===>" + kqkssj);
            bb.writeLog("requestParams:kqjssj===>" + kqjssj);
            bb.writeLog("requestParams:timestamp===>" + timestamp);
            //所有第三方打卡的数据
            JSONArray thirdData = new JSONArray();
            if (StringUtils.isNotBlank(eventName) && StringUtils.isNotBlank(originalTable)) {
                JSONObject params = new JSONObject();
                JSONArray ja = new JSONArray();
                if(StringUtils.isNotBlank(kqkssj) && StringUtils.isNotBlank(kqjssj)){
                    String kssj = dateConvert(kqkssj);
                    String jssj = dateConvert(kqjssj);
                    rs.execute("delete from "+originalTable+" where checkinTime >= "+kssj +" and checkinTime <= "+jssj);
                    JSONObject jsonObject1 = new JSONObject();
                    JSONObject jsonObject2 = new JSONObject();
                    jsonObject1.put("name", "checkin_time");
                    jsonObject1.put("operator", "GTE");
                    jsonObject1.put("value", kssj);
                    jsonObject2.put("name", "checkin_time");
                    jsonObject2.put("operator", "LTE");
                    jsonObject2.put("value", jssj);
                    ja.add(jsonObject1);
                    ja.add(jsonObject2);
                }else {
                    if (StringUtils.isNotBlank(timestamp)) {
                        String sdkssj = dateConvert(timestamp);
                        JSONObject jsonObject1 = new JSONObject();
                        JSONObject jsonObject2 = new JSONObject();
                        jsonObject1.put("name", "checkin_time");
                        jsonObject1.put("operator", "GTE");
                        jsonObject1.put("value", sdkssj);
                        jsonObject2.put("name", "checkin_time");
                        jsonObject2.put("operator", "LTE");
                        jsonObject2.put("value", SDUtil.getSecondTimestamp(getYesterdayEndTime()));
                        ja.add(jsonObject1);
                        ja.add(jsonObject2);
                    } else {
                        JSONObject jsonObject1 = new JSONObject();
                        JSONObject jsonObject2 = new JSONObject();
                        jsonObject1.put("name", "checkin_time");
                        jsonObject1.put("operator", "GTE");
                        jsonObject1.put("value", SDUtil.getSecondTimestamp(getYesterdayStartTime()));
                        jsonObject2.put("name", "checkin_time");
                        jsonObject2.put("operator", "LTE");
                        jsonObject2.put("value", SDUtil.getSecondTimestamp(getYesterdayEndTime()));
                        ja.add(jsonObject1);
                        ja.add(jsonObject2);
                    }
                }
                if(!ja.isEmpty()){
                    params.put("filter", ja);
                }
                params.put("page",1);
                bb.writeLog("ESB的params" +JSONObject.toJSONString(params));
                //获取所有第三方打卡的数据
                getAllThirdData(thirdData, params);
                bb.writeLog("从第三方接口中获取的数据" + thirdData.toJSONString());
                //将原始数据表插入原始记录数据日志表当中--这里只做原始记录的同步
                if (!thirdData.isEmpty()) {
                    errMsg = insertOriginalTable(thirdData);
                }
            }

        } catch (Exception e) {
            bb.writeLog("考勤日志异常：" + SDUtil.getExceptionDetail(e));
        }
        bb.writeLog("AttendanceLogsJob----end");
    }
    //将原始数据表插入原始记录数据日志表当中--这里只做原始记录的同步
    private String insertOriginalTable(JSONArray thirdData) {
        String errMsg = "";
        ArrayList<ThirdAttendanceInfoBean> thirdAttendanceInfoBeans = new ArrayList<>();
        for (int i = 0; i < thirdData.size(); i++) {
            ThirdAttendanceInfoBean bean = new ThirdAttendanceInfoBean();
            String thirdDataStr = (String) thirdData.get(i);
            JSONObject jsonObject = JSONObject.parseObject(thirdDataStr);
            if (jsonObject != null && !jsonObject.isEmpty()) {
                bean.setCheckinTime(Util.null2String(jsonObject.getString("checkin_time")));
                bean.setWifiName(Util.null2String(jsonObject.getString("wifiname")));
                bean.setNotes(Util.null2String(jsonObject.getString("notes")));
                bean.setLng(Util.null2String(jsonObject.getString("lng")));
                bean.setLocationDetail(Util.null2String(jsonObject.getString("location_detail")));
                bean.setGroupId(Util.null2String(jsonObject.getString("groupid")));
                bean.setUserid(Util.null2String(jsonObject.getString("userid")));
                bean.setGroupName(Util.null2String(jsonObject.getString("groupname")));
                bean.setDeviceId(Util.null2String(jsonObject.getString("deviceid")));
                bean.setCheckinType(Util.null2String(jsonObject.getString("checkin_type")));
                bean.setExceptionType(Util.null2String(jsonObject.getString("exception_type")));
                bean.setLocationTitle(Util.null2String(jsonObject.getString("location_title")));
                bean.setWifiMac(Util.null2String(jsonObject.getString("wifimac")));
                bean.setMediaIds(Util.null2String(jsonObject.getString("mediaids")));
                bean.setLat(Util.null2String(jsonObject.getString("lat")));
                bean.setSchCheckinTime(Util.null2String(jsonObject.getString("sch_checkin_time")));
                bean.setCityOsOp(Util.null2String(jsonObject.getString("cityosOp")));
                bean.setCityOsLandingTs(Util.null2String(jsonObject.getString("cityosLandingTs")));
                thirdAttendanceInfoBeans.add(bean);
            }
        }
        if (thirdAttendanceInfoBeans != null && !thirdAttendanceInfoBeans.isEmpty()) {
            ArrayList<Object> errList = new ArrayList<>();
            int moduleId = ModuleDataUtil.getModuleIdByName(originalTable);
            // 获取rows的长度
            int totalRows = thirdAttendanceInfoBeans.size();
            // 定义每次截取的大小
            int batchSize = 1000;
            // 开始截取数据
            for (int i = 0; i < totalRows; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalRows); // 确定本次截取的结束索引
                List<ThirdAttendanceInfoBean> beans = thirdAttendanceInfoBeans.subList(i, endIndex);
                ModuleResult mr = ModuleDataUtil.insertObjList(beans, originalTable, moduleId, 1);
                if (!mr.isSuccess()) {
                    errMsg = mr.getErroMsg();
                    errList.addAll(beans);
                }
            }
            bb.writeLog("插入原始数据出错错误信息为"+errMsg);
            bb.writeLog("插入原始数据出错错误数据为"+JSONObject.toJSONString(errList));
        }
        return errMsg;
    }

    //获取所有第三方打卡的数据
    private void getAllThirdData(JSONArray thirdData, JSONObject params) {
        bb.writeLog("请求参数" + params.toJSONString());
        EsbEventResult esbEventResult = EsbUtil.callEsbEvent(eventName, params);
        bb.writeLog("esbEventResult响应结果" + JSONObject.toJSONString(esbEventResult));
        if (esbEventResult.isSuccess()) {
            JSONObject res = esbEventResult.getData();
            bb.writeLog("esbEventResult.getData响应结果" + JSONObject.toJSONString(res));
            if (res != null && !res.isEmpty()) {
                JSONObject data = (JSONObject) res.get("body");
                if (data != null) {
                    String records = (String) data.get("records");
                    bb.writeLog("records" + records);
                    int rcs = Integer.parseInt(records);
                    Object rows =  data.get("rows");
                    JSONArray rowsArray = (JSONArray) data.get("rows");
                    bb.writeLog("data.get(rows)响应结果" + rows);
                    bb.writeLog("JSONArray rowsArray响应结果" + rowsArray.toJSONString());
                    if (rowsArray != null) {
                        thirdData.addAll(rowsArray);
                    }
                    int batchSize = 1000;
                    int count = 1;
                    for (int i = 1000; i < rcs; i += batchSize) {// 确定本次截取的结束索引
                        //发送插入建模中的请求
                        count += 1;
                        params.put("page", count);
                        EsbEventResult insertResulr = EsbUtil.callEsbEvent(eventName, params);
                        if (insertResulr.isSuccess()) {
                            JSONObject res1 = insertResulr.getData();
                            if (res1 != null && !res1.isEmpty()) {
                                JSONObject data1 = (JSONObject) res1.get("body");
                                if (data1 != null) {
                                    JSONArray rowsArray1 = (JSONArray) data1.get("rows");
                                    if (rowsArray1 != null) {
                                        thirdData.addAll(rowsArray1);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    private static Date getYesterdayStartTime() {
        Calendar yesterdayStart = Calendar.getInstance();
        // 将日期减去一天
        yesterdayStart.add(Calendar.DAY_OF_MONTH, -1);
        // 设置时间为0点0分0秒0毫秒
        yesterdayStart.set(Calendar.HOUR_OF_DAY, 0);
        yesterdayStart.set(Calendar.MINUTE, 0);
        yesterdayStart.set(Calendar.SECOND, 0);
        yesterdayStart.set(Calendar.MILLISECOND, 0);
        return yesterdayStart.getTime();
    }

    private static Date getYesterdayEndTime() {
        Calendar yesterdayEnd = Calendar.getInstance();
        // 将日期减去一天
        yesterdayEnd.add(Calendar.DAY_OF_MONTH, -1);
        // 设置时间为23点59分59秒999毫秒
        yesterdayEnd.set(Calendar.HOUR_OF_DAY, 23);
        yesterdayEnd.set(Calendar.MINUTE, 59);
        yesterdayEnd.set(Calendar.SECOND, 59);
        yesterdayEnd.set(Calendar.MILLISECOND, 999);
        return yesterdayEnd.getTime();
    }

    public String dateConvert(String timeStr) {
        // 定义时间字符串
//        String timeStr = "2024-05-10 08:30:00";

        // 定义时间字符串的格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        // 将时间字符串转换为 LocalDateTime 对象
        LocalDateTime dateTime = LocalDateTime.parse(timeStr, formatter);

        // 将 LocalDateTime 对象转换为时间戳（秒级）
        long timestamp = dateTime.atZone(ZoneId.systemDefault()).toEpochSecond();

        // 打印时间戳
        return String.valueOf(timestamp);
    }
}
