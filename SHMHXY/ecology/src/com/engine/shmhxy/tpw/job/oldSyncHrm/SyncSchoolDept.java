package com.engine.shmhxy.tpw.job.oldSyncHrm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.weaver.general.Util;
import org.apache.commons.lang3.StringUtils;
import weaver.general.TimeUtil;

import java.util.*;

public class SyncSchoolDept {

    /**
     * 正常新增/更新的部门
     */
    List<List<Map<String, Object>>> deptData = new ArrayList<>();


    /**
     * 需要解封的部门
     */
    List<List<Map<String, Object>>> unDelData = new ArrayList<>();

    /**
     * 需要封存的部门
     */
    List<List<Map<String, Object>>> delData = new ArrayList<>();

    private static final Logger log = LoggerFactory.getLogger(SyncSchoolDept.class);


    //同步学校部门信息
    public String syncSchoolDeptOaDept(Map<String, String> canceledDeptMap, Map<String, String> normalDeptMap, String token, String subcompanycode, String uuid, JSONObject tc) {
        SFSyncLog sfSyncLog = new SFSyncLog();
        ArrayList<SFSyncDetailLog> syncDetailList = new ArrayList<>();
        SFSyncDetailLog sfSyncDetailLog = new SFSyncDetailLog();
        SFSyncDetailLog sfSyncDetailLogOA = new SFSyncDetailLog();
        syncDetailList.add(sfSyncDetailLog);
        syncDetailList.add(sfSyncDetailLogOA);
        //获取开始时间
        long startTime = System.currentTimeMillis();
        //同步批次号
        sfSyncLog.setPch(uuid);
        sfSyncLog.setBz("查看日志请在当前服务器地址后面拼接/log/sd/sd.log，例如：http://122.51.232.134:8080/log/sd/sd.log 。将页面内容保存至本地，按照建模表单请求/响应报文中的UUID，即可搜索查看详细信息。");
        //同步类型
        sfSyncLog.setTblx(0);
        //同步日期
        sfSyncLog.setTbrq(TimeUtil.getCurrentDateString());
        //同步开始时间
        sfSyncLog.setTbkssj(TimeUtil.getCurrentTimeString());
        //同步响应信息
        String errMsg = "成功";
        JSONObject bodyParam = new JSONObject();
        ArrayList errResInfo = new ArrayList<>();
        try {
            if (StringUtils.isNotBlank(tc.getString("id"))) {
                sfSyncLog.setZpz(Integer.parseInt(tc.getString("id")));
            }
            //学校部门配置主表信息
            JSONObject scm = Utils.getMainBaseInfo("select * from uf_xxbmtb");
            //中台数据部门信息层级排序
            log.info("处理接口返回的的学校部门信息---start");
            ArrayList deptList = new ArrayList<>();
            errMsg = handleSchoolResData(token, deptList, scm, sfSyncDetailLog, errResInfo);
            if ("失败".equals(sfSyncDetailLog.getJkjg())) {
                errResInfo.add(sfSyncDetailLog.getXybw());
                errMsg = "失败";
            }
            log.info("处理接口返回的的学校部门信息---end");
            if (StringUtils.isNotBlank(errMsg)) {
                log.info("处理接口返回的的学校部门信息出错");
            }
            log.info("接口字段到OA同步接口字段之间的转换--start");
            errMsg = deptFieldConver(deptList, scm, canceledDeptMap, normalDeptMap, subcompanycode);
            if (StringUtils.isNotBlank(errMsg)) {
                log.info("接口字段到OA同步接口字段之间的转换出错");
            }
            log.info("接口字段到OA同步接口字段之间的转换--end");
            log.info("发送同步学校部门请求--start");
            errMsg = sendSyncDeptReq(sfSyncDetailLogOA, bodyParam, errResInfo);

        } catch (Exception e) {
            errMsg = "失败";
        }
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫转换为秒
        //同步结束时间
        sfSyncLog.setTbjssj(TimeUtil.getCurrentTimeString());
        //耗时（秒）
        sfSyncLog.setTbhs(String.valueOf(elapsedSeconds));
        if (sfSyncDetailLog.getJkjg() == 0 && sfSyncDetailLogOA.getJkjg() == 0) {
            //同步结果
            if ("失败".equals(errMsg)) {
                sfSyncLog.setTbjg(1);
            } else {
                sfSyncLog.setTbjg(0);
            }
        } else {
            //同步结果
            sfSyncLog.setTbjg(1);
        }

        //同步信息
        String tbxxUUID = UUID.randomUUID().toString();
        JSONObject tBSchoolInfo = new JSONObject();
        int count  = 0;
        if(!unDelData.isEmpty()){
            List<Map<String, Object>> maps = unDelData.get(0);
            if(maps != null){
                count = maps.size();
            }
        }
        if(!deptData.isEmpty()){
            List<Map<String, Object>> maps = deptData.get(0);
            if(maps != null){
                count = count + maps.size();
            }
        }
        if(!delData.isEmpty()){
            List<Map<String, Object>> maps = delData.get(0);
            if(maps != null){
                count = count + maps.size();
            }
        }
        tBSchoolInfo.put("--本次同步的数量--",count);

        // 将 ArrayList 转换为 JSON 数组
//        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(errResInfo));
//         将 JSON 数组转换为字符串
//        String errMsgListAsString = jsonArray.toJSONString();
        int errCount = Utils.countOccurrences(JSONObject.toJSONString(errResInfo),"失败");
        int expCount = Utils.countOccurrences(JSONObject.toJSONString(errResInfo),"syncEachDeptData异常" );
        tBSchoolInfo.put("--本次同步失败的数量--",errCount+expCount);
        tBSchoolInfo.put("--本次同步请求参数信息--",tbxxUUID);

        sfSyncLog.setTbxx(JSONObject.toJSONString(tBSchoolInfo));
        log.info("同步学校部门：" + tbxxUUID + "同步信息:" + JSONObject.toJSONString(bodyParam));

        //同步失败信息
        String tbsbUUID = UUID.randomUUID().toString();
        sfSyncLog.setSbxx(JSONObject.toJSONString(errResInfo));
        log.info("同步学校部门失败信息：" + tbsbUUID + "同步失败信息:" + JSONObject.toJSONString(errResInfo));
        SFSyncLogUtil.insertLog(sfSyncLog, syncDetailList);
        log.info("发送同步学校部门请求--end");
        return null;
    }

    //中台数据部门信息层级排序
    public String handleSchoolResData(String token, ArrayList deptList, JSONObject scm, SFSyncDetailLog sfSyncDetailLog, ArrayList errResInfo) {

        try {
            if (sfSyncDetailLog == null) {
                sfSyncDetailLog = new SFSyncDetailLog();
            }
            //接口机构编码字段
            String jkjgbmzdValue = scm.getString("jkjgbmzd");
            //接口系统ID字段
            String jkxtidzdValue = scm.getString("jkxtidzd");
            //接口上级系统ID字段
            String jksjxtidzdValue = scm.getString("jksjxtidzd");

            JSONArray schoolBaseInfo = Utils.getThirdHrmInfoWithPage(errResInfo, token, "uf_xxbmtb", sfSyncDetailLog);
            if (schoolBaseInfo == null || schoolBaseInfo.isEmpty()) {
                return "分页获取中台数据部门信息出错！";
            }
            //得到顶级部门
            String errMsg = getTopDept(deptList, schoolBaseInfo, jksjxtidzdValue);
            if (StringUtils.isNotBlank(errMsg)) {
                return errMsg;
            }
            //将所有部门分等级 根据顶级部门 来找寻顶级部门下的下级部门 递归操作
            deptRec(deptList, schoolBaseInfo, jkxtidzdValue, jksjxtidzdValue);
            log.info("具有层级结构的中台数据学校部门信息：" + JSONObject.toJSONString(deptList));
        } catch (Exception e) {
            log.info("中台数据部门信息层级排序handleSchoolResData出错" + SDUtil.getExceptionDetail(e));
            return SDUtil.getExceptionDetail(e);
        }
        return null;
    }

    //接口字段到OA同步接口字段之间的转换
    private String deptFieldConver(ArrayList deptList, JSONObject scm, Map<String, String> canceledDeptMap, Map<String, String> normalDeptMap, String subcompanycode) {
        try {
            if (scm == null) {
                return "学校部门配置表的主表信息为空!";
            }
            log.info("学校部门配置表的主表信息" + scm.toJSONString());

            //获取学校部门配置表的明细表信息
            JSONArray scd = Utils.getDetailBaseInfo("select * from uf_xxbmtb_dt1");
            if (scd == null) {
                return "学校部门配置表的明细表信息为空!";
            }
            log.info("学校部门配置表的明细表信息" + scd.toJSONString());
            String djfb = "";

            String xzzpz = scm.getString("xzzpz");
            String jkdz = scm.getString("jkdz");
            String jkzlzd = scm.getString("jkzlzd");
            String oazlzd = scm.getString("oazlzd");
            String oazlzdwz = scm.getString("oazlzdwz");
            String jkjgbmzd = scm.getString("jkjgbmzd");
            String jkxtidzd = scm.getString("jkxtidzd");
            String jksjxtidzd = scm.getString("jksjxtidzd");
            String jkfczd = scm.getString("jkfczd");
            String jkfcz = scm.getString("jkfcz");
            JSONArray oaDeptInfo = null;
            //判断OA增量字段的位置
            switch (oazlzdwz) {
                case "0":
                    //系统标准部门表
                    oaDeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartment");
                    break;
                default:
                    //自定义部门基本信息
                    oaDeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartmentdefined");
            }
            //中台数据部门中的所有部门数据
            for (int i = 0; i < deptList.size(); i++) {
                //需要解封的部门
                ArrayList<Map<String, Object>> unDelList = new ArrayList<>();
                //需要封存的部门
                ArrayList<Map<String, Object>> delList = new ArrayList<>();
                //正常新增/更新的部门
                ArrayList<Map<String, Object>> updataDeptList = new ArrayList<>();
                unDelData.add(unDelList);
                delData.add(delList);
                deptData.add(updataDeptList);
                JSONArray deptArray = (JSONArray) deptList.get(i);
                //去除不用同步的部门
                //todo
//                for (int j = 0; j < deptArray.size(); j++) {
//                    JSONObject dept = (JSONObject) deptArray.get(j);
//                    //接口系统id
//                    String XTID = dept.getString(jkxtidzd);
//                    //增量值
//                    String tcityosLandingTs = dept.getString(jkzlzd);
//                    for (int k = 0; k < oaDeptInfo.size(); k++) {
//                        JSONObject oadept = (JSONObject) oaDeptInfo.get(k);
//                        String oaXTID = (String) oadept.getString("xtid");
//                        String oazhgxrq = (String) oadept.getString(oazlzd);
//                        if (XTID.equals(oaXTID) && tcityosLandingTs.equals(oazhgxrq)) {
//                            deptArray.remove(j);
//                            j--;
//                            break;
//                        }
//                    }
//                }

                //将要同步的部门
                for (int j = 0; j < deptArray.size(); j++) {
                    JSONObject dept = (JSONObject) deptArray.get(j);

                    Map<String, Object> deptParam = new HashMap<>();

                    JSONObject custObject = new JSONObject();
                    String infeFczdValue = Util.null2String(dept.getString(jkfczd));
                    //接口封存值与配置值相等 该部门将要被封存
                    String code = "";
                    String name = "";
                    String partentCode = dept.getString(jksjxtidzd);
                    String canceled;
                    if (infeFczdValue.equals(jkfcz)) {
                        //封存该部门
                        canceled = "0";
                    } else {
                        canceled = "1";
                    }
                    Iterator iterator = scd.iterator();
                    while (iterator.hasNext()) {
                        JSONObject record = (JSONObject) iterator.next();
                        String oajkzd = Util.null2String(record.getString("oajkzd"));
                        String oazdwz = Util.null2String(record.getString("oazdwz"));
                        if ("0".equals(oazdwz)) {
                            switch (oajkzd) {
                                case "code":
                                    code = Utils.UtilParams(record, dept);
                                    break;
                                case "shortname":
                                    name = Utils.UtilParams(record, dept);
                                    break;
                                case "fullname":
                                    name = Utils.UtilParams(record, dept);
                                    break;
                                default:
                            }
                        } else {
                            Utils.custFields(record, dept, custObject);
                        }
                    }
                    //判断部门是否解封还是封存
                    //封存的部门是否有该部门编码
                    if (canceledDeptMap.containsKey(code) && "1".equals(canceled)) { //解封
                        deptParam = Utils.buildDeptParam("delete",
                                code,
                                name,
                                partentCode,
                                subcompanycode,
                                canceled,
                                custObject);
                        unDelList.add(deptParam);
                    }else if (normalDeptMap.containsKey(code)&& "0".equals(canceled)) {//未封存的部门是否有该部门编码 封存
                        deptParam = Utils.buildDeptParam("delete",
                                code,
                                name,
                                partentCode,
                                subcompanycode,
                                canceled,
                                custObject);
                        delList.add(deptParam);
                    }else {
                        //更新的部门
                        deptParam = Utils.buildDeptParam("delete",
                                code,
                                name,
                                partentCode,
                                subcompanycode,
                                canceled,
                                custObject);
                        updataDeptList.add(deptParam);
                    }


                }
            }
        } catch (Exception e) {
            log.info("接口字段到OA同步接口字段之间的转换出错" + SDUtil.getExceptionDetail(e));
            return "接口字段到OA同步接口字段之间的转换出错";
        }
        return null;
    }


    //根据不同的部门封存类型来发送部门同步请求
    private String sendSyncDeptReq(SFSyncDetailLog sfSyncDetailLog, JSONObject bodyParam, ArrayList errResInfo) {

        if (sfSyncDetailLog == null) {
            sfSyncDetailLog = new SFSyncDetailLog();
        }

        long startTime = System.currentTimeMillis(); // 获取开始时间
        //请求时间
        sfSyncDetailLog.setQqsj(TimeUtil.getCurrentTimeString());
        if (bodyParam == null) {
            bodyParam = new JSONObject();
        }
        bodyParam.put("需要解封的部门", unDelData);
        bodyParam.put("需要封存的部门", delData);
        bodyParam.put("正常新增/更新的部门", deptData);
        String errorMsg = "";
        ArrayList xybw = new ArrayList<>();

        if (!unDelData.isEmpty()) {
            errorMsg = Utils.syncEachDeptData(unDelData, xybw, errResInfo);
        }
        // 2、执行封存
        if (errorMsg.isEmpty() && !delData.isEmpty()) {
            errorMsg = Utils.syncEachDeptData(delData, xybw, errResInfo);
        }
        // 3、执行新增/更新
        if (errorMsg.isEmpty() && !deptData.isEmpty()) {
            errorMsg = Utils.syncEachDeptData(deptData, xybw, errResInfo);
        }

        //接口名
        sfSyncDetailLog.setJkm("OA同步学校部门");
        //请求报文
        String qquuid = UUID.randomUUID().toString();
        sfSyncDetailLog.setQqbw(qquuid);
        log.info("OA同步学校部门请求报文信息: " + qquuid + " 请求报文信息:" + JSONObject.toJSONString(bodyParam));
        //响应报文
        String xxuuid = UUID.randomUUID().toString();
        sfSyncDetailLog.setXybw(xxuuid);
        log.info("OA同步学校部门:" + xxuuid + " 响应报文信息:" + JSONObject.toJSONString(xybw));
        //响应时间
        sfSyncDetailLog.setXysj(TimeUtil.getCurrentTimeString());
        //接口耗时
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
        sfSyncDetailLog.setJkhs(String.valueOf(elapsedSeconds));
        //接口结果
        if (StringUtils.isBlank(errorMsg)) {
            sfSyncDetailLog.setJkjg(0);
        } else {
            sfSyncDetailLog.setJkjg(1);
        }
        return errorMsg;

    }

    //得到顶级部门
    private String getTopDept(ArrayList deptList, JSONArray schoolBaseInfo, String jksjxtidzdValue) {

        try {
            JSONArray topDept = new JSONArray();
            for (int i = 0; i < schoolBaseInfo.size(); i++) {
                JSONObject schoolObject = schoolBaseInfo.getJSONObject(i);
                String SJJG = schoolObject.getString(jksjxtidzdValue);
                // 判断是否满足移除条件
                if (StringUtils.isBlank(SJJG)) {
                    topDept.add(schoolObject);
                    schoolBaseInfo.remove(i);
                    i--; // 因为移除了一个元素，需要调整索引
                }
            }
            deptList.add(topDept);
        } catch (Exception e) {
            log.info("得到顶级部门getTopDept信息出错" + SDUtil.getExceptionDetail(e));
            return "得到顶级部门getTopDept信息出错";
        }
        if (deptList.size() == 0) {
            return "获取不到顶级部门信息";
        }
        return null;
    }

    //递归，目的是将所有的部门分层级的村堆到deptList当中
    private void deptRec(ArrayList deptList, JSONArray schoolBaseInfo, String jkxtidzdValue, String jksjxtidzdValue) {
        try {
            if (schoolBaseInfo.isEmpty()) {
                return;
            }
            JSONArray dept = new JSONArray();

            ArrayList<String> XTIDS = new ArrayList<>();
            JSONArray supDept = (JSONArray) deptList.get(deptList.size() - 1);
            for (int j = 0; j < supDept.size(); j++) {
                JSONObject jo = (JSONObject) supDept.get(j);
                String XTID = jo.getString(jkxtidzdValue);
                XTIDS.add(XTID);
            }
            for (int i = 0; i < schoolBaseInfo.size(); i++) {
                JSONObject schoolObject = (JSONObject) schoolBaseInfo.get(i);
                String SJJG = schoolObject.getString(jksjxtidzdValue);
                if (XTIDS.contains(SJJG)) {
                    dept.add(schoolObject);
                    schoolBaseInfo.remove(i);
                    i--; // 因为移除了一个元素，需要调整索引
                }
            }
            deptList.add(dept);
            deptRec(deptList, schoolBaseInfo, jkxtidzdValue, jksjxtidzdValue);
        } catch (Exception e) {
            log.info("递归排序部门出错" + SDUtil.getExceptionDetail(e));
        }
    }


}
