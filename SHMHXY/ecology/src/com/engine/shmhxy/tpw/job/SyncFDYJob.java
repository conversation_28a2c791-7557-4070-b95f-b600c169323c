package com.engine.shmhxy.tpw.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.http.dto.RestResult;
import com.engine.parent.http.util.HttpUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.parent.module.dto.ModuleResult;
import com.engine.parent.module.util.ModuleDataUtil;
import com.engine.parent.query.util.QueryUtil;
import com.engine.sd2.db.util.DBUtil;
import com.engine.shmhxy.tpw.job.bean.SyncConfig;
import com.engine.shmhxy.tpw.job.bean.SyncFDYLog;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;
import weaver.general.Util;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @FileName SyncFDYJob
 * @Description 学生辅导员同步
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/4/9
 */
@Getter
@Setter
public class SyncFDYJob extends BaseCronJob {
    private static final Logger log = LoggerFactory.getLogger(SyncFDYJob.class);
    /**
     * 组织架构同步接口总配置编号
     */
    private String bh;
    /**
     * 辅导员配置表名
     */
    private String tableName;
    /**
     * 手动同步
     */
    private String handSync;
    /**
     * 中台接口请求token
     */
    private String token;
    /**
     * 组织架构接口同步总配置
     */
    private SyncConfig config;
    /**
     * 辅导员同步主配置
     */
    private JSONObject mainConfig;
    /**
     * 辅导员同步明细配置
     */
    private JSONArray detailConfig;
    /*
     * 标准表人力资源信息  workcode - 人员对象
     */
    private Map<String, Map<String, Object>> hrmMap;
    /*
     * 标准表部门信息  departmentcode - 部门对象
     */
    private Map<String, Map<String, Object>> departObjMap;
    /*
     * 标准表部门信息  departmentcode - 部门对象
     */
    private Map<String, String> departMap;
    /*
     * 标准表分部信息  id - code
     */
    private Map<String, String> subcompanyMap;

    @Override
    public void execute() {
        log.info("班级辅导员同步开始");
        try {
            //创建日志类
            SyncFDYLog syncFDYLog = new SyncFDYLog();
            syncFDYLog.setSync_begin_date(TimeUtil.getCurrentDateString());
            syncFDYLog.setSync_begin_time(TimeUtil.getCurrentTimeString());
            String errMsg = "";
            //1.初始化参数
            _init();
            //2.重置参数
            errMsg = _reset();
            if (StringUtils.isNotBlank(errMsg)) {
                //调用写入日志方法 结束定时任务执行
                syncFDYLog.setResult(1);
                syncFDYLog.setBz(errMsg);
                writeMainLog(syncFDYLog);
                return;
            }
            //3.调用中台接口，获取辅导员数据
            JSONArray fdyInfos = getZtFdyInfo();
            log.info("从中台获取的班级辅导员信息fdyInfos : " + JSONObject.toJSONString(fdyInfos));
            syncFDYLog.setOriginalData(JSON.toJSONString(fdyInfos));
            if (fdyInfos.isEmpty()) {
                //调用写入日志方法 结束定时任务执行
                syncFDYLog.setResult(0);
                syncFDYLog.setBz("从中台获取的班级辅导员信息为空");
                writeMainLog(syncFDYLog);
                return;
            }
            //对中台辅导员数据做过滤 每个fdyInfos对象中都包含两个字段 班级代码：BJDM 工号：ZGH 一个班级有多个辅导员：不进行同步;一个班级无辅导员：不进行同步
            Map<String, List<JSONObject>> filterFdyInfos = filterFdyInfos(fdyInfos);
            log.info("中台辅导员过滤后数据 :" + JSONObject.toJSONString(filterFdyInfos));
            List<JSONObject> fdyList = filterFdyInfos.get("valid");
            syncFDYLog.setAfterFilterData(JSONObject.toJSONString(fdyList));
            log.info("将要同步的班级辅导员同步数据 :" + JSONObject.toJSONString(fdyList));
            //组装要同步的班级辅导员数据
            ArrayList<Map<String, Object>> fdyDeparts = assembleFdyReqParams(fdyList);
            log.info("组装要同步的班级辅导员数据 :" + JSONObject.toJSONString(fdyDeparts));
            syncFDYLog.setSync_counts(fdyDeparts.size());
            syncFDYLog.setDeptSyncData(JSONObject.toJSONString(fdyDeparts));
//            if (!fdyDeparts.isEmpty()) {
//                //进行班级辅导员同步
//                ArrayList<Map<String, Map<String, String>>> maporg_codes = syncFdyDeparts(fdyDeparts);
//                syncFDYLog.setSync_err_info(JSONObject.toJSONString(maps));
//                syncFDYLog.setResult(!maps.isEmpty() ? 0 : 1);
//            }
            writeMainLog(syncFDYLog);
        } catch (Exception e) {
            log.info("班级辅导员同步 exception ：" + SDUtil.getExceptionDetail(e));
        }
        log.info("班级辅导员同步结束");
    }

    private ArrayList<Map<String, Map<String, String>>> syncFdyDeparts(ArrayList<Map<String, Object>> deptData) {
        ArrayList<Map<String, Map<String, String>>> errs = new ArrayList<>();
        try {
            HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();
            int batchSize = 1000; // 每批次处理的最大数量
            int totalSize = deptData.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                List<Map<String, Object>> batchData = deptData.subList(i, endIndex);
                // 1、执行同步
                Map<String, Map<String, String>> restResult = manager.synDepartment("code", batchData);
                if (restResult.toString().contains("失败")) {
                    errs.add(restResult);
                }
            }

        } catch (Exception e) {
            log.info("");
        }
        return errs;
    }

    private ArrayList<Map<String, Object>> assembleFdyReqParams(List<JSONObject> fdyList) {
        ArrayList<Map<String, Object>> fdyDeparts = new ArrayList<>();
        try {
            //将要同步的辅导员部门

            for (int i = 0; i < fdyList.size(); i++) {
                JSONObject fdy = fdyList.get(i);
                //判断该辅导员是否符合同步的逻辑判断
                if (judgeFdy(fdy)) {
                    if (departObjMap.containsKey(fdy.getString("BJDM"))) {
                        Map<String, Object> department = departObjMap.get(fdy.getString("BJDM"));
                        if("2451201".equals(fdy.getString("BJDM"))){
                            log.info("班级代码2451201"+JSONObject.toJSONString(department));
                        }
                        Map<String, Object> params = new HashMap<>();
                        params.put("@action", "edit");
                        params.put("code", Util.null2String(department.get("DEPARTMENTCODE")));
                        params.put("shortname", Util.null2String(department.get("DEPARTMENTNAME")));
                        params.put("fullname", Util.null2String(department.get("DEPARTMENTNAME")));
                        //分部id
                        String subcompanyid1 = Util.null2String(department.get("SUBCOMPANYID1"));
                        if (subcompanyMap.containsKey(subcompanyid1)) {
                            //设置分部code
                            params.put("org_code", subcompanyMap.get(subcompanyid1));
                        }
                        //上级部门id
                        String supdepid = Util.null2String(department.get("SUPDEPID"));
                        if (departMap.containsKey(supdepid)) {
                            //设置上级部门code
                            params.put("parent_code", Util.null2String(departMap.get(supdepid)));
                        }
                        //设置自定义字段信息
                        JSONObject custObject = new JSONObject();
                        if (hrmMap.containsKey(fdy.getString("ZGH"))) {
                            Map<String, Object> hrm = hrmMap.get(fdy.getString("ZGH"));
                            String id = Util.null2String(hrm.get("ID"));
                            custObject.put("fdy", id);
                        }
                        params.put("custom_data", custObject);
                        fdyDeparts.add(params);
                    }
                }
            }
        } catch (Exception e) {
            log.info("装要同步的班级辅导员数据 exception:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return fdyDeparts;
    }

    private boolean judgeFdy(JSONObject fdy) {
        try {
            RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
            for (int i = 0; i < detailConfig.size(); i++) {
                //职工号 ZGH -> 对应人力资源workcode
                String zgh = Util.null2String(fdy.get("ZGH"));
                //班级 bjdm -> 对应部门的code
                String bjdm = Util.null2String(fdy.get("BJDM"));
                JSONObject detail = (JSONObject) detailConfig.get(i);
                //需满足条件类型
                String xmztjlx = Util.null2String(detail.get("xmztjlx"));
                //数据中台接口字段
                String sjztjkzd = Util.null2String(detail.get("sjztjkzd"));
                //数据中台接口字段值
                String sjztjkzdz = Util.null2String(fdy.get(sjztjkzd));
                if ("0".equals(xmztjlx)) {//固定值
                    String gdz = Util.null2String(detail.get("gdz"));
                    //数据中台接口字段的值与固定值相等，则满足条件
                    if (gdz.equals(sjztjkzdz)) {

                    } else {
                        return false;
                    }
                } else { //关联OA字段
                    //oa接口字段
                    String oajkzd = Util.null2String(detail.get("oajkzd"));
                    //oa字段位置
                    String oazdwz = Util.null2String(detail.get("oazdwz"));

                    if ("0".equals(oajkzd)) { //标准人力资源部门表
                        recordSet.executeQuery("select " + oajkzd + " from hrmdepartment where departmentcode = " + bjdm);
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    } else if ("1".equals(oazdwz)) {//自定义人力资源部门表
                        recordSet.executeQuery("select " + oajkzd + " from hrmdepartmentdefined where deptid in (select * from hrmdepartment where departmentcode " + bjdm + ") ");
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    } else if ("2".equals(oazdwz)) {//标准人力资源表
                        recordSet.executeQuery("select " + oajkzd + " from hrmresource where workcode = " + zgh);
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    } else if ("3".equals(oazdwz)) {//自定义人力资源基本信息表
                        recordSet.executeQuery("select " + oajkzd + " from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid= -1 and id in (select id from hrmresource where workcode = " + zgh + ")");
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    } else if ("4".equals(oazdwz)) {//自定义人力资源个人信息表
                        recordSet.executeQuery("select " + oajkzd + " from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid= 1 and id in (select id from hrmresource where workcode = " + zgh + ")");
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    } else if ("5".equals(oazdwz)) {//自定义人员工作信息
                        recordSet.executeQuery("select " + oajkzd + " from cus_fielddata where scope='HrmCustomFieldByInfoType' and scopeid= 3 and id in (select id from hrmresource where workcode = " + zgh + ")");
                        if (recordSet.next()) {
                            if (sjztjkzdz.equals(recordSet.getString(oajkzd))) {

                            } else {
                                return false;
                            }
                        }
                    }
                }

            }
        } catch (Exception e) {

        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return true;
    }

    private Map<String, List<JSONObject>> filterFdyInfos(JSONArray fdyInfos) {
        // 结果集：有效数据 + 分类的无效数据
        Map<String, List<JSONObject>> result = new HashMap<>();
        List<JSONObject> validFdyList = new ArrayList<>();       // 有效数据（1班级1有效辅导员）
        List<JSONObject> noFdyList = new ArrayList<>();          // 无辅导员的班级（BJDM无数据）
        List<JSONObject> multiFdyList = new ArrayList<>();       // 多辅导员的班级（BJDM重复）
        List<JSONObject> emptyZghFdyList = new ArrayList<>();    // 职工号为空的辅导员

        try {
            // 1. 按班级代码分组
            Map<String, List<JSONObject>> classToFdyMap = new HashMap<>();
            for (int i = 0; i < fdyInfos.size(); i++) {
                JSONObject fdy = (JSONObject) fdyInfos.get(i);
                String bjdm = fdy.getString("BJDM");
                classToFdyMap.computeIfAbsent(bjdm, k -> new ArrayList<>()).add(fdy);
            }

            // 2. 遍历分组数据，分类处理
            for (Map.Entry<String, List<JSONObject>> entry : classToFdyMap.entrySet()) {
                String bjdm = entry.getKey();
                List<JSONObject> fdyList = entry.getValue();

                // 情况1：班级无辅导员（理论上不会发生，因分组后至少1条）
                if (fdyList.isEmpty()) {
                    noFdyList.add(new JSONObject().fluentPut("BJDM", bjdm));
                    continue;
                }

                // 情况2：班级有多个辅导员
                if (fdyList.size() > 1) {
                    // 检查是否只有1个辅导员有有效职工号
                    List<JSONObject> validFdysInGroup = fdyList.stream()
                            .filter(fdy -> StringUtils.isNotBlank(Util.null2String(fdy.get("ZGH"))))
                            .collect(Collectors.toList());

                    if (validFdysInGroup.size() == 1) {
                        // 只有1个有效职工号，视为有效数据
                        validFdyList.add(validFdysInGroup.get(0));
                    } else {
                        // 多个辅导员或无有效职工号，全部标记为无效
                        multiFdyList.addAll(fdyList);
                    }
                    continue;
                }

                // 情况3：班级只有1个辅导员
                JSONObject fdy = fdyList.get(0);
                if (StringUtils.isBlank(Util.null2String(fdy.get("ZGH")))) {
                    emptyZghFdyList.add(fdy);  // 职工号为空
                } else {
                    validFdyList.add(fdy);     // 有效数据
                }
            }

            // 3. 记录分类结果
            result.put("valid", validFdyList);
            result.put("noFdy", noFdyList);
            result.put("multiFdy", multiFdyList);
            result.put("emptyZgh", emptyZghFdyList);
        } catch (Exception e) {
            log.error("过滤辅导员数据异常", e);
        }

        return result;
    }

    private void _init() {
        token = "";
        config = null;
        mainConfig = null;
        detailConfig = null;
        hrmMap = null;
        departMap = null;
        departObjMap = null;
        subcompanyMap = null;
    }

    private String _reset() {
        //请求参数校验
        if (StringUtils.isBlank(tableName)) return "辅导员配置表名未配置!";
//        if (StringUtils.isBlank(bh)) return "组织架构同步接口总配置编号未配置!";

        //2.1 获取组织架构接口同步总配置
        config = getTotalConfig();
        if (config == null) return "获取组织架构接口同步总配置失败!";
        log.info("组织架构接口同步总配置: " + JSONObject.toJSONString(config));

        //2.2 获取token
        token = getToken(config);
        if (StringUtils.isBlank(token)) return "调用中转平台获取token接口，获取token失败!";
        log.info("中台系统token: " + token);

        //2.3 获取辅导员主配置
        String mainSql = "select * from " + tableName;
        mainConfig = getMainBaseInfo(mainSql);
        if (mainConfig == null) return "获取辅导员主配置失败。";
        log.info("辅导员主配置: " + JSONObject.toJSONString(mainConfig));

        //2.4 获取辅导员明细配置
        String mainId = Util.null2String(mainConfig.get("id"));
        if (StringUtils.isNotBlank(mainId)) {
            String detailsSql = "select * from " + tableName + "_dt1 where 1=1 and mainid = " + mainId;
            detailConfig = getDetailBaseInfo(detailsSql);
        }
        log.info("辅导员明细配置: " + JSONObject.toJSONString(detailConfig));

        //2.5 设置OA人力资源和部门标准表和自定义表信息
        setHrmMaps();
        log.info("设置的hrmMap信息：" + JSONObject.toJSONString(hrmMap));
        setDepartMaps();
        log.info("设置的departMap信息： " + JSONObject.toJSONString(departMap));
        setDepartObjMaps();
        log.info("设置的departMap信息： " + JSONObject.toJSONString(departObjMap));
        setsubcompanyMaps();
        log.info("设置的subcompanyMap信息： " + JSONObject.toJSONString(subcompanyMap));
        return "";
    }

    private void setDepartMaps() {
        try {
            departMap = new HashMap<>();
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            // 1. 查询数据库
            if (rs.executeQuery("select id,departmentcode from  hrmdepartment")) { // 确保查询包含id字段
                while (rs.next()) {
                    departMap.put(Util.null2String(rs.getString("id")), Util.null2String(rs.getString("departmentcode")));
                }
            } else {
                log.info("hrmdepartment表查询结果为空");
            }
        } catch (Exception e) {
            log.error("设置departMap异常:  " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet(); // 确保资源释放
        }
    }

    private void setsubcompanyMaps() {
        try {
            subcompanyMap = new HashMap<>();
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            // 1. 查询数据库
            if (rs.executeQuery("select * from  hrmsubcompany")) { // 确保查询包含id字段
                while (rs.next()) {
                    subcompanyMap.put(Util.null2String(rs.getString("id")), Util.null2String(rs.getString("subcompanycode")));
                }
            } else {
                log.info("hrmsubcompany表查询结果为空");
            }
        } catch (Exception e) {
            log.error("设置分部Map异常:  " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet(); // 确保资源释放
        }
    }

    private void setDepartObjMaps() {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            // 1. 查询数据库
            if (rs.executeQuery("select a.*,b.bjdm FROM hrmdepartment a inner join hrmdepartmentdefined b on a.id = b.deptid")) { // 确保查询包含id字段
                // 2. 转换为List<Map>
                List<Map<String, Object>> departList = QueryUtil.getMapList(rs);
                // 3. 手动遍历List，构建Map<ID, Object>
                departObjMap = new HashMap<>(); // 清空旧数据
                for (Map<String, Object> depart : departList) {
                    String departmentcode = Util.null2String(depart.get("bjdm")); // 获取ID作为Key
                    if (departObjMap.containsKey(departmentcode)) {
//                        log.info("发现重复departmentcode : " + departmentcode + "，保留旧值 "); // 可选：记录重复ID警告
                    } else {
                        departObjMap.put(departmentcode, depart); // 存入Map
                    }
                }
            } else {
                log.info("hrmdepartment表查询结果为空");
            }
        } catch (Exception e) {
            log.error("设置部门Map异常:  " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet(); // 确保资源释放
        }
    }

    private void setHrmMaps() {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            // 1. 查询数据库
            if (rs.executeQuery("select * FROM hrmresource")) { // 确保查询包含id字段
                // 2. 转换为List<Map>
                List<Map<String, Object>> hrmList = QueryUtil.getMapList(rs);
                // 3. 手动遍历List，构建Map<ID, Object>
                hrmMap = new HashMap<>(); // 清空旧数据
                for (Map<String, Object> employee : hrmList) {
                    String workcode = Util.null2String(employee.get("WORKCODE")); // 获取ID作为Key
                    if (hrmMap.containsKey(workcode)) {
//                        log.info("发现重复: " + workcode + "，保留旧值 "); // 可选：记录重复ID警告
                    } else {
                        hrmMap.put(workcode, employee); // 存入Map
                    }
                }
            } else {
                log.info("hrmresource表查询结果为空");
            }
        } catch (Exception e) {
            log.error("设置人力资源Map异常:  " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet(); // 确保资源释放
        }
    }

    //获取组织架构接口同步总配置
    public SyncConfig getTotalConfig() {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            String sql = "select * from uf_zzjgtb ";
            if (rs.executeQuery(sql)) {
                return QueryUtil.getObj(rs, SyncConfig.class);
            }
        } catch (Exception e) {
            log.error("获取组织架构接口同步总配置Exception: " + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return null;
    }

    //获取token
    public String getToken(SyncConfig config) {
        String token = "";
        try {
            String urlAdd = config.getJkzydz() + config.getTokenjkdz();
            String username = config.getJkyhm();
            String password = config.getJkmm();
            String url = urlAdd + "?username=" + username + "&password=" + password;
            log.info("获取token请求url" + url);
            RestResult tokenResult = HttpUtil.getData(url);
            log.info("接口返回的tokenResult信息" + JSON.toJSONString(tokenResult));
            String body = tokenResult.getResponseInfo().getBody();
            if (tokenResult.isSuccess() && StringUtils.isNotBlank(body)) {
                token = (String) JSON.parseObject(body).get("token");
            }
        } catch (Exception e) {
            log.error("调用中转平台获取token接口,获取token Exception: " + SDUtil.getExceptionDetail(e));
        }
        return token;
    }
    //获取辅导员配置

    public JSONObject getMainBaseInfo(String sql) {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            JSONObject jsonObject = new JSONObject();
            rs.execute(sql);
            while (rs.next()) {
                String[] columnNames = rs.getColumnName();
                if (columnNames != null && columnNames.length > 0) {
                    for (String columnName : columnNames) {
                        jsonObject.put(columnName, rs.getString(columnName));
                    }
                }
                break;
            }
            return jsonObject;
        } catch (Exception e) {
            log.info("获取建模表中配置表的信息getMainBaseInfo:" + sql + "出错！错误信息:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return null;
    }

    public JSONArray getDetailBaseInfo(String sql) {
        try {
            RecordSet rs = DBUtil.getThreadLocalRecordSet();
            JSONArray JA = new JSONArray();
            String[] columnNames = null;
            rs.execute(sql);
            while (rs.next()) {
                JSONObject jsonObject = new JSONObject();
                if (columnNames == null || columnNames.length <= 0) {
                    columnNames = rs.getColumnName();
                }
                if (columnNames != null && columnNames.length > 0) {
                    for (String columnName : columnNames) {
                        jsonObject.put(columnName, rs.getString(columnName));
                    }
                    JA.add(jsonObject);
                }
            }
            return JA;
        } catch (Exception e) {
            log.info("获取建模表中配置表的信息getDetailBaseInfo:" + sql + "出错！错误信息:" + SDUtil.getExceptionDetail(e));
        } finally {
            DBUtil.clearThreadLocalRecordSet();
        }
        return null;
    }

    public JSONArray getZtFdyInfo() {
        JSONArray departments = new JSONArray();
        try {
            String url = config.getJkzydz() + mainConfig.getString("jkdz");
            boolean flag = true;
            int page = 1;
            Map<String, String> headers = new HashMap<>();
            headers.put("Accept", "application/json, text/plain, */*");
            headers.put("X-Token", token);
            JSONObject bodyParam = new JSONObject();
            JSONArray filters = new JSONArray();
            if (StringUtils.isNotBlank(handSync)) {
                //手动同步
                JSONObject filter = new JSONObject();
                filter.put("name", mainConfig.getString("jkzlzd"));
                filter.put("operator", "GTE");
                filter.put("value", handSync + "T00:00:00.000+0000");
                filters.add(filter);
            } else {
                //自动同步
                JSONObject filter = new JSONObject();
                filter.put("name", mainConfig.getString("jkzlzd"));
                filter.put("operator", "GTE");
                filter.put("value", TimeUtil.dateAdd(TimeUtil.getCurrentDateString(),-2) + "T00:00:00.000+0000");
                filters.add(filter);
            }
            bodyParam.put("filter", filters); // 空数组
            bodyParam.put("rows", 1000);
            bodyParam.put("sidx", "cityosLandingTs");
            bodyParam.put("sord", "asc");
            bodyParam.put("page", page);
            log.info("bodyParam : "+JSON.toJSONString(bodyParam));
            do {
                //发送分页 post请求 获取辅导员基本数据
                bodyParam.put("page", page);
                RestResult restResult = HttpUtil.postDataWithHeader(url, headers, bodyParam.toJSONString());
                log.info("分页获取辅导员信息: page : " + page + " : " + JSONObject.toJSONString(restResult));
                String body = restResult.getResponseInfo().getBody();
                if (restResult.isSuccess() && StringUtils.isNotBlank(body)) {
                    JSONObject jb = JSON.parseObject(body);
                    JSONArray rows = jb.getJSONArray("rows");
                    departments.addAll(rows);
                    if (String.valueOf(page).equals(jb.getString("totalPage")) || "0".equals(jb.getString("totalPage"))) {
                        flag = false;
                    } else {
                        page++;
                    }
                } else {
                    flag = false;
                }
            } while (flag);
        } catch (Exception e) {
            log.info("getZtFdyInfo Exception:" + SDUtil.getExceptionDetail(e));
        }
        return departments;
    }

    private void writeMainLog(SyncFDYLog syncFDYLog) {
        try {
            syncFDYLog.setSync_end_date(TimeUtil.getCurrentDateString());
            syncFDYLog.setSync_end_time(TimeUtil.getCurrentTimeString());
            //插入主表日志
            int logModId = ModuleDataUtil.getModuleIdByName("uf_fdy_log");
            ModuleResult mr = ModuleDataUtil.insertObj(syncFDYLog, "uf_fdy_log", logModId, 1);
            log.info("插入uf_fdy_log表响应结果： " + JSONObject.toJSONString(mr));
        } catch (Exception e) {
            log.info("同步组织结构日志 Exception: " + SDUtil.getExceptionDetail(e));
        }
    }


}






