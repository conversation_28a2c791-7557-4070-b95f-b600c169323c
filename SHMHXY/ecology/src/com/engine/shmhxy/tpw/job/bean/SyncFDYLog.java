package com.engine.shmhxy.tpw.job.bean;

import lombok.Data;

import java.time.Duration;

/**
 * @FileName SyncFDYLog
 * @Description 辅导员同步日志
 * <AUTHOR>
 * @Version v1.00
 * @Date 2025/4/18
 */
@Data
public class SyncFDYLog {
    /**
     * 同步开始日期
     */
    private String sync_begin_date;
    /**
     * 同步结束日期
     */
    private String sync_end_date;
    /**
     * 同步开始时间
     */
    private String sync_begin_time;
    /**
     * 同步结束时间
     */
    private String sync_end_time;
    /**
     * 同步结果
     */
    private int result;
    /**
     * 同步总数量
     */
    private int sync_counts;
    /**
     * 中台接口获取辅导员数据
     */
    private String originalData;
    /**
     * 中台辅导员过滤后数据
     */
    private String afterFilterData;
    /**
     * 部门同步参数
     */
    private String deptSyncData;
    /**
     * 同步失败信息
     */
    private String sync_err_info;
    /**
     * 备注
     */
    private String bz;

}
