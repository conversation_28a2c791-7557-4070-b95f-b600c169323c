package com.engine.shmhxy.tpw.job.oldSyncHrm;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.hrm.util.face.hrmrestful.service.HrmRestFulFromWebServiceManager;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import org.apache.commons.lang3.StringUtils;
import weaver.general.TimeUtil;
import weaver.general.Util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class SyncTeacherHrm {
    private static final Logger log = LoggerFactory.getLogger(SyncTeacherHrm.class);
    /**
     * HRM restful 同步类
     */
    private static HrmRestFulFromWebServiceManager manager = new HrmRestFulFromWebServiceManager();

    //同步教师人员信息
    public void syncTeacherHrmInfo(String token, JSONObject tc, String subcompanycode, String uuid) {
        List<SFSyncDetailLog> syncDetailList = new ArrayList<>();
        SFSyncLog sfSyncLog = new SFSyncLog();
        ArrayList<Object> errList = new ArrayList<>();
        int tbjg = 0;
        //获取开始时间
        long startTime = System.currentTimeMillis();
        //同步批次号
        sfSyncLog.setPch(uuid);
        //同步类型
        sfSyncLog.setTblx(2);
        sfSyncLog.setBz("查看日志请在当前服务器地址后面拼接/log/sd/sd.log，例如：http://122.51.232.134:8080/log/sd/sd.log ，将页面保存至本地，按照请求/响应报文中的UUID即可搜索查看");
        //同步日期
        sfSyncLog.setTbrq(TimeUtil.getCurrentDateString());
        //同步开始时间
        sfSyncLog.setTbkssj(TimeUtil.getCurrentTimeString());
        ArrayList<Object> errMsgList = new ArrayList<>();
        //缺失
        ArrayList<Object> lackWorkcodeList = new ArrayList<>();
        SFSyncDetailLog sfSyncDetailLogZt = new SFSyncDetailLog();
        SFSyncDetailLog sfSyncDetailLogOA = new SFSyncDetailLog();
        syncDetailList.add(sfSyncDetailLogZt);
        syncDetailList.add(sfSyncDetailLogOA);
        try {
            if(StringUtils.isNotBlank(tc.getString("id"))){
                sfSyncLog.setZpz(Integer.parseInt(tc.getString("id")));
            }
            //获取第三方所有的教师人员信息
            //【教职工】只需同步“状态”为“01”的在职人员
            JSONArray filerArray = new JSONArray();
            JSONObject filerObject = new JSONObject();
            filerObject.put("name","ZT");
            filerObject.put("operator","EQ");
            filerObject.put("value","01");
            filerArray.add(filerObject);
            JSONArray teacherHrmInfo = Utils.getThirdHrmInfoWithPage(errMsgList,token, "uf_lsrytb", sfSyncDetailLogZt);

//            JSONArray teacherHrmInfo = Utils.getThirdHrmInfoWithPage2(errMsgList,token, "uf_lsrytb", sfSyncDetailLogZt,filerArray);
//            JSONArray jobtitleInfos = Utils.getThirdHrmInfoWithPage(errMsgList,token, "uf_xxgwtbjkpz", sfSyncDetailLogZt);
//            HashMap<String, String> jobtitleMap = new HashMap<>();
//            if(!jobtitleInfos.isEmpty()){
//                for (int i = 0; i < jobtitleInfos.size(); i++) {
//                    JSONObject jobtitleInfo = (JSONObject) jobtitleInfos.get(i);
//                    String key = Util.null2String(jobtitleInfo.get("YHXX"));
//                    String value = Util.null2String(jobtitleInfo.get("GWLB"));
//                    jobtitleMap.put(key, value);
//                }
//            }
//            log.info("从第三方获取到的jobtitleMap==>"+JSONObject.toJSONString(jobtitleMap));

            //得到教师配置主表信息
            JSONObject tm = Utils.getMainBaseInfo("select * from uf_lsrytb");
            //log.info("得到教师配置主表信息"+JSONObject.toJSONString(tm));
            //得到教师配置主表信息
            JSONArray OADeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartmentdefined");

            String jkzlzd = Util.null2String(tm.getString("jkzlzd"));
            String oazlzd = Util.null2String(tm.getString("oazlzd"));
            String oazlzdwz = Util.null2String(tm.getString("oazlzdwz"));
            String jkyjbmzd = Util.null2String(tm.getString("jkyjbmzd"));
            String jkejbmzd = Util.null2String(tm.getString("jkejbmzd"));
            String xzzpz = Util.null2String(tm.getString("xzzpz"));

            //同步总配置表
            JSONObject mainBaseInfo = Utils.getMainBaseInfo("select * from  uf_zzjgtb where id =" + xzzpz);
            String djbmszfb = (String) mainBaseInfo.get("djbmszfb");
            //判断OA增量字段的位置再那张表中
            String formName = "";
            String sqlWhere = "";

            if ("0".equals(oazlzdwz)) {
                formName = "HrmResource";
            } else if ("1".equals(oazlzdwz)) {
                formName = " cus_fielddata ";
                sqlWhere = " where scope = 'HrmCustomFieldByInfoType' and scopeid = -1";
            } else if ("2".equals(oazlzdwz)) {
                formName = " cus_fielddata ";
                sqlWhere = " where scope = 'HrmCustomFieldByInfoType' and scopeid = 1 ";
            } else {
                formName = " cus_fielddata ";
                sqlWhere = " where scope = 'HrmCustomFieldByInfoType' and scopeid = 3 ";
            }

            JSONArray hrmResourceArray = Utils.getDetailBaseInfo("select * from HrmResource");
            JSONArray hrmResourceCustArray = Utils.getDetailBaseInfo("select * from " + formName + sqlWhere);
            List<Map<String, Object>> hrmData = new ArrayList<>();
            //根据增量字段筛选要同步的老师数据
            for (int i = 0; i < teacherHrmInfo.size(); i++) {
                JSONObject teacherObject = teacherHrmInfo.getJSONObject(i);
                for (int j = 0; j < hrmResourceArray.size(); j++) {
                    JSONObject hr = (JSONObject) hrmResourceArray.get(j);
                    boolean sameHrmflag = false;
                    if (!"".equals(sqlWhere)) {
                        //增量字段在自定表当中
                        if (Util.null2String(hr.getString("WORKCODE")).equals(teacherObject.getString("GH"))) {
                            for (int k = 0; k < hrmResourceCustArray.size(); k++) {
                                JSONObject jo = (JSONObject) hrmResourceCustArray.get(k);
                                if (jo.get("ID").equals(hr.getString("ID")) && teacherObject.getString(jkzlzd).equals(jo.getString(oazlzd))) {
                                    //移除中台数据不同步的人员信息
                                    teacherHrmInfo.remove(i);
                                    i--;
                                    sameHrmflag = true;
                                    break;
                                }
                            }
                            if (sameHrmflag) {
                                break;
                            }
                        }
                    } else {
                        if (Util.null2String(hr.getString("WORKCODE")).equals(teacherObject.getString("GH")) && Util.null2String(teacherObject.getString(jkzlzd)).equals(hr.getString(oazlzd))) {
                            //移除第三方不同步的人员信息
                            teacherHrmInfo.remove(i);
                            i--;
                            break;
                        }
                    }
                }
            }

            //老师要同步到的分部
            JSONObject fbcode = new JSONObject();
            fbcode.put("subcompanycode", subcompanycode);
            String fb = "{JSON}" + JSONObject.toJSONString(fbcode);

            //判断老师要分配到的OA部门
            for (int i = 0; i < teacherHrmInfo.size(); i++) {
                Map<String, Object> hrm = new HashMap<>();
                hrm.put("subcompany", fb);
                JSONObject teacherObject = teacherHrmInfo.getJSONObject(i);
                String YJBM = teacherObject.getString(jkyjbmzd);
                String EJBM = teacherObject.getString(jkejbmzd);
                String bm = "";
                if (StringUtils.isBlank(EJBM)) {
                    //比较接口部门字段的值与在OA部门自定义字段【系统ID】相等
                    for (int j = 0; j < OADeptInfo.size(); j++) {
                        JSONObject oaDept = (JSONObject) OADeptInfo.get(j);
                        if (YJBM.equals(oaDept.getString("xtid"))) {
                            //老师要同步到的部门
                            JSONObject departmentcode = new JSONObject();
                            departmentcode.put("departmentcode", YJBM);
                            bm = "{JSON}" + JSONObject.toJSONString(departmentcode);
                            hrm.put("department", bm);
                            //拼装请求参数 AssReqParams
                            assReqParams(hrm, teacherObject, tc);
                            //todo
//                            assReqParams2(hrm, teacherObject, tc,jobtitleMap);
                            break;
                        }
                    }
                } else {
                    for (int j = 0; j < OADeptInfo.size(); j++) {
                        JSONObject oaDept = (JSONObject) OADeptInfo.get(j);
                        if (EJBM.equals(Util.null2String(oaDept.get("xtid")))) {
                            //老师要同步到的部门
                            JSONObject departmentcode = new JSONObject();
                            departmentcode.put("departmentcode", EJBM);
                            bm = "{JSON}" + JSONObject.toJSONString(departmentcode);
                            hrm.put("department", bm);
                            assReqParams(hrm, teacherObject, tc);
                            //todo
//                            assReqParams2(hrm, teacherObject, tc,jobtitleMap);
                            break;
                        }
                    }
                }
                if(StringUtils.isBlank(Util.null2String(hrm.get("workcode")))){
                    lackWorkcodeList.add(teacherObject);
                }else {
                    hrmData.add(hrm);
                }

            }
            String resMsg = syncHrmData(hrmData,errMsgList,sfSyncDetailLogOA);
            //同步信息
            String tbxxUUID = UUID.randomUUID().toString();
            JSONObject tBTeacherInfo = new JSONObject();
            tBTeacherInfo.put("--本次同步的数量--",hrmData.size());
            int errCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList),"失败");
            int expCount = Utils.countOccurrences(JSONObject.toJSONString(errMsgList),"syncHrmData异常");
            tBTeacherInfo.put("--本次同步失败的数量--",errCount+expCount);
            tBTeacherInfo.put("--本次同步请求参数信息--",tbxxUUID);
            sfSyncLog.setTbxx(JSONObject.toJSONString(tBTeacherInfo));

            log.info("同步老师信息："+tbxxUUID+"同步信息:"+JSONObject.toJSONString(hrmData));
        } catch (Exception e) {
            log.info("同步教师数据异常信息" + SDUtil.getExceptionDetail(e));
            tbjg = 1;
        }
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫转换为秒
        //同步结束时间
        sfSyncLog.setTbjssj(TimeUtil.getCurrentTimeString());
        //耗时（秒）
        sfSyncLog.setTbhs(Util.null2String(String.valueOf(elapsedSeconds)));
        if(tbjg == 0 && sfSyncDetailLogZt.getJkjg() == 0 && sfSyncDetailLogOA.getJkjg() == 0 ){
            sfSyncLog.setTbjg(0);
        }else{
            sfSyncLog.setTbjg(1);
        }
        //同步失败信息
//        String tbsbUUID = UUID.randomUUID().toString();
        sfSyncLog.setSbxx(JSONObject.toJSONString(lackWorkcodeList)+"-------------"+JSONObject.toJSONString(errMsgList));
//        log.info("同步老师失败信息："+tbsbUUID+"失败信息："+JSONObject.toJSONString(errMsgList));
        //插入日志
        SFSyncLogUtil.insertLog(sfSyncLog, syncDetailList);
    }
    //拼装请求参数 AssReqParams
    private void assReqParams2(Map<String, Object> hrm, JSONObject teacherObject, JSONObject tc,HashMap<String, String> jobtitleMap) throws ParseException {
        try{
            JSONObject base_custom_data = new JSONObject();
            JSONObject person_custom_data = new JSONObject();
            JSONObject work_custom_data = new JSONObject();

            String jobgroupid = tc.getString("ryzwlb");
            String jobactivityid = tc.getString("ryzw");
            hrm.put("jobgroupid", jobgroupid);
            hrm.put("jobactivityid", jobactivityid);
            //获取教师明细表配置
            JSONArray teacherFormDetails = Utils.getDetailBaseInfo("select * from uf_lsrytb_dt1");
            for (int i = 0; i < teacherFormDetails.size(); i++) {
                JSONObject jo = (JSONObject) teacherFormDetails.get(i);
                String oazdwz = Util.null2String(jo.getString("oazdwz"));
                String oajkzd = Util.null2String(jo.getString("oajkzd"));
                //存放的位置
                switch (oazdwz) {
                    case "1":
                        //base_custom_data
                        Utils.custFields2(jo, teacherObject, base_custom_data,jobtitleMap);
                        break;
                    case "2":
                        //person_custom_data
                        Utils.custFields(jo, teacherObject, person_custom_data);
                        break;
                    case "3":
                        //work_custom_data
                        Utils.custFields(jo, teacherObject, work_custom_data);
                        break;
                    default:
                        hrm.put(oajkzd, Utils.UtilParams(jo, teacherObject));
                }
                String ZTInteValue = Utils.UtilParams(jo, teacherObject);
                if("companystartdate".equals(oajkzd) || "startdate".equals(oajkzd)){
                    // 定义输入日期格式
                    SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyyMMdd");
                    // 定义输出日期格式
                    SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if(StringUtils.isNotBlank(ZTInteValue)){
                        Date date = inputDateFormat.parse(ZTInteValue);
                        String outputDateString = outputDateFormat.format(date);
                        hrm.put(oajkzd,outputDateString );
                    }
                }
            }
            hrm.put("base_custom_data", base_custom_data);
            hrm.put("person_custom_data", person_custom_data);
            hrm.put("work_custom_data", work_custom_data);
        }catch (Exception e){
            log.info("凭借参数出错"+SDUtil.getExceptionDetail(e));
        }
    }
    //拼装请求参数 AssReqParams
    private void assReqParams(Map<String, Object> hrm, JSONObject teacherObject, JSONObject tc) throws ParseException {
        try{
            JSONObject base_custom_data = new JSONObject();
            JSONObject person_custom_data = new JSONObject();
            JSONObject work_custom_data = new JSONObject();

            String jobgroupid = tc.getString("ryzwlb");
            String jobactivityid = tc.getString("ryzw");

            //获取教师明细表配置
            JSONArray teacherFormDetails = Utils.getDetailBaseInfo("select * from uf_lsrytb_dt1");
            for (int i = 0; i < teacherFormDetails.size(); i++) {
                JSONObject jo = (JSONObject) teacherFormDetails.get(i);
                String oazdwz = Util.null2String(jo.getString("oazdwz"));
                String oajkzd = Util.null2String(jo.getString("oajkzd"));
                //存放的位置
                switch (oazdwz) {
                    case "1":
                        //base_custom_data
                        Utils.custFields(jo, teacherObject, base_custom_data);
                        break;
                    case "2":
                        //person_custom_data
                        Utils.custFields(jo, teacherObject, person_custom_data);
                        break;
                    case "3":
                        //work_custom_data
                        Utils.custFields(jo, teacherObject, work_custom_data);
                        break;
                    default:
                        String val = Utils.UtilParams(jo, teacherObject);
                        if("jobtitle".equals(oajkzd)){
                            if(StringUtils.isBlank(val) ){
                                hrm.put(oajkzd, "教师岗位");
                                hrm.put("jobgroupid", jobgroupid);
                                hrm.put("jobactivityid", jobactivityid);
                            }else {
                                hrm.put(oajkzd, val);
                                hrm.put("jobgroupid", jobgroupid);
                                hrm.put("jobactivityid", jobactivityid);
                            }
                        }else {
                            if(StringUtils.isNotBlank(val)){
                                hrm.put(oajkzd, val);
                            }

                        }

                }
                String ZTInteValue = Utils.UtilParams(jo, teacherObject);
                if("companystartdate".equals(oajkzd) || "startdate".equals(oajkzd)){
                    // 定义输入日期格式
                    SimpleDateFormat inputDateFormat = new SimpleDateFormat("yyyyMMdd");
                    // 定义输出日期格式
                    SimpleDateFormat outputDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    if(StringUtils.isNotBlank(ZTInteValue)){
                        Date date = inputDateFormat.parse(ZTInteValue);
                        String outputDateString = outputDateFormat.format(date);
                        hrm.put(oajkzd,outputDateString );
                    }
                }
            }
            hrm.put("base_custom_data", base_custom_data);
            hrm.put("person_custom_data", person_custom_data);
            hrm.put("work_custom_data", work_custom_data);
        }catch (Exception e){
            log.info("凭借参数出错"+SDUtil.getExceptionDetail(e));
        }
    }

    /**
     * 调用标准rest接口方法，同步人员
     *
     * @param hrmData
     */
    @SuppressWarnings("unchecked")
    private String syncHrmData(List<Map<String, Object>> hrmData, ArrayList errList, SFSyncDetailLog sfSyncDetailLogOA) {
        if (sfSyncDetailLogOA == null) {
            sfSyncDetailLogOA = new SFSyncDetailLog();
        }
        long startTime = System.currentTimeMillis(); // 获取结束时间
        //请求时间
        sfSyncDetailLogOA.setQqsj(TimeUtil.getCurrentTimeString());
        if(errList == null){
            errList = new ArrayList();
        }
        Map<String, Map<String, String>> restResult;
        List<Map<String, Object>> batchData;
        String errorMsg = "";
        ArrayList<Object> xybw = new ArrayList<>();
        try {
            int batchSize = 1000; // 每批次处理的最大数量
            int totalSize = hrmData.size();
            for (int i = 0; i < totalSize; i += batchSize) {
                int endIndex = Math.min(i + batchSize, totalSize);
                batchData = hrmData.subList(i, endIndex);
                // 1、执行同步
                restResult = manager.synHrmresource("workcode", batchData);
                xybw.add(JSONObject.toJSONString(restResult));
                if (restResult.toString().contains("失败")) {
//                    Utils.countOccurrences("失败", restResult.toString());
                    errList.add(JSONObject.toJSONString(restResult));
                    errorMsg = "失败";
                }
            }
        } catch (Exception e) {
            errorMsg = "syncHrmData异常：" + SDUtil.getExceptionDetail(e);
            errList.add(errorMsg);
        }
        //接口名
        sfSyncDetailLogOA.setJkm("OA同步老师人员");
        //请求报文
        String qquuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setQqbw(qquuid);
        log.info("OA同步老师请求报文信息: "+qquuid+" 请求报文信息:"+JSONObject.toJSONString(hrmData));
        //响应报文
        String xxuuid = UUID.randomUUID().toString();
        sfSyncDetailLogOA.setXybw(xxuuid);
        log.info("OA同步老师响应报文信息:"+xxuuid+" 响应报文信息:"+JSONObject.toJSONString(xybw));
        //响应时间
        sfSyncDetailLogOA.setXysj(TimeUtil.getCurrentTimeString());
        //接口耗时
        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫秒转换为秒
        sfSyncDetailLogOA.setJkhs(String.valueOf(elapsedSeconds));
        //接口结果
        if(StringUtils.isBlank(errorMsg)){
            sfSyncDetailLogOA.setJkjg(0);
        }else {
            sfSyncDetailLogOA.setJkjg(1);
        }
        return errorMsg;
    }
}

