package com.engine.shmhxy.tpw.job.bean;

import lombok.Data;

/**
    * @FileName SyncConfig
    * @Description 组织架构同步总配置类
    * <AUTHOR>
    * @Version v1.00
    * @Date 2025/3/21
    */
@Data
public class SyncConfig {
    // 编号
    private String bh;

    // 接口资源地址
    private String jkzydz;

    // token接口地址
    private String tokenjkdz;

    // 接口用户名
    private String jkyhm;

    // 接口密码
    private String jkmm;

    // 顶级部门所属分部
    private String djbmszfb;

    // 备注
    private String bz;

    // 人员职务
    private String ryzw;

    // 人员职务类别
    private String ryzwlb;
}
