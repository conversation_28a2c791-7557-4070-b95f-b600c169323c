package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import com.engine.sd2.db.util.DBUtil;
import com.weaver.general.Util;

import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.general.TimeUtil;

import java.util.*;

public class SyncClassDept {

    private static final Logger log = LoggerFactory.getLogger(SyncClassDept.class);
    /**
     * 正常新增/更新的部门
     */
    List<List<Map<String, Object>>> deptData = new ArrayList<>();

    /**
     * 需要解封的部门
     */
    List<List<Map<String, Object>>> unDelData = new ArrayList<>();

    /**
     * 需要封存的部门
     */
    List<List<Map<String, Object>>> delData = new ArrayList<>();
    /**
     * "学生部门"的code
     */
    ArrayList<String> codes = new ArrayList<>();

    List<String> departmentCodes = new ArrayList();

    public String syncOaClass(String token, Map<String, String> canceledDeptMap, Map<String, String> normalDeptMap, String subcompanycode,String uuid, JSONObject tc,String flag) {
        init();
        SFSyncLog sfSyncLog = new SFSyncLog();
        List<SFSyncDetailLog> syncDetailList = new ArrayList<>();
        SFSyncDetailLog sfSyncDetailLog = new SFSyncDetailLog();
        SFSyncDetailLog sfSyncDetailLog1 = new SFSyncDetailLog();
        syncDetailList.add(sfSyncDetailLog);
        syncDetailList.add(sfSyncDetailLog1);

        ArrayList errResInfo = new ArrayList<>();
        JSONObject bodyParam = new JSONObject();

        //获取开始时间
        long startTime = System.currentTimeMillis();
        //同步批次号
        sfSyncLog.setPch(uuid);
        sfSyncLog.setBz("查看日志请在当前服务器地址后面拼接/log/sd/sd.log，例如：http://122.51.232.134:8080/log/sd/sd.log ，将页面保存至本地，按照请求/响应报文中的UUID即可搜索查看");
        if(StringUtils.isNotBlank(tc.getString("id"))){
            sfSyncLog.setZpz(Integer.parseInt(tc.getString("id")));
        }

        //同步类型
        sfSyncLog.setTblx(1);
        //同步日期
        sfSyncLog.setTbrq(TimeUtil.getCurrentDateString());
        //同步开始时间
        sfSyncLog.setTbkssj(TimeUtil.getCurrentTimeString());
        //同步响应结果
        String errMsg = "成功";

        log.info("班级获取token信息:" + token);
//        JSONArray classArr = new JSONArray();
//        JSONObject filerObject = new JSONObject();
//        filerObject.put("name","SFSY");
//        filerObject.put("operator","NE");
//        filerObject.put("value","0");
//        classArr.add(filerObject);

//        JSONArray oldClassArray = Utils.getThirdHrmInfoWithPage(errResInfo,token, "uf_bjbmtb",sfSyncDetailLog);
//        log.info("原始数据oldClassArray："+JSONObject.toJSONString(oldClassArray));
//        JSONArray classArray = Utils.getThirdHrmInfoWithPage2(errResInfo,token, "uf_bjbmtb",sfSyncDetailLog,classArr);
        JSONArray classArray = Utils.getThirdHrmInfoWithPage(errResInfo,token, "uf_bjbmtb",sfSyncDetailLog);
        log.info("new数据newClassArray："+JSONObject.toJSONString(classArray));
        //获取中台数据--学校部门数据
//        JSONArray deptArray = Utils.getThirdHrmInfoWithPage(null,token, "uf_xxbmtb",null);
//        JSONArray deptArr = new JSONArray();
//        JSONObject deptObject = new JSONObject();
//        deptObject.put("name","SFCX");
//        deptObject.put("operator","NE");
//        deptObject.put("value","1");
//        deptArr.add(deptObject);
//        JSONArray deptArray = Utils.getThirdHrmInfoWithPage2(null,token, "uf_xxbmtb",null,deptArr);
        JSONArray deptArray = Utils.getThirdHrmInfoWithPage(null,token, "uf_xxbmtb",null);
        //获取OA所有自定义字段部门信息
        JSONArray oaCustDeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartmentdefined");
        //获取OA所有部门信息
        JSONArray oaDeptInfo = Utils.getDetailBaseInfo("select * from hrmdepartment");
        //获取OA学校自定义字段部门信息
        JSONObject scm = Utils.getMainBaseInfo("select * from uf_xxbmtb");
        //新建“学生班级”的部门数据
        ArrayList stuClassList = new ArrayList<>();
        //班级主表配置信息
        JSONObject ccm = Utils.getMainBaseInfo("select * from uf_bjbmtb");
        //班级明细表配置信息
        JSONArray ccd = Utils.getDetailBaseInfo("select * from uf_bjbmtb_dt1");
        String jkjgbmzd = ccm.getString("jkjgbmzd");
        //该班级是否有所属于的部门
        for (int i = 0; i < classArray.size(); i++) {
            JSONObject cob = (JSONObject) classArray.get(i);
            for (int j = 0; j < deptArray.size(); j++) {
                JSONObject dob = (JSONObject) deptArray.get(j);
                //该班级有所属于的部门
                if(cob!=null && StringUtils.isNotBlank(jkjgbmzd) && dob!=null && scm!=null&& StringUtils.isNotBlank(cob.getString(jkjgbmzd))){
                    if (cob.getString(jkjgbmzd).equals(dob.getString(scm.getString("jkjgbmzd")))) {
                        boolean ifDept = true;
                        //接口部门系统ID
                        String XTID = dob.getString(scm.getString("jkxtidzd"));
                        String deptid = "";
                        for (int k = 0; k < oaCustDeptInfo.size(); k++) {
                            JSONObject oaCustDept = (JSONObject) oaCustDeptInfo.get(k);
                            if (XTID.equals(oaCustDept.get("xtid"))) {
                                deptid = oaCustDept.getString("DEPTID");
                                break;
                            }
                        }
                        for (int k = 0; k < oaDeptInfo.size(); k++) {
                            JSONObject oaDept = (JSONObject) oaDeptInfo.get(k);
                            //查看该OA部门下是否有“学生班级”这个部门
                            if (deptid.equals(oaDept.get("SUPDEPID")) && "学生班级".equals(oaDept.getString("DEPARTMENTNAME"))) {//“学生部门”不存在
                                ifDept = false;
                                break;
                            }
                        }
                        if(ifDept){
                            String code = XTID + "_XTID";
                            if (!codes.contains(code)) {
                                Map<String, Object> deptParam = Utils.buildDeptParam("add",
                                        code,
                                        "学生班级",
                                        XTID,//上级部门的系统id
                                        subcompanycode,
                                        "0",
                                        null);
                                stuClassList.add(deptParam);
                                codes.add(code);
                            }
                            break;
                        }
                        break;
                    }
                }
            }
        }
        ArrayList arrayList = new ArrayList();
        arrayList.add(stuClassList);
        ArrayList xybw = new ArrayList<>();


        //请求时间
        sfSyncDetailLog1.setQqsj(TimeUtil.getCurrentTimeString());
        long startOATime = System.currentTimeMillis();
        log.info("新建班级的数据：" + JSONObject.toJSONString(arrayList));
        errMsg = Utils.syncEachDeptData(arrayList, xybw, errResInfo);
        bodyParam.put("新建学生班级",arrayList);
        //同步班级
        deptFieldConverClass(ccm,deptArray,oaCustDeptInfo,classArray,scm,ccd,canceledDeptMap,normalDeptMap,subcompanycode,flag);
        String resMsg = sendSyncDeptReq(xybw,errResInfo,bodyParam);

        if(StringUtils.isBlank(errMsg) && StringUtils.isBlank(resMsg)){
            sfSyncDetailLog1.setJkjg(0);
        }else {
            sfSyncDetailLog1.setJkjg(1);
        }
        log.info("同步班级部门信息"+resMsg);
        //接口名

        sfSyncDetailLog1.setJkm("OA同步学校班级");

        //请求报文
        String qquuid = UUID.randomUUID().toString();
        sfSyncDetailLog1.setQqbw(qquuid);
        log.info("OA同步班级请求报文信息: "+qquuid+" 请求报文信息:"+JSONObject.toJSONString(bodyParam));
        //响应报文
        String xxuuid = UUID.randomUUID().toString();
        sfSyncDetailLog1.setXybw(xxuuid);
        log.info("OA同步班级响应报文: "+xxuuid+" 响应报文信息:"+JSONObject.toJSONString(xybw));
        //响应时间
        sfSyncDetailLog1.setXysj(TimeUtil.getCurrentTimeString());
        //接口耗时
        long endOATime = System.currentTimeMillis(); // 获取结束时间
        long elapsedOATime = endOATime - startOATime; // 计算时间差（毫秒）
        double elapsedOASeconds = elapsedOATime / 1000.0; // 将毫秒转换为秒
        sfSyncDetailLog1.setJkhs(String.valueOf(elapsedOASeconds));


        long endTime = System.currentTimeMillis(); // 获取结束时间
        long elapsedTime = endTime - startTime; // 计算时间差（毫秒）
        double elapsedSeconds = elapsedTime / 1000.0; // 将毫转换为秒
        //同步结束时间
        sfSyncLog.setTbjssj(TimeUtil.getCurrentTimeString());
        //耗时（秒）
        sfSyncLog.setTbhs(String.valueOf(elapsedSeconds));

        if(sfSyncDetailLog.getJkjg() == 0 && sfSyncDetailLog1.getJkjg() == 0){
            //同步结果成功
            sfSyncLog.setTbjg(0);
        }else {
            //同步结果失败
            sfSyncLog.setTbjg(1);
        }
        //同步信息
        String tbxxUUID = UUID.randomUUID().toString();
        JSONObject tBClassInfo = new JSONObject();
        int count  = 0;
        if(!unDelData.isEmpty()){
            List<Map<String, Object>> maps = unDelData.get(0);
            if(maps != null){
                count = maps.size();
            }
        }
        if(!deptData.isEmpty()){
            List<Map<String, Object>> maps = deptData.get(0);
            if(maps != null){
                count = count + maps.size();
            }
        }
        if(!delData.isEmpty()){
            List<Map<String, Object>> maps = delData.get(0);
            if(maps != null){
                count = count + maps.size();
            }
        }
        tBClassInfo.put("--本次同步的数量--",count);
        int errCount = Utils.countOccurrences(JSONObject.toJSONString(errResInfo),"失败");
        int expCount = Utils.countOccurrences(JSONObject.toJSONString(errResInfo),"syncEachDeptData异常");
        tBClassInfo.put("--本次同步失败的数量--",errCount+expCount);
        tBClassInfo.put("--本次同步请求参数信息--",tbxxUUID);
        sfSyncLog.setTbxx(JSONObject.toJSONString(tBClassInfo));
        log.info("同步学校班级："+tbxxUUID+"同步信息:"+JSONObject.toJSONString(bodyParam));
        //同步失败信息
        sfSyncLog.setSbxx(JSONObject.toJSONString(errResInfo));
        SFSyncLogUtil.insertLog(sfSyncLog, syncDetailList);
        return null;
    }

    //接口字段到OA同步接口字段之间的转换--班级
    private void deptFieldConverClass(JSONObject ccm,JSONArray deptArray,JSONArray oaCustDeptInfo, JSONArray classArray, JSONObject scm, JSONArray scd, Map<String, String> canceledDeptMap, Map<String, String> normalDeptMap, String subcompanycode,String flag) {


//        String xzzpz = scm.getString("xzzpz");
//        String jkzlzd = scm.getString("jkzlzd");
//        String oazlzd = scm.getString("oazlzd");
//        String oazlzdwz = scm.getString("oazlzdwz");
//        String jkjgbmzd = scm.getString("jkjgbmzd");
//        String jkxtidzd = scm.getString("jkxtidzd");
//        String jkfczd = scm.getString("jkfczd");
//        String jkfcz = scm.getString("jkfcz");

        String xzzpz = ccm.getString("xzzpz");
        String jkzlzd = ccm.getString("jkzlzd");
        String oazlzd = ccm.getString("oazlzd");
        String oazlzdwz = ccm.getString("oazlzdwz");
        String jkjgbmzd = ccm.getString("jkjgbmzd");
        String jkxtidzd = ccm.getString("jkxtidzd");
        String jkfczd = ccm.getString("jkfczd");
        String jkfcz = ccm.getString("jkfcz");

        //1.得到所有要更新的数据
        //需要解封的数据
        ArrayList<Map<String, Object>> unDelList = new ArrayList<>();
        //需要封存的部门
        ArrayList<Map<String, Object>> delList = new ArrayList<>();
        //正常新增/更新的部门
        ArrayList<Map<String, Object>> updataDeptList = new ArrayList<>();
        unDelData.add(unDelList);
        delData.add(delList);
        deptData.add(updataDeptList);


        //去除不同步的班级
        for (int j = 0; j < classArray.size(); j++) {
            JSONObject dept = (JSONObject) classArray.get(j);
            String XTID = dept.getString(jkxtidzd);
            String tcityosLandingTs = dept.getString(jkzlzd);
            for (int k = 0; k < oaCustDeptInfo.size(); k++) {
                JSONObject oadept = (JSONObject) oaCustDeptInfo.get(k);
                String oaXTID = (String) oadept.getString("xtid");
                String oazhgxrq = (String) oadept.getString(oazlzd);
                if (XTID.equals(oaXTID)) {
                    if (tcityosLandingTs.equals(oazhgxrq)) {
                        classArray.remove(j);
                        j--;
                    }
                    break;
                }
            }
        }


        //将要同步的班级
        for (int j = 0; j < classArray.size(); j++) {
            JSONObject cls = (JSONObject) classArray.get(j);
            Map<String, Object> deptParam = new HashMap<>();
            JSONObject custObject = new JSONObject();
            String infeFczdValue = Util.null2String(cls.getString(jkfczd));
            String code = "";
            String name = "";
            //找到该班级的上级部门，上级部门的系统ID+"_XTID"
            String partentCode = "";


            for (int k = 0; k < deptArray.size(); k++) {
                JSONObject dept = (JSONObject) deptArray.get(k);
                if(StringUtils.isNotBlank(jkjgbmzd) && StringUtils.isNotBlank(cls.getString(jkjgbmzd))){
                    if (cls.getString(jkjgbmzd).equals(dept.getString(scm.getString("jkjgbmzd")))) {
                        partentCode = dept.getString(ccm.getString("jkxtidzd"))+"_XTID";
                        break;
                    }
                }
            }
            Iterator iterator = scd.iterator();
            while (iterator.hasNext()) {
                JSONObject record = (JSONObject) iterator.next();
                String oajkzd = Util.null2String(record.getString("oajkzd"));
                String oazdwz = Util.null2String(record.getString("oazdwz"));
                if ("0".equals(oazdwz)) {
                    switch (oajkzd) {
                        case "code":
                            code = Utils.UtilParams(record, cls);
                            break;
                        case "shortname":
                            name = Utils.UtilParams(record, cls);
                            break;
                        case "fullname":
                            name = Utils.UtilParams(record, cls);
                            break;
                        default:
                    }
                } else {
                    Utils.custFields(record, cls, custObject);
                }
            }
            //判断班级是否解封还是封存
            //解封的班级
            if("first".equals(flag)){
                if (!infeFczdValue.equals(jkfcz)) { //解封
                    //查看系统中是否存在部门
                    //如果存在
                    if (departmentCodes.contains(code)) {
                        //
                        if(canceledDeptMap.containsKey(code)){
                            deptParam = Utils.buildDeptParam("delete",
                                    code,
                                    name,
                                    partentCode,
                                    subcompanycode,
                                    "1",
                                    custObject);
                            unDelList.add(deptParam);
                        }else {
                            deptParam = Utils.buildDeptParam("edit",
                                    code,
                                    name,
                                    partentCode,
                                    subcompanycode,
                                    "",
                                    custObject);
                            updataDeptList.add(deptParam);
                        }

                    } else {
                        deptParam = Utils.buildDeptParam("edit",
                                code,
                                name,
                                partentCode,
                                subcompanycode,
                                "",
                                custObject);
                        updataDeptList.add(deptParam);
                    }
                }
            }else {
                if (infeFczdValue.equals(jkfcz)) {
                    deptParam = Utils.buildDeptParam("delete",
                            code,
                            name,
                            partentCode,
                            subcompanycode,
                            "0",
                            custObject);
                    delList.add(deptParam);

                }
            }
        }
    }

    //根据不同的部门封存类型来发送部门同步请求
    private String sendSyncDeptReq(ArrayList xybw,ArrayList errResInfo,JSONObject bodyParam) {
        bodyParam.put("需要解封的部门",unDelData);
        bodyParam.put("需要封存的部门",delData);
        bodyParam.put("正常新增/更新的部门",deptData);

        String errorMsg = "";
        if (!unDelData.isEmpty()) {
            log.info("需要解封的部门");
            errorMsg = Utils.syncEachDeptData(unDelData,xybw,errResInfo);
        }
        // 2、执行封存
        if (errorMsg.isEmpty() && !delData.isEmpty()) {
            log.info("需要封存的部门");
            errorMsg = Utils.syncEachDeptData(delData,xybw,errResInfo);
        }
        // 3、执行新增/更新
        if (errorMsg.isEmpty() && !deptData.isEmpty()) {
            log.info("进入正常新增/更新的部门方法");
            errorMsg = Utils.syncEachDeptData(deptData,xybw,errResInfo);
        }
        return errorMsg;
    }
    public void init(){
        RecordSet recordSet = DBUtil.getThreadLocalRecordSet();
        String sql = "select * from hrmdepartment";
        List<Map<String, Object>> list;
        if (recordSet.executeQuery(sql)) {
            while (recordSet.next()) {
                departmentCodes.add(Util.null2String(recordSet.getString("departmentcode")));
            }
        }
    }
}
