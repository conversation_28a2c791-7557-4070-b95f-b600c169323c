package com.engine.shmhxy.tpw;

import com.alibaba.fastjson.JSONObject;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import weaver.conn.RecordSet;
import weaver.interfaces.schedule.BaseCronJob;

import java.util.*;


@Getter
@Setter
public class SyncHrmInfo extends BaseCronJob {
    /**
     * 组织架构同步接口总配置编号
     */
    private String bh;
    //日志
    private static final Logger log = LoggerFactory.getLogger(SyncHrmInfo.class);
    /**
     * 接口请求token信息
     */
    private String token;
    /**
     * 总配置信息
     */
    private JSONObject tc;
    /**
     * 存储被封存了的OA部门编码map
     */
    private Map<String, String> canceledDeptMap = new HashMap<>();
    /**
     * 存储正常状态的OA部门编码map
     */
    private Map<String, String> normalDeptMap = new HashMap<>();

    private static final RecordSet rs = new RecordSet();


    @Override
    public void execute() {
        log.info("同步上海民航职业技术学院信息开始");
        try {
            String uuid = UUID.randomUUID().toString();
            //1.获取组织架构同步接口总配置信息
            log.info("获取组织架构同步接口总配置信息开始!");
            tc = Utils.getMainBaseInfo("select * from uf_zzjgtb where bh = "+"'"+bh+"'");
            if (tc == null) {
                log.info("组织架构同步接口总配置TotalConfig信息为null 后续操作不再执行");
                return;
            }
            String subcompanycode = "";
            String fbIid = tc.getString("djbmszfb");
            rs.execute("select subcompanycode from hrmsubcompany where id = "+fbIid);
            while (rs.next()){
                subcompanycode = rs.getString("subcompanycode");
                break;
            }
            log.info("组织架构同步接口总配置TotalConfig" + tc);
            log.info("获取组织架构同步接口总配置息结束!");

            //2.获取token
            log.info("获取token开始!");
            token = Utils.getTokenInfo(tc);
            if (StringUtils.isBlank(token)) {
                log.info("获取token信息出错后续同步操作不再执行！");
                return;
            }
            log.info("获取token结束!");

            //3.同步学校部门信息
            log.info("同步学校部门信息开始!");
            putDeptMap();
            SyncSchoolDept syncSchoolDept = new SyncSchoolDept();
            String syncDeptResInfo = syncSchoolDept.syncSchoolDeptOaDept(canceledDeptMap,normalDeptMap, token, subcompanycode,uuid,tc,"first");
            if (StringUtils.isNotBlank(syncDeptResInfo)) {
                log.info("同步同步学校部门信息出错，后续同步操作不再执行！");
                return;
            }
            log.info("同步学校部门信息结束!");

            //4.同步学校班级部信息
            log.info("同步学校班级部信息开始!");
            SyncClassDept syncClassDept = new SyncClassDept();
            String classInfo = syncClassDept.syncOaClass(token, canceledDeptMap,normalDeptMap, subcompanycode,uuid,tc,"first");
            if (StringUtils.isNotBlank(classInfo)) {
                return;
            }
            log.info("同步学校班级部信息结束!");

            //同步教师基本数据
            log.info("同步教师基本数据信息开始!");
            SyncTeacherHrm syncTeacherHrm = new SyncTeacherHrm();
            syncTeacherHrm.syncTeacherHrmInfo(token,tc,subcompanycode,uuid);
            log.info("同步教师基本数据信息结束!");

            //同步学生基本数据
            log.info("同步学生基本数据息开始!");
            SyncStudentHrm syncStudentHrm = new SyncStudentHrm();
            syncStudentHrm.syncStudentHrmInfo(token,tc,subcompanycode,uuid);
            log.info("同步学生基本数据结束!");

            //5.同步学校部门信息
            log.info("同步学校部门信息开始!");
            putDeptMap();

            String syncDeptResInfo2 = syncSchoolDept.syncSchoolDeptOaDept(canceledDeptMap,normalDeptMap, token, subcompanycode,uuid,tc,"second");
            if (StringUtils.isNotBlank(syncDeptResInfo2)) {
                log.info("同步同步学校部门信息出错，后续同步操作不再执行！");
                return;
            }
            log.info("同步学校部门信息结束!");

            //6.同步学校班级部信息
            log.info("同步学校班级部信息开始!");
            String classInfo2 = syncClassDept.syncOaClass(token, canceledDeptMap,normalDeptMap, subcompanycode,uuid,tc,"second");
            if (StringUtils.isNotBlank(classInfo2)) {
                return;
            }
            log.info("同步学校班级部信息结束!");



        } catch (Exception e) {
            log.info("同步上海民航职业技术学院信息出错："+SDUtil.getExceptionDetail(e));
        }
        log.info("同步上海民航职业技术学院信息结束");
    }

    // 将被封存/正常的的OA部门push到map
    private void putDeptMap() {
        try {
            RecordSet rs = new RecordSet();
            String departmentcode, canceled;
            if (rs.executeQuery("select departmentcode,canceled from hrmdepartment ")) {
                while (rs.next()) {
                    departmentcode = weaver.general.Util.null2String(rs.getString("departmentcode"));
                    canceled = weaver.general.Util.null2String(rs.getString("canceled"));
                    //1为封存，
                    if ("1".equals(canceled)) {
                        canceledDeptMap.put(departmentcode, "");
                    } else {
                        normalDeptMap.put(departmentcode, "");
                    }
                }
            }
        } catch (Exception e) {
            log.info("将被封存/正常的的OA部门push到map出错" + SDUtil.getExceptionDetail(e));
        }
    }
}






