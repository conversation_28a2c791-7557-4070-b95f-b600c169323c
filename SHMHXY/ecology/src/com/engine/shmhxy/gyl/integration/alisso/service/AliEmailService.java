package com.engine.shmhxy.gyl.integration.alisso.service;

import weaver.hrm.User;

import java.util.Map;

public interface AliEmailService {
    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> getssolink(Map<String, Object> params, User user);

    /**
     * @param params
     * @param user
     * @return
     */
    Map<String, Object> unreadlist(Map<String, Object> params, User user);
}
