package com.engine.shmhxy.gyl.integration.alisso.cmd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName UnreadListCmd.java
 * @Description 阿里邮箱，未读邮件列表
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/10/15
 */
public class UnreadListCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * ESB事件名-ali_文件夹列表
     */
    private static final String ESB_FOLDER_LIST = "ali_folder_list";
    /**
     * ESB事件名-ali_文件夹下邮箱列表
     */
    private static final String ESB_EMAIL_LIST = "ali_unread_list";
    /**
     * 错误信息
     */
    private String error;

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public UnreadListCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
        error = "";
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        log.info("---START---");
        log.info("params:" + params);
        result.put("unreadArray", null);
        try {
            String email = Util.null2String(user.getEmail());
            log.info("userid:" + user.getUID());
            log.info("userName:" + user.getUsername());
            log.info("email:" + email);
            if (!email.isEmpty()) {
                //调用ESB,查邮箱下的文件夹列表
                JSONObject esbParam = new JSONObject();
                esbParam.put("email", email);
                EsbEventResult er = EsbUtil.callEsbEvent(ESB_FOLDER_LIST, esbParam);
                if (er.isSuccess()) {
                    JSONObject jsonObject = er.getData();
                    if (jsonObject.containsKey("folders")) {
                        JSONArray folders = jsonObject.getJSONArray("folders");
                        if (folders != null && !folders.isEmpty()) {
                            //按照文件夹获取文件夹下所有的未读邮件
                            JSONArray unreadArray = getFolderEmails(folders);
                            result.put("unreadList", unreadArray);
                        } else {
                            error = "响应folders无数据";
                            log.error(error);
                        }
                    }
                } else {
                    error = er.getErroMsg();
                    log.error(error);
                }
            } else {
                error = "缺失email地址";
                log.error(error);
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        }
        result.put("error", error);
        result.put("status", error.isEmpty());
        return result;
    }

    /**
     * 获取未读邮件列表
     *
     * @param folders
     * @return
     */
    private JSONArray getFolderEmails(JSONArray folders) {
        String displayName, inBoxFolderId = "";
        JSONArray unreadArray = new JSONArray();
        JSONObject jo;
        //这里只获取收件箱的邮件
        for (int i = 0; i < folders.size(); i++) {
            jo = folders.getJSONObject(i);
            displayName = Util.null2String(jo.get("displayName"));
            if ("inbox".equals(displayName)) {
                //收件箱文件夹id
                inBoxFolderId = Util.null2String(jo.get("id"));
                break;
            }
        }
        if (!inBoxFolderId.isEmpty()) {
            //根据文件夹id 递归获取该文件夹的邮件列表
            getEmailListByFolder(inBoxFolderId, "", unreadArray);
        } else {
            error = "未找到收件箱文件夹id";
            log.error("未找到收件箱文件夹id");
        }
        return unreadArray;
    }

    /**
     * 根据文件夹id获取未读邮件列表
     *
     * @param folderId
     * @param cursor
     * @param unreadArray
     */
    private void getEmailListByFolder(String folderId, String cursor, JSONArray unreadArray) {
        JSONObject jo;
        try {
            JSONObject esbParam = new JSONObject();
            esbParam.put("email", user.getEmail());
            esbParam.put("folderId", folderId);
            esbParam.put("cursor", cursor);
            EsbEventResult er = EsbUtil.callEsbEvent(ESB_EMAIL_LIST, esbParam);
            if (er.isSuccess()) {
                JSONObject responseObject = er.getData();
                if (responseObject.containsKey("messages")) {
                    JSONArray messages = responseObject.getJSONArray("messages");
                    if (messages != null && !messages.isEmpty()) {
                        for (int i = 0; i < messages.size(); i++) {
                            jo = messages.getJSONObject(i);
                            String isRead = Util.null2String(jo.get("isRead"));
                            if (!"true".equals(isRead)) {
                                unreadArray.add(jo);
                            }
                        }
                    }
                }

                if (responseObject.containsKey("nextCursor") && responseObject.containsKey("hasMore")) {
                    String nextCursor = Util.null2String(responseObject.get("nextCursor"));
                    String hasMore = Util.null2String(responseObject.get("hasMore"));
                    //需要重复调用getEmailListByFolder
                    // 如果 hasMore 为 true，继续递归调用本方法
                    if ("true".equals(hasMore) && !nextCursor.isEmpty()) {
                        getEmailListByFolder(folderId, nextCursor, unreadArray);
                    }
                }
            } else {
                error = "ESB_EMAIL_LIST出错:" + er.getErroMsg();
                log.error(error);
            }
        } catch (Exception e) {
            error = "ESB_EMAIL_LIST异常" + SDUtil.getExceptionDetail(e);
            log.error(error);
        }
    }
}
