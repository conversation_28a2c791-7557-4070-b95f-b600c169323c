package com.engine.shmhxy.gyl.integration.alisso.web;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.util.ServiceUtil;
import com.engine.parent.common.util.ApiUtil;
import com.engine.shmhxy.gyl.integration.alisso.service.AliEmailService;
import com.engine.shmhxy.gyl.integration.alisso.service.impl.AliEmailServiceImpl;
import weaver.hrm.HrmUserVarify;
import weaver.hrm.User;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Context;
import javax.ws.rs.core.MediaType;
import java.util.Map;

public class AliEmailWeb {
    private AliEmailService getService(User user) {
        return ServiceUtil.getService(AliEmailServiceImpl.class, user);
    }

    /**
     * 获取阿里邮箱单点登录地址
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/getssolink")
    @Produces(MediaType.TEXT_PLAIN)
    public String getssolink(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).getssolink(params, user)
        );
    }

    /**
     * 获取阿里邮箱未读邮件列表
     *
     * @param request
     * @param response
     * @return
     */
    @POST
    @Path("/unreadlist")
    @Produces(MediaType.TEXT_PLAIN)
    public String unreadlist(@Context HttpServletRequest request, @Context HttpServletResponse response) {
        User user = HrmUserVarify.getUser(request, response);
        Map<String, Object> params = ApiUtil.request2Map(request);
        return JSONObject.toJSONString(
                getService(user).unreadlist(params, user)
        );
    }
}
