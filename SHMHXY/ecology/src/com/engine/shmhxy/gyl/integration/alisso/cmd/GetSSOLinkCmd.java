package com.engine.shmhxy.gyl.integration.alisso.cmd;

import com.alibaba.fastjson.JSONObject;
import com.engine.common.biz.AbstractCommonCommand;
import com.engine.common.entity.BizLogContext;
import com.engine.core.interceptor.CommandContext;
import com.engine.parent.common.util.SDUtil;
import com.engine.parent.esb.EsbUtil;
import com.engine.parent.esb.dto.EsbEventResult;
import com.engine.parent.log.Logger;
import com.engine.parent.log.LoggerFactory;
import weaver.general.Util;
import weaver.hrm.User;

import java.util.HashMap;
import java.util.Map;

/**
 * @FileName SaveColorCmd.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/19
 */
public class GetSSOLinkCmd extends AbstractCommonCommand<Map<String, Object>> {
    /**
     * 二开log类
     */
    private final Logger log = LoggerFactory.getLogger(this.getClass());

    /**
     * ESB事件名
     */
    private static final String ESB_NAME = "ali_ssolink";

    @Override
    public BizLogContext getLogContext() {
        return null;
    }


    public GetSSOLinkCmd(Map<String, Object> params, User user) {
        this.params = params;
        this.user = user;
    }

    @Override
    public Map<String, Object> execute(CommandContext commandContext) {
        Map<String, Object> result = new HashMap<>();
        String error = "", link = "";
        log.info("---START---");
        log.info("params:" + params);
        try {
            String email = Util.null2String(user.getEmail());
            String mailId = Util.null2String(params.get("mailId"));//具体某个邮件的id，传入该参数，则返回打开该邮件的链接地址
            log.info("userid:" + user.getUID());
            log.info("userName:" + user.getUsername());
            log.info("email:" + email);
            if (!email.isEmpty()) {
                //调用ESB
                JSONObject esbParam = new JSONObject();
                esbParam.put("email", email);
                if (!mailId.isEmpty()) {
                    esbParam.put("mailId", mailId);
                }
                EsbEventResult er = EsbUtil.callEsbEvent(ESB_NAME, esbParam);
                if (er.isSuccess()) {
                    JSONObject jsonObject = er.getData();
                    if (jsonObject.containsKey("link")) {
                        link = Util.null2String(jsonObject.get("link"));
                    }
                } else {
                    error = er.getErroMsg();
                }
            } else {
                error = "缺失email地址";
            }
        } catch (Exception e) {
            error = "异常：" + SDUtil.getExceptionDetail(e);
            log.error("异常", e);
        }
        result.put("error", error);
        result.put("link", link);
        result.put("status", error.isEmpty());
        return result;
    }
}
