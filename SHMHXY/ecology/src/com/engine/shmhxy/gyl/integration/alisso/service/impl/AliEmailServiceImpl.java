package com.engine.shmhxy.gyl.integration.alisso.service.impl;

import com.engine.core.impl.Service;
import com.engine.shmhxy.gyl.integration.alisso.cmd.GetSSOLinkCmd;
import com.engine.shmhxy.gyl.integration.alisso.cmd.UnreadListCmd;
import com.engine.shmhxy.gyl.integration.alisso.service.AliEmailService;
import weaver.hrm.User;

import java.util.Map;

/**
 * @FileName AliEmailServiceImpl.java
 * @Description TODO
 * <AUTHOR>
 * @Version v1.00
 * @Date 2024/5/20
 */
public class AliEmailServiceImpl extends Service implements AliEmailService {
    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> getssolink(Map<String, Object> params, User user) {
        return commandExecutor.execute(new GetSSOLinkCmd(params, user));
    }

    /**
     * @param params
     * @param user
     * @return
     */
    @Override
    public Map<String, Object> unreadlist(Map<String, Object> params, User user) {
        return commandExecutor.execute(new UnreadListCmd(params, user));
    }
}
